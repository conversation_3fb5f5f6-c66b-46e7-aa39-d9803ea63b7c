import Visa from './app/assets/svg/visa';
// import Chat from './app/assets/svg/chat';
import countries from 'world-countries';
import help from './app/assets/svg/help';
import logout from './app/assets/svg/logout';
import Canada from './app/assets/svg/canada';
import Payment from './app/assets/svg/payment';
import Courses from './app/assets/svg/courses';
import Payments from './app/assets/svg/payments';
import GoldStar from './app/assets/svg/GoldStar';
import Feedback from './app/assets/svg/feedback';
import Settings from './app/assets/svg/Settings';
import Harvard from './app/assets/img/Harvard.png';
import Finland from './app/assets/img/Finland.png';
import dashboard from './app/assets/svg/dashboard';
import Australia from './app/assets/svg/Australia';
import { ChartConfig } from '@/components/ui/chart';
import Enrollee from './app/assets/img/enrollee.png';  
import LinkedinIcon from './app/assets/svg/linkedin';
import BronzeStar from './app/assets/svg/BronzeStar';
import SilverStar from './app/assets/svg/SilverStar';
import Application from './app/assets/svg/application';
import UserProfile from './app/assets/svg/userProfile';
import StarterStar from './app/assets/svg/StarterStar';
import PlatinumStar from './app/assets/svg/PlatinumStar';
import Applications from './app/assets/svg/applications';
import Universities from './app/assets/svg/universities';
import RpSignupCover from './app/assets/svg/RpSignupCover';
import AccessPermission from './app/assets/svg/AccessPermission';
import CaltechNniLogo from './app/assets/svg/caltech-uni-logo.svg';
import HarvardUniLogo from './app/assets/svg/harvard-uni-logo.svg';
import CostofLivingIcon from './app/assets/svg/cost-of-living-icon';
import AvgTutionFeeIcon from './app/assets/svg/avg-tution-fee-icon';
import StudentSignUpCover from './app/assets/svg/StudentSignUpCover';
import MichiganUniLogo from './app/assets/svg/michigan-uni-logo.svg';
import ApplicationFeeIcon from './app/assets/svg/application-fee-icon';
import { TableRowProps, UniversityDataProps, TabProps } from '@/types';
import AvgGradProgramIcon from './app/assets/svg/avg-grad-program-icon';
import EmailIconimage from './app/assets/svg/progress-record-message.svg';
import UniversitySignupCover from './app/assets/svg/UniversitySignupCover';
import StatCardVisitorIcon from './app/assets/svg/stat-card-visitor-icon.svg';
import StatCardStudentIcon from './app/assets/svg/stat-card-student-icon.svg';
import StatCardAcceptedIcon from './app/assets/svg/stat-card-accepted-icon.svg';
import StatCardRejectedIcon from './app/assets/svg/stat-card-rejected-icon.svg';
import UniversityImageOne from '@/app/assets/img/university-profile-image-1.png';
import UniversityImageTwo from '@/app/assets/img/university-profile-image-2.png';
import UniversityImageSix from '@/app/assets/img/university-profile-image-6.jpg';
import UniversityImageFour from '@/app/assets/img/university-profile-image-4.png';
import UniversityImageFive from '@/app/assets/img/university-profile-image-5.jpg';
import StatCardApplicationIcon from './app/assets/svg/stat-card-application-icon.svg';
import EnrolledComponentBannerImage from './app/assets/img/enrolled-component-banner-image.jpg'; 


export const signupTabLists = [
    { value: 'Student', label: 'Student' },
    { value: 'RecruitmentPartner', label: 'Recruitment Partner' }
];

export const signupTabDescriptions = {
    Student: 'Every signup is a step closer to your goals - stay driven, stay focused, and make it count!',
    RecruitmentPartner: 'Empower others, inspire change sign up as a recruitment partner and make a lasting impact today!',
    Institution: 'Unlock your potential and shape your future - this is where your journey begins!',
};

export const signupTabImages = {
    Student: StudentSignUpCover,
    RecruitmentPartner: RpSignupCover,
    Institution: UniversitySignupCover,
};

export const countryOptions = countries.map((country) => ({
    label: country.name.common,
    value: country.cca2,
}));

const pdfFileUrl = 'https://pdfobject.com/pdf/sample.pdf';

export const courses = [
    {
        id: 1,
        university: 'Massachusetts Institute of Tech.',
        universityLogo: Harvard,
        program: 'Master of Cloud Computing',
        slug: 'master-of-cloud-computing',
        tuitionFees: [37823, 50369],
        courseRank: 123,
        acceptanceRate: '87%',
        programType: '2 year Master\'s Degree',
        intake: 'Sept 2025',
        country: 'USA',
        scholarship: '50% waiver',
        countryLogo: Finland
    },
    {
        id: 2,
        university: 'University of Michigan',
        universityLogo: Harvard,
        program: 'Master of Applied Data Science',
        slug: 'master-of-applied-data-science',
        tuitionFees: [37823, 50369],
        courseRank: 123,
        acceptanceRate: '87%',
        programType: '2 year Master\'s Degree',
        intake: 'Feb 2025',
        country: 'USA',
        scholarship: '30% waiver',
        countryLogo: Finland,
    },
    {
        id: 3,
        university: 'Howard University, Washington DC',
        universityLogo: Harvard,
        program: 'Master of Science in Data Science & AI',
        slug: 'master-of-science-in-data-science-and-ai',
        tuitionFees: [37823, 50369],
        courseRank: 123,
        acceptanceRate: '87%',
        programType: '2 year Master\'s Degree',
        intake: 'Feb 2025',
        country: 'USA',
        scholarship: '30% waiver',
        countryLogo: Finland
    },
    {
        id: 4,
        university: 'University of Central Missouri',
        universityLogo: Harvard,
        program: 'Master of Science in Data Science & AI',
        slug: 'master-of-science-in-data-science-and-ai',
        tuitionFees: [37823, 50369],
        courseRank: 123,
        acceptanceRate: '87%',
        programType: '2 year Master\'s Degree',
        intake: 'Feb 2025',
        country: 'USA',
        scholarship: '30% waiver',
        countryLogo: Finland
    },
    {
        id: 5,
        university: 'University of Central Missouri',
        universityLogo: Harvard,
        program: 'MBA in Business Analytics',
        slug: 'mba-in-business-analytics',
        tuitionFees: [37823, 50369],
        courseRank: 123,
        acceptanceRate: '87%',
        programType: '2 year Master\'s Degree',
        intake: 'Feb 2025',
        country: 'USA',
        scholarship: '30% waiver',
        countryLogo: Finland
    },
];

export const notifications = [
    {
        title: 'Pop-up Notifications',
        description: 'Receive real-time pop-up alerts directly on your profile for important updates.'
    },
    {
        title: 'University Notifications',
        description: 'Stay informed about all university-related announcements and events.'
    },
    {
        title: 'Application Status Updates',
        description: 'Track your application progress with instant status notifications.'
    },
    {
        title: 'Payment Alerts',
        description: 'Receive notifications for upcoming payments, due dates, and transaction updates.'
    },
    {
        title: 'Notes Notifications',
        description: 'Get notified when new notes or important changes are made to your saved content.'
    },
    {
        title: 'Assignment Alerts',
        description: 'Receive reminders for upcoming assignments, submissions, and due dates.'
    },
    {
        title: 'Task Reminders',
        description: 'Stay on top of your to-do list with timely task reminder notifications.'
    },
    {
        title: '@Mention Alerts',
        description: `Get instant notifications when you're mentioned or tagged in discussions or tasks.`
    }
]

export const messages = [
    {
        dept: 'Applications',
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        dept: 'Applications',
        sender: 'other',
        text: 'perfect!',
        time: '12m',
    },
    {
        dept: 'Applications',
        sender: 'other',
        text: 'Wow, this is really epic',
        time: '12m',
    },
    {
        sender: 'me',
        text: 'How are you?',
        time: '24m',
    },
    {
        sender: 'me',
        text: 'woohoooo',
        time: '24m',
    },
    {
        dept: 'Applications',
        sender: 'me',
        text: 'Haha oh man',
        time: '24m',
    },
    {
        dept: 'Applications',
        sender: 'me',
        text: 'Haha that’s terrifying',
        time: '24m',
    },
    {
        dept: 'Applications',
        sender: 'me',
        text: 'Haha that’s terrifying',
        time: '24m',
    },
    {
        sender: 'me',
        text: 'Haha that’s terrifying',
        time: '24m',
    },
    {
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        sender: 'me',
        text: 'Haha oh man',
        time: '24m',
    },
    {
        sender: 'me',
        text: 'Haha oh man',
        time: '24m',
    },
    {
        sender: 'me',
        text: 'Haha oh man',
        time: '24m',
    },
    {
        sender: 'me',
        text: 'Haha oh man',
        time: '24m',
    },
    {
        dept: 'Payments',
        sender: 'other',
        text: 'omg, this is amazing',
        time: '12m',
    },
    {
        dept: 'Payments',
        sender: 'other',
        text: 'perfect!',
        time: '12m',
    },
    {
        dept: 'Payments',
        sender: 'other',
        text: 'Wow, this is really epic',
        time: '12m',
    },
    {
        dept: 'Payments',
        sender: 'me',
        text: 'How are you?',
        time: '24m',
    },
    {
        dept: 'Payments',
        sender: 'me',
        text: 'woohoooo',
        time: '24m',
    },
];

export const chatDept = [
    {
        icon: Application ,
        dept: 'Applications',
        message: 'Thank you'
    },
    {
        unreadMsg: true,
        unreadMsgCount: 4,
        icon: Visa,
        dept: 'VISA',
        message: 'Thank you'
    },
    {
        icon: Payment,
        dept: 'Payments',
        message: 'Thank you'
    },
    {
        unreadMsg: true,
        unreadMsgCount: 4,
        icon: Feedback,
        dept: 'Feedback',
        message: 'How are you?'
    }
]

export const VerticalSteps = [
    {   
        date: "Sep 25, 2024",
        title: "Updated Final Offer Letter",
        status: "completed",
        update: 'This is a Update Final Offer Letter, and this document can be used to apply for your Visa :',
        content: "Step 1: This step is upcoming. longe lore Please ensure you complete each step as outlined of your final offer letter as soon as possible in order to secure your seat. Please review all details regardin",
        link: "https://www.nopcommerce.com/en/demo-link",
        linkText: "Attestation_letter.pdf",
        file: 'Attestation_letter.pdf',
        iconUrl: EmailIconimage
    },
    {
        date: "Sep 25, 2024",
        title: "Provincial Attestation Letter (PAL)",
        status: "completed",
        content: "Step 2: This step is also completed.",
    },
    {
        date: "Sep 25, 2024",
        title: "Provincial Attestation Letter (PAL)",
        status: "active",
        content: "Step 3: This is the current active step.",
    },
    {
        date: "Sep 25, 2024",
        title: "Provincial Attestation Letter (PAL)",
        status: "regular",
        content: "Step 4: This step is upcoming. longe lore Please ensure you complete each step as outlined of your final offer letter as soon as possible in order to secure your seat. Please review all details regardin",
    },
    {
        date: "Sep 25, 2024",
        title: "Provincial Attestation Letter (PAL)",
        status: "empty",
        content: "Step 5: This step is optional or skipped. ",
    },
];

export const tabs: TabProps[] = [
    { id: 1, label: 'Personal' },
    { id: 2, label: 'Education' },
    { id: 3, label: 'Application' },
    { id: 4, label: 'Payment' },
    { id: 5, label: 'Documents' }
];

export const partnerBenefitsTabs: TabProps[] = [
    { id: 1, label: 'Starter' },
    { id: 2, label: 'Bronze' },
    { id: 3, label: 'Silver' },
    { id: 4, label: 'Gold' },
    { id: 5, label: 'Premium' }
];

export const navItems = [
    { icon: dashboard, label: 'Dashboard', href: '/' },
    { icon: UserProfile, label: 'User Profile', href: '/profile' },
    { icon: Applications, label: 'Applications', href: '/all-applications' },
    { icon: Courses, label: 'Courses', href: '/course-finder' },
    { icon: Universities, label: 'Universities', href: '/universities' },
    { icon: Payments, label: 'Payments', href: '/payments' },
    // { icon: Chat, label: 'Chat', href: '/chat' },
    { icon: AccessPermission, label: 'Access Permissions', href: '/access-permissions' },
    { icon: Settings, label: 'Settings', href: '/settings' },
    { icon: help, label: 'Help', href: '/help' },
    { icon: logout, label: 'Logout', href: '/logout' }
];

export const courseData: UniversityDataProps[] = [
    {
        university: 'Westcliff University - Irvine',
        location: 'Tempe, Arizona, US',
        program: 'Associate of Science',
        programLevel: '2-Year Undergraduate Diploma',
        applicationFee: '$135 CAD',
        tuitionFee: '$32325.70 CAD / Year',
        intakes: 'Jan, May, Sep',
        gpa: '2.50',
        ielts: 'R7.5 W8.0 L7.5 S7.5 Overall7.5',
        duolingo: '770',
        gre: 'R7.5 W8.0 L7.5 S7.5 Overall7.5',
        pte: '140',
        toefl: 'R7.5 W8.0 L7.5 S7.5 Overall7.5'
    },
    {
        university: 'University of Massachusetts Dartmouth',
        location: 'Tempe, Arizona, US',
        program: 'Bachelor of Business Administration - Project Management',
        programLevel: `4-Year Bachelor's Degree`,
        applicationFee: '$0.00 CAD',
        tuitionFee: '$15000.0 CAD / Year',
        intakes: 'Jan',
        gpa: '2.89',
        ielts: 'R7.5 W8.0 L7.5 S7.5 Overall7.5',
        duolingo: '770',
        gre: 'R7.5 W8.0 L7.5 S7.5 Overall7.5',
        pte: '125',
        toefl: 'R7.5 W8.0 L7.5 S7.5 Overall7.5'
    },
    {
        university: 'University of Massachusetts Dartmouth',
        location: 'Tempe, Arizona, US',
        program: 'Bachelor of Business Administration - Project Management',
        programLevel: `4-Year Bachelor's Degree`,
        applicationFee: '$0.00 CAD',
        tuitionFee: '$15000.0 CAD / Year',
        intakes: 'Jan',
        gpa: '2.89',
        ielts: 'R7.5 W8.0 L7.5 S7.5 Overall7.5',
        duolingo: '770',
        gre: 'R7.5 W8.0 L7.5 S7.5 Overall7.5',
        pte: '125',
        toefl: 'R7.5 W8.0 L7.5 S7.5 Overall7.5'
    }
];

export const courseTableRows: TableRowProps[] = [
    { label: 'Program', key: 'program' },
    { label: 'Program Level', key: 'programLevel' },
    { label: 'Application Fee', key: 'applicationFee' },
    { label: 'Tuition Fee', key: 'tuitionFee' },
    { label: 'Intakes and Submission', key: 'intakes' },
    { label: 'GPA / CGPA / Division', key: 'gpa' },
    { label: 'IELTS', key: 'ielts' },
    { label: 'DUOLINGO', key: 'duolingo' },
    { label: 'GRE / GMAT', key: 'gre' },
    { label: 'PTE', key: 'pte' },
    { label: 'TOEFL', key: 'toefl' }
];

// export const countries = [
//     {value: 'usa', label: 'USA'},
//     {value: 'canada', label: 'CANADA'},
//     {value: 'finland', label: 'Finland'},
//     {value: 'australia', label: 'Australia'},
// ];

export const programs  = [
    { value: 'bba', label: 'BBA' },
    { value: 'cse', label: 'CSE' },
    { value: 'mba', label: 'MBA' },
    { value: 'eee', label: 'EEE' }
];

// export const sortByOptions  = [
//     { value: 'full_free_scholarship', label: 'Full Free Scholarship' },
//     { value: '50', label: '50% waiver' },
//     { value: '30', label: '30% waiver' },
//     { value: 'free_application', label: 'Free Application' }
// ];

export const intakes  = [
    { value: 'Oct 2024', label: 'Oct 2024' },
    { value: 'Nov 2024', label: 'Nov 2024' },
    { value: 'Dec 2024', label: 'Dec 2024' },
    { value: 'Jan 2025', label: 'Jan 2025' },
    { value: 'Feb 2025', label: 'Feb 2025' },
    { value: 'March 2025', label: 'March 2025' },
    { value: 'April 2025', label: 'April 2025' },
    { value: 'May 2025', label: 'May 2025' },
    { value: 'June 2025', label: 'June 2025' },
    { value: 'July 2025', label: 'July 2025' }
];

export const subjects = [
    { value: 'cse', label: 'CSE' },
    { value: 'eee', label: 'EEE' },
    { value: 'mba', label: 'MBA' },
    { value: 'softwareEngineering', label: 'Software Engineering' }
];

export const gender = [
    { id: 'male', label: 'Male' },
    { id: 'female', label: 'Female' },
    { id: 'others', label: 'Others' }
];

export const maritalStatus = [
    { value: 'single', label: 'Single' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' }
];

export const frameworks = [
    { value: 'next.js', label: 'Next.js'},
    { value: 'sveltekit', label: 'SvelteKit'},
    { value: 'nuxt.js', label: 'Nuxt.js'},
    { value: 'remix', label: 'Remix'},
    { value: 'astro', label: 'Astro'},
    { value: 'laravel', label: 'laravel'}
];

export const personalInfo = [
    { label: 'Given Name', value: 'Faysal' },
    { label: 'Sur Name', value: 'Ahmed' },
    { label: 'Date Of Birth', value: 'Dec 11 1997' },
    { label: 'NID', value: '03243628364' },
    { label: 'Gender', value: 'Male' },
    { label: 'Email', value: '<EMAIL>' },
    { label: 'Name In Native Language', value: 'ফয়সাল আহমেদ' },
    { label: 'Number', value: '+880 18752-363132' },
    { label: 'Present Address', value: 'South, Kolabagan, Dhaka.' },
    { label: 'Father Name', value: 'Kabir Ahmed' },
    { label: 'Permanent Address', value: '85/Gh/22, Panthapath, Dhaka.' },
    { label: 'Mother Name', value: 'Sakila Ahmed' },
    { label: 'Marital Status', value: 'Married' },
    { label: 'Guardian Number', value: '+880 1552-358913' },
    { label: 'Spouse Name', value: 'Carrey Lane' },
    { label: 'Spouse Passport Number', value: 'NE584954' },
];

export const socialLinks = [
    { name: 'Facebook', icon: LinkedinIcon, username: '@faysalahmed', link: 'https://facebook.com/faysalahmed' },
    { name: 'LinkedIn', icon: LinkedinIcon, username: '@faysalahmed', link: 'https://linkedin.com/in/faysalahmed' },
    { name: 'Twitter', icon: LinkedinIcon, username: '@faysalahmed', link: 'https://twitter.com/faysalahmed' },
    { name: 'Instagram', icon: LinkedinIcon, username: '@faysalahmed', link: 'https://instagram.com/faysalahmed' },
    { name: 'YouTube', icon: LinkedinIcon, username: '@faysalahmed', link: 'https://youtube.com/@faysalahmed' },
];

export const socialPlatform = [
    { value: 'facebook', label: 'Facebook' },
    { value: 'linkedin', label: 'Linkedin' },
    { value: 'x', label: 'X' },
    { value: 'instagram', label: 'Instagram' },
    { value: 'youtube', label: 'Youtube' },
    { value: 'google_scholar', label: 'Google Scholar' },
    { value: 'research_gate', label: 'Research Gate' },
    { value: 'others', label: 'Others' },
];

export const Reference = [
    { value: 'website', label: 'Website' },
    { value: 'facebook', label: 'Facebook' },
    { value: 'linkedin', label: 'Linkedin' },
    { value: 'instagram', label: 'Instagram' }
];

export const webinar = [
    {
        bannerImage: EnrolledComponentBannerImage,
        title: '"Fast-Track Your Student Visa: Join Our Free Webinar!"',
        enrollee: [Enrollee,Enrollee,Enrollee,Enrollee,Enrollee,],
        totalEnrollee: 10,
        date: 'Dec 25, 2024',
        startTime: '18:00 P.M.',
        endTime: '19.30 P.M.',
    }
];

export const events = [
    {
        day: 'Mon',
        date: '10',
        title: 'Application Deadline',
        time: '1:00 pm - 2:30 pm',
    },
    {
        day: 'Fri',
        date: '14',
        title: 'Interview Date',
        time: '9:00 am - 11:30 am',
    },
    {
        day: 'Sun',
        date: '16',
        title: 'Meeting with the Agent',
        time: '9:00 am - 11:30 am',
    },
    {
        day: 'Sun',
        date: '16',
        title: 'Meeting with the Agent',
        time: '9:00 am - 11:30 am',
    },
];

export const applications = [
    {
        id: 30987,
        program: 'Master of Business Administration',
        university: 'University of Birmingham',
        location: 'Birmingham, England',
        dateCreated: '21 Dec 2024',
        status: 'New',
        logo: HarvardUniLogo, 
    },
    {
        id: 30834,
        program: 'Master of Science in Data Science & AI',
        university: 'Arizona State University',
        location: 'Tempe, Arizona, US',
        dateCreated: '19 Oct 2024',
        status: 'In Progress',
        logo: HarvardUniLogo, 
    },
    {
        id: 30124,
        program: 'Master of Communication Systems and Networks',
        university: 'Tampere University',
        location: 'Tampere, Finland',
        dateCreated: '21 Sept 2024',
        status: 'Awaiting Documents',
        logo: HarvardUniLogo, 
    },
    {
        id: 30445,
        program: 'Master of Computer Science & Engineering',
        university: 'Adelaide University',
        location: 'Adelaide, Australia',
        dateCreated: '20 Jun 2024',
        status: 'Submitted',
        logo: HarvardUniLogo, 
    },
    {
        id: 30342,
        program: 'Master of Information and Communications Technology',
        university: 'Deakin University',
        location: 'Deakin, Australia',
        dateCreated: '21 Feb 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
    {
        id: 30988,
        program: 'Master of Business Analytics',
        university: 'University of Sydney',
        location: 'Sydney, Australia',
        dateCreated: '15 Nov 2024',
        status: 'Deferred',
        logo: HarvardUniLogo, 
    },
    {
        id: 30989,
        program: 'Master of Software Engineering',
        university: 'Carnegie Mellon University',
        location: 'Pittsburgh, US',
        dateCreated: '10 Dec 2024',
        status: 'Rejected',
        logo: HarvardUniLogo, 
    },
    {
        id: 30990,
        program: 'Master of Artificial Intelligence',
        university: 'University of Amsterdam',
        location: 'Amsterdam, Netherlands',
        dateCreated: '5 Sept 2024',
        status: 'Withdrawn',
        logo: HarvardUniLogo, 
    },
    {
        id: 30991,
        program: 'Master of Public Health',
        university: 'Harvard University',
        location: 'Cambridge, US',
        dateCreated: '25 Oct 2024',
        status: 'Enrolled',
        logo: HarvardUniLogo, 
    },
    {
        id: 30992,
        program: 'Master of Data Analytics',
        university: 'University of Toronto',
        location: 'Toronto, Canada',
        dateCreated: '1 Aug 2024',
        status: 'Completed',
        logo: HarvardUniLogo, 
    },
    {
        id: 30993,
        program: 'Master of Environmental Science',
        university: 'ETH Zurich',
        location: 'Zurich, Switzerland',
        dateCreated: '30 May 2024',
        status: 'Conditional Offer',
        logo: HarvardUniLogo, 
    },
    {
        id: 30994,
        program: 'Master of Machine Learning',
        university: 'University College London',
        location: 'London, England',
        dateCreated: '10 Oct 2024',
        status: 'Payment Pending',
        logo: HarvardUniLogo, 
    },
    {
        id: 30995,
        program: 'Master of Cloud Computing',
        university: 'University of Melbourne',
        location: 'Melbourne, Australia',
        dateCreated: '18 Dec 2024',
        status: 'Transferred',
        logo: HarvardUniLogo, 
    },
    {
        id: 30996,
        program: 'Master of Business Analytics',
        university: 'MIT',
        location: 'Cambridge, US',
        dateCreated: '12 Jul 2024',
        status: 'Collecting Documents',
        logo: HarvardUniLogo, 
    },
    {
        id: 30997,
        program: 'Master of Electrical Engineering',
        university: 'National University of Singapore',
        location: 'Singapore',
        dateCreated: '23 Jun 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
    {
        id: 30998,
        program: 'Master of Biomedical Engineering',
        university: 'University of California, Berkeley',
        location: 'Berkeley, US',
        dateCreated: '15 Apr 2024',
        status: 'CAL Issued',
        logo: HarvardUniLogo, 
    },
    {
        id: 30999,
        program: 'Master of Cybersecurity',
        university: 'Stanford University',
        location: 'Stanford, US',
        dateCreated: '3 Jan 2024',
        status: 'Applied',
        logo: HarvardUniLogo, 
    },
    {
        id: 31000,
        program: 'Master of Psychology',
        university: 'University of Oxford',
        location: 'Oxford, England',
        dateCreated: '29 Feb 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
    {
        id: 31001,
        program: 'Master of Mechanical Engineering',
        university: 'Technical University of Munich',
        location: 'Munich, Germany',
        dateCreated: '7 Mar 2024',
        status: 'Collecting Documents',
        logo: HarvardUniLogo, 
    },
    {
        id: 31002,
        program: 'Master of Artificial Intelligence',
        university: 'University of Tokyo',
        location: 'Tokyo, Japan',
        dateCreated: '1 Feb 2024',
        status: 'CAL Issued',
        logo: HarvardUniLogo, 
    },
    {
        id: 31003,
        program: 'Master of Industrial Design',
        university: 'Royal College of Art',
        location: 'London, England',
        dateCreated: '22 Apr 2024',
        status: 'I20 Issued',
        logo: HarvardUniLogo, 
    },
    {
        id: 31004,
        program: 'Master of Marketing',
        university: 'University of Chicago',
        location: 'Chicago, US',
        dateCreated: '9 Mar 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
    {
        id: 31005,
        program: 'Master of Finance',
        university: 'New York University',
        location: 'New York, US',
        dateCreated: '12 Jun 2024',
        status: 'Collecting Documents',
        logo: HarvardUniLogo, 
    },
    {
        id: 31006,
        program: 'Master of International Relations',
        university: 'University of Geneva',
        location: 'Geneva, Switzerland',
        dateCreated: '10 May 2024',
        status: 'CAL Issued',
        logo: HarvardUniLogo, 
    },
    {
        id: 31007,
        program: 'Master of Chemical Engineering',
        university: 'Imperial College London',
        location: 'London, England',
        dateCreated: '3 Jul 2024',
        status: 'Applied',
        logo: HarvardUniLogo, 
    },
    {
        id: 31008,
        program: 'Master of Biotechnology',
        university: 'University of Queensland',
        location: 'Brisbane, Australia',
        dateCreated: '19 Sept 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
    {
        id: 31009,
        program: 'Master of Civil Engineering',
        university: 'University of Alberta',
        location: 'Edmonton, Canada',
        dateCreated: '15 Nov 2024',
        status: 'Applied',
        logo: HarvardUniLogo, 
    },
    {
        id: 31010,
        program: 'Master of Economics',
        university: 'University of Cambridge',
        location: 'Cambridge, England',
        dateCreated: '8 Dec 2024',
        status: 'I20 Issued',
        logo: HarvardUniLogo, 
    },
    {
        id: 31011,
        program: 'Master of Public Administration',
        university: 'Princeton University',
        location: 'Princeton, US',
        dateCreated: '5 Nov 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
];

export const meetings = [
    {
        date: { day: "10", month: "Oct" },
        title: "Organizational Meeting",
        description:
            "A meeting to discuss key organizational matters, including setting goals, assigning roles, and more.",
        time: "10:30 am - 12:00 pm",
    },
    {
        date: { day: "22", month: "Oct" },
        title: "Training Session",
        description:
            "A focused session designed to develop skills and knowledge in a specific area.",
        time: "10:30 am - 12:00 pm",
    },
    {
        date: { day: "07", month: "Nov" },
        title: "Meeting with HR",
        description:
            "A discussion with HR Admin to address personnel matters, policies, benefits, and more.",
        time: "10:30 am - 12:00 pm",
    },
    {
        date: { day: "23", month: "Dec" },
        title: "Meeting with Team",
        description:
            "A team gathering to discuss project updates, share progress, address challenges, and align on goals.",
        time: "10:30 am - 12:00 pm",
    },
    {
        date: { day: "12", month: "Jan" },
        title: "Meeting with Admin",
        description:
            "A meeting to discuss administrative tasks, policies, operations, or resource management.",
        time: "10:30 am - 12:00 pm",
    },
];

export const prefferedCountry = [
    { value: 'australia', label: 'Australia' },
    { value: 'belgium', label: 'Belgium' },
    { value: 'canada', label: 'Canada' },
    { value: 'china', label: 'China' },
    { value: 'czech_republic', label: 'Czech Republic' },
    { value: 'denmark', label: 'Denmark' },
    { value: 'dubai', label: 'Dubai' },
    { value: 'germany', label: 'Germany' },
    { value: 'italy', label: 'Italy' },
    { value: 'hungary', label: 'Hungary' },
    { value: 'malaysia', label: 'Malaysia' },
    { value: 'malta', label: 'Malta' },
    { value: 'south_korea', label: 'South Korea' },
    { value: 'turkey', label: 'Turkey' },
    { value: 'uk', label: 'UK' },
    { value: 'usa', label: 'USA' }
];

export const universityList = [
    {
        id:1,
        universityName: "Massachusetts Institute of Technology (MIT)",
        slug: 'massachusetts-institute-of-technology-mit',
        universityLogo: Harvard,
        location: "Cambridge, AL",
        country: { label: 'Canada', logo: Canada },
        province: "AL",
        city: "Cambridge",
        type: "Private",
        tuitionFees: "$1,200 - $4,276",
        rank: 1,
        courses: [
            {
                courseTitle: 'Master of Applied Data Science',
                slug: 'master-of-applied-data-science'
            }
        ]
    },
    {
        id:2,
        universityName: "Harvard University",
        slug: 'harvard-university',
        universityLogo: Harvard,
        location: "Philadelphia, AK",
        country: { label: 'United States', logo: Australia },
        province: "AK",
        city: "Philadelphia",
        type: "Public",
        tuitionFees: "$10,200 - $24,276",
        rank: 4,
    },
    {
        id:3,
        universityName: "California Institute of Technology (Caltech)",
        slug: 'california-institute-of-technology-caltech',
        universityLogo: Harvard,
        location: "Pasadena, IN",
        country: { label: 'Canada', logo: Canada },
        province: "IN",
        city: "Pasadena",
        type: "Non-Profit",
        tuitionFees: "$122,200 - $123,276",
        rank: 16,
    },
    {
        id:4,
        universityName: "University of California, Berkeley (UCB)",
        slug: 'university-of-california-berkeley-ucb',
        universityLogo: Harvard,
        location: "Cambridge, AL",
        country: { label: 'Canada', logo:Canada },
        province: "AL",
        city: "Cambridge",
        type: "Private, Non-Profit",
        tuitionFees: "$1,200 - $4,276",
        rank: 6,
    }
];

export const types = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-Profit', label: 'Non-Profit' }
];

export const country = [
    { value: 'USA', label: 'USA' },
    { value: 'canada', label: 'Canada' },
    { value: 'finland', label: 'Finland' },
    { value: 'australia', label: 'Australia' }
];

export const province = [
    { value: 'alabama', label: 'Alabama'},
    { value: 'ohio', label: 'Ohio'},
    { value: 'washington', label: 'Washington'},
    { value: 'michigan', label: 'Michigan'},
    { value: 'north-carolina', label: 'North Carolina'}
];

export const city = [
    { value: 'Ada', label: 'Ada'},
    { value: 'Akron', label: 'Akron'},
    { value: 'cleveland', label: 'Cleveland'},
    { value: 'columbus', label: 'Columbus'},
    { value: 'dayton', label: 'Dayton'},
    { value: 'delaware', label: 'Delaware'}
];

export const sortByOptions  = [
    { value: 'rank', label: 'Rank' },
    { value: 'full_free_scholarship', label: 'Full Free Scholarship' },
    { value: '50', label: '50% waiver' },
    { value: '30', label: '30% waiver' },
    { value: 'free_application', label: 'Free Application' }
];

export const fees  = [
    { value: 'tuition_fee_low_to_high', label: 'Tuition Fee (Low to High)' },
    { value: 'tuition_fee_high_to_low', label: 'Tuition Fee (High to Low)' },
    { value: 'select_range', label: 'Select range' },
];

export const BarChartDataCounselor = {
    'This Year': [
        { month: 'January', application: 186, accepted: 80, rejected: 50 },
        { month: 'February', application: 305, accepted: 200, rejected: 120 },
        { month: 'March', application: 237, accepted: 120, rejected: 95 },
        { month: 'April', application: 73, accepted: 190, rejected: 60 },
        { month: 'May', application: 209, accepted: 130, rejected: 85 },
        { month: 'June', application: 214, accepted: 140, rejected: 100 },
        { month: 'July', application: 286, accepted: 50, rejected: 210 },
        { month: 'August', application: 305, accepted: 300, rejected: 250 },
        { month: 'September', application: 137, accepted: 220, rejected: 110 },
        { month: 'October', application: 43, accepted: 390, rejected: 70 },
        { month: 'November', application: 409, accepted: 330, rejected: 320 },
        { month: 'December', application: 214, accepted: 140, rejected: 410 },
    ],
    'This Month': [
        { day: 'Week 1', application: 14, accepted: 330, rejected: 100 },
        { day: 'Week 2', application: 300, accepted: 450, rejected: 180 },
        { day: 'Week 3', application: 430, accepted: 250, rejected: 210 },
        { day: 'Week 4', application: 100, accepted: 350, rejected: 120 },
    ],
    'This Week': [
        { day: 'Monday', application: 220, accepted: 30, rejected: 50 },
        { day: 'Tuesday', application: 600, accepted: 335, rejected: 150 },
        { day: 'Wednesday', application: 300, accepted: 40, rejected: 80 },
        { day: 'Thursday', application: 890, accepted: 225, rejected: 300 },
        { day: 'Friday', application: 90, accepted: 50, rejected: 30 },
        { day: 'Saturday', application: 200, accepted: 55, rejected: 90 },
        { day: 'Sunday', application: 110, accepted: 60, rejected: 40 },
    ],
    Today: [
        // { hour: '8 AM', application: 10, accepted: 5, rejected: 2 },
        // { hour: '10 AM', application: 15, accepted: 10, rejected: 4 },
        // { hour: '12 PM', application: 20, accepted: 15, rejected: 6 },
        // { hour: '2 PM', application: 25, accepted: 20, rejected: 8 },
        { hour: '4 PM', application: 30, accepted: 25, rejected: 10 },
    ],
};

export const UniversityProfileAccordionData = [
    {
        title: 'Bachelor of Business Administration - General',
        details: [
            { label: 'Nearest Intake & Deadline for Application', value: 'Summer (March 03, 2023)' },
            { label: 'Last Academic', value: 'Grade 12 / HSC / High School' },
            { label: 'Next Intakes', value: 'Spring, Winter, Fall' },
            { label: 'Minimum GPA', value: '60% or Above' },
            { label: 'Tuition Fee', value: '$11,465.00 / Year' },
            { label: 'English Proficiency', value: 'IELTS: 6.0 / TOEFL: 60 / PTE: 50 / DUOLINGO: 95' },
            { label: 'Course Duration', value: '2 Years' },
            { label: 'Publication', value: 'N/A' },
            { label: 'Lecture Language', value: 'English, Chinese' },
        ],
    },
    {
        title: 'Master of Business Administration - General',
        details: [
            { label: 'Nearest Intake & Deadline for Application', value: 'Summer (March 03, 2023)' },
            { label: 'Last Academic', value: 'Grade 12 / HSC / High School' },
            { label: 'Next Intakes', value: 'Spring, Winter, Fall' },
            { label: 'Minimum GPA', value: '60% or Above' },
            { label: 'Tuition Fee', value: '$11,465.00 / Year' },
            { label: 'English Proficiency', value: 'IELTS: 6.0 / TOEFL: 60 / PTE: 50 / DUOLINGO: 95' },
            { label: 'Course Duration', value: '2 Years' },
            { label: 'Publication', value: 'N/A' },
            { label: 'Lecture Language', value: 'English, Chinese' },
        ],
    },
    {
        title: 'Doctorate of Business Administration',
        details: [
            { label: 'Nearest Intake & Deadline for Application', value: 'Summer (March 03, 2023)' },
            { label: 'Last Academic', value: 'Grade 12 / HSC / High School' },
            { label: 'Next Intakes', value: 'Spring, Winter, Fall' },
            { label: 'Minimum GPA', value: '60% or Above' },
            { label: 'Tuition Fee', value: '$11,465.00 / Year' },
            { label: 'English Proficiency', value: 'IELTS: 6.0 / TOEFL: 60 / PTE: 50 / DUOLINGO: 95' },
            { label: 'Course Duration', value: '2 Years' },
            { label: 'Publication', value: 'N/A' },
            { label: 'Lecture Language', value: 'English, Chinese' },
        ],
    },
    {
        title: 'Master of Business Administration - Education',
        details: [
            { label: 'Nearest Intake & Deadline for Application', value: 'Summer (March 03, 2023)' },
            { label: 'Last Academic', value: 'Grade 12 / HSC / High School' },
            { label: 'Next Intakes', value: 'Spring, Winter, Fall' },
            { label: 'Minimum GPA', value: '60% or Above' },
            { label: 'Tuition Fee', value: '$11,465.00 / Year' },
            { label: 'English Proficiency', value: 'IELTS: 6.0 / TOEFL: 60 / PTE: 50 / DUOLINGO: 95' },
            { label: 'Course Duration', value: '2 Years' },
            { label: 'Publication', value: 'N/A' },
            { label: 'Lecture Language', value: 'English, Chinese' },
        ],
    },
    {
        title: 'Doctorate of Business Administration - Education',
        details: [
            { label: 'Nearest Intake & Deadline for Application', value: 'Summer (March 03, 2023)' },
            { label: 'Last Academic', value: 'Grade 12 / HSC / High School' },
            { label: 'Next Intakes', value: 'Spring, Winter, Fall' },
            { label: 'Minimum GPA', value: '60% or Above' },
            { label: 'Tuition Fee', value: '$11,465.00 / Year' },
            { label: 'English Proficiency', value: 'IELTS: 6.0 / TOEFL: 60 / PTE: 50 / DUOLINGO: 95' },
            { label: 'Course Duration', value: '2 Years' },
            { label: 'Publication', value: 'N/A' },
            { label: 'Lecture Language', value: 'English, Chinese' },
        ],
    },
    {
        title: 'Bachelor of Business Administration - Education',
        details: [
            { label: 'Nearest Intake & Deadline for Application', value: 'Summer (March 03, 2023)' },
            { label: 'Last Academic', value: 'Grade 12 / HSC / High School' },
            { label: 'Next Intakes', value: 'Spring, Winter, Fall' },
            { label: 'Minimum GPA', value: '60% or Above' },
            { label: 'Tuition Fee', value: '$11,465.00 / Year' },
            { label: 'English Proficiency', value: 'IELTS: 6.0 / TOEFL: 60 / PTE: 50 / DUOLINGO: 95' },
            { label: 'Course Duration', value: '2 Years' },
            { label: 'Publication', value: 'N/A' },
            { label: 'Lecture Language', value: 'English, Chinese' },
        ],
    },
];

export const paidApplicationStatus = [
    {
        status: 'Complete',
        statusBarColor: '#1E62E0'
    },
    {
        status: 'Reviewing',
        statusBarColor: '#78A1EC'
    },
    {
        status: 'Missing',
        statusBarColor: '#F2A735'
    },
    {
        status: 'Rejected',
        statusBarColor: '#9FA0A6'
    }
];

export const levelOfStudy = [
    { value: 'foundation-programs', label: 'Foundation/Pathway Programs' },
    { value: 'piploma-programs', label: 'Diploma/Certificate Programs' },
    { value: 'undergraduate-programs', label: 'Undergraduate Programs' },
    { value: 'postgraduate-programs', label: 'Postgraduate Programs' },
    { value: 'doctoral-programs-phd', label: 'Doctoral Programs (PhD)' },
    { value: 'Short-term-and-certification-programs', label: 'Short-term and Certification Programs' },
    { value: 'postgraduate-diploma-pgd', label: 'Postgraduate Diploma/Certificate (PGD)' }
];

export const GeneralInformation = [
    { Label: 'Founded', value: '1890' },
    { Label: 'SchoolID', value: '456' },
    { Label: 'DLI_number', value: 'O345G9788H' },
    { Label: 'InstitutionType', value: 'Private research university' },
];

export const AcceptanceLetterTimeframe = [
    { Label: 'January to April', value: 'Under 15 days' },
    { Label: 'May to August', value: 'Under 20 days' },
    { Label: 'September to December', value: 'Under 10 days' },
];

export const CostAndDuration = [
    { 
        Label: 'Application fee', 
        value: '$ 2345.567',
        icon: ApplicationFeeIcon 
    },
    { 
        Label: 'Average Tuition Fee', 
        value: '$ 2345.567 / Year',
        icon: AvgTutionFeeIcon 
    },
    { 
        Label: 'Average Graduate Program', 
        value: '02 (years)', 
        icon: AvgGradProgramIcon 
    },
    { 
        Label: 'Cost of Living', 
        value: '$ 45.567 / Year',
        icon: CostofLivingIcon 
    },
    { 
        Label: 'Average undergraduate Program', 
        value: '04 (years)',
        icon: AvgGradProgramIcon
    }
];

export const FinancialAid = [
    { Label: 'Tuition Fee Discount', value: 'Yes' },
    { Label: 'Financial Aid Acceptance ', value: 'Yes' },
    { Label: 'Scholarship Opportunity', value: 'Yes' },
]
export const Accommodation = [
    { Label: 'Accommodation Facility', value: 'Yes' },

]
export const GraduationAndEmployment = [
    { Label: 'Post-Graduation Opportunities', value: 'No' },
    { Label: 'Employment Opportunities ', value: 'Yes' },
]

export const CourseAccordionData = [
    {
        title : 'ACCT 220. Introduction to Financial Accounting (3)',
        description : 'Prerequisites: A grade of “C” or higher in MATH 103 or higher-level mathematics course; Sophomore class standing. Introduces the role of accounting in business and society, a summary of the accounting process, accounting measurement issues, analyzing and recording financial transactions, accounting valuation and allocation issues, conceptual foundation for understanding financial reporting, the usefulness of financial …',
    },
    {
        title : 'ACCT 230. Introduction to Managerial Accounting (3)',
        description : 'Prerequisites: ACCT 220; MATH 103 or a higher-level mathematics course with a grade of “C” or higher. Introduces the analysis and techniques for aiding management in planning and controlling decisions, and the use of accounting data for budgeting, cost control, pricing, evaluation of performance and general decision making.',
    },
    {
        title : 'ACCT 230. Introduction to Managerial Accounting (3)',
        description : 'Prerequisites: ACCT 220; MATH 103 or a higher-level mathematics course with a grade of “C” or higher. Introduces the analysis and techniques for aiding management in planning and controlling decisions, and the use of accounting data for budgeting, cost control, pricing, evaluation of performance and general decision making.',
    },
    {
        title : 'ACCT 292BCS. Volunteer Income Tax Assistance Clinic – Preparer (2-2-2)',
        description : 'Prerequisites: ACCT 220; MATH 103 or a higher-level mathematics course with a grade of “C” or higher. Introduces the analysis and techniques for aiding management in planning and controlling decisions, and the use of accounting data for budgeting, cost control, pricing, evaluation of performance and general decision making.',
    },
    {
        title : 'ACCT 292CCS. Volunteer Income Tax Assistance Clinic – Preparer (3-3-3)',
        description : 'Prerequisites: ACCT 220; MATH 103 or a higher-level mathematics course with a grade of “C” or higher. Introduces the analysis and techniques for aiding management in planning and controlling decisions, and the use of accounting data for budgeting, cost control, pricing, evaluation of performance and general decision making.',
    },
    {
        title : 'ACCT 350. Intermediate Financial Accounting I (3)',
        description : 'Prerequisites: ACCT 220; MATH 103 or a higher-level mathematics course with a grade of “C” or higher. Introduces the analysis and techniques for aiding management in planning and controlling decisions, and the use of accounting data for budgeting, cost control, pricing, evaluation of performance and general decision making.',
    }
];

export const CounselorActivitiesData = {
    sections: [
        {
            date: "Today",
            count: 100,
            entries: [
            {
                name: "Mir Mosharraf Karim",
                phone: "************",
                location: "São Tomé and Príncipe",
                startDate: "Mar 2025",
                program: "2 year Undergraduate Diploma",
                degree: "Master of Applied Data Science",
                buttonText: "Student Enrollment"
            },
            {
                name: "Australian Values Statement (Form 1281) and Privacy Notice (Form 1442)",
                startDate: "Mar 2025",
                university: "Federation University - Mount Helen (Ballarat)",
                buttonText: "Application"
            },
            {
                name: "Australian Values Statement (Form 1281) and Privacy Notice (Form 1442)",
                startDate: "Mar 2025",
                university: "Federation University - Mount Helen (Ballarat)",
                buttonText: "Application"
            }
            ]
        },
        {
            date: "Mon, 09 Dec 2021",
            count: 100,
            entries: [
            {
                name: "Mir Mosharraf Karim",
                phone: "************",
                location: "Papua New Guinea",
                startDate: "Mar 2025",
                program: "2 year Undergraduate Diploma",
                degree: "Master of Applied Data Science",
                buttonText: "Counseling",
                isInPerson: false 
            },
            {
                name: "Australian Values Statement (Form 1281) and Privacy Notice (Form 1442)",
                startDate: "Mar 2025",
                university: "Federation University - Mount Helen (Ballarat)",
                buttonText: "Application"
            },
            {
                name: "Australian Values Statement (Form 1281) and Privacy Notice (Form 1442)",
                startDate: "Mar 2025",
                university: "Federation University - Mount Helen (Ballarat)",
                buttonText: "Application"
            },
            {
                name: "Mir Mosharraf Karim",
                phone: "************",
                location: "Papua New Guinea",
                startDate: "Mar 2025",
                program: "2 year Undergraduate Diploma",
                degree: "Master of Applied Data Science",
                buttonText: "Counseling",
                isInPerson: true 
            },
            {
                name: "Mir Mosharraf Karim",
                phone: "************",
                location: "Papua New Guinea",
                startDate: "Mar 2025",
                program: "2 year Undergraduate Diploma",
                degree: "Master of Applied Data Science",
                buttonText: "Counseling",
                isInPerson: true 
            },
            ]
        },
        {
            date: "Sun, 08 Dec 2021",
            count: 100,
            entries: Array.from({ length: 20 }, (_, i) => ({
                name: `Student ${i + 1}`,
                phone: `01999-12345${i}`,
                location: "Country XYZ",
                startDate: "Feb 2024",
                program: `Program ${i + 1}`,
                degree: `Degree ${i + 1}`,
                buttonText: i % 2 === 0 ? "Student Enrollment" : "Application"
            }))
        }
    ]
};

export const SuperAdminStatCardData =[
    {
        label : 'Total Agencies',
        value : 34,
        decrease : 2.2,
        icon : StatCardVisitorIcon
    },
    {
        label : 'Total Universities',
        value : 20,
        decrease : 4.1,
        icon : StatCardStudentIcon
    },
    {
        label : 'Agencies Students',
        value : 7700,
        increase : 5.3,
        icon : StatCardApplicationIcon
    },
    {
        label : 'Agencies Applications',
        value : 7800,
        increase : 5.3,
        icon : StatCardAcceptedIcon
    },
    {
        label : 'Agencies VISA Success',
        value : 5600,
        decrease : 2.9,
        icon : StatCardRejectedIcon
    },
]

export const ApplicationProfileIntakes = [
        {
            id: 1,
            monthYear: "Oct 2024",
            status: "Closed",
            deadline: "09 Sept, 2024",
        },
        {
            id: 2,
            monthYear: "Jan 2025",
            status: "Open",
            deadline: "09 Dec, 2024",
        },
        {
            id: 3,
            monthYear: "Apr 2025",
            status: "Likely Open",
            deadline: "09 Mar, 2025",
        },
];

export const ApplicationProfileStepperData = [
    {   
        date: 'Sep 25, 2024',
        title: 'Collecting Documents',
        status: 'completed',
        update: 'This is a Update Final Offer Letter, and this document can be used to apply for your Visa :',
        content: 'Please ensure you complete ech step as outlined of your final offer letter as soon as possible in order to secure your seat.  Please ensure you review all details regarding refund and deferral policy to avoid any complications.',
    },
    {
        date: 'Sep 25, 2024',
        title: 'Applied',
        status: 'completed',
        content: 'Please ensure you complete ech step as outlined of your final offer letter as soon as possible in order to secure your seat.  Please ensure you review all details regarding refund and deferral policy to avoid any complications.',

    },
    {
        date: 'Sep 25, 2024',
        title: 'Accepted',
        status: 'completed',
        content: 'Please ensure you complete ech step as outlined of your final offer letter as soon as possible in order to secure your seat.  Please ensure you review all details regarding refund and deferral policy to avoid any complications.',

    },
    {
        date: 'Sep 25, 2024',
        title: 'CAL Issued',
        status: 'completed',
        content: 'Please ensure you complete ech step as outlined of your final offer letter as soon as possible in order to secure your seat.  Please ensure you review all details regarding refund and deferral policy to avoid any complications.',

    },
    {
        date: 'Sep 25, 2024',
        title: 'I20 Issued',
        status: 'completed',
        content: 'Please ensure you complete ech step as outlined of your final offer letter as soon as possible in order to secure your seat.  Please ensure you review all details regarding refund and deferral policy to avoid any complications.',

    },
    {
        date: 'Sep 25, 2024',
        title: 'Appointment Confirmation',
        status: 'active',
        content: 'Please ensure you complete ech step as outlined of your final offer letter as soon as possible in order to secure your seat.  Please ensure you review all details regarding refund and deferral policy to avoid any complications.',

    },
    {
        date: 'Sep 25, 2024',
        title: 'VISA Processing',
        status: 'regular',
    },
    {
        date: 'Sep 25, 2024',
        title: 'VISA Approved',
        status: 'rejected',
    },
];

export const studentDetails = [
    {
        label: 'ID',
        value: '#2464658'
    },
    {
        label: 'Passport ',
        value: 'NE594955 '
    },
    {
        label: 'Contact',
        value: '+880 1341890324'
    },
    {
        label: 'Email',
        value: '<EMAIL>'
    },
    {
        label: 'Status',
        value: 'File Received'
    },
];

export const requirementCardsData = [
    {
        title: 'Copy of Passport',
        description: 'Please attach a copy of the applicant\'s passport - pages that include the applicant\'s identity information.',
        status: 'Reviewing' as const,
        required: true,
        uploadedFiles: ['Passport.jpg'],
        allowUpload: true,
        file: pdfFileUrl
    },
    {
        title: 'English Language Proficiency Test',
        description: 'Please provide a copy of the applicant\'s English test scores.',
        status: 'Missing' as const,
        required: true,
        uploadedFiles: ['IELTS.pdf', 'document1.pdf'],
        allowUpload: true
    },
    {
        title: 'Request for Student Loans',
        description: 'Please provide details about your student loan requirements.',
        status: 'No Status' as const,
        allowAnswer: true
    },
    {
        title: 'Request for Student Loans',
        description: 'Please provide details about your student loan requirements.',
        status: 'Completed' as const,
        allowUpload: true
    },
    {
        title: 'Request for Student Loans',
        description: 'Please provide details about your student loan requirements.',
        status: 'Rejected' as const,
        allowUpload: true,
        uploadedFiles: ['s_statement.pdf'],
        ResubmissionDate: 'Sept 12,2024',
        RejectionNote: 'This file is incomplete documents or unmet visa requirements.'
    }
];

export const EventandMettingsData = [
    {
        date: { day: '10', month: 'Oct' },
        title: 'Organizational Meeting',
        description:
            'A meeting to discuss key organizational matters, including setting goals, assigning roles, planning initiatives, reviewing budget and resources, updating policies, and addressing team insights. The outcome is a clear understanding of roles and strategic direction for upcoming actions',
        time: '10:30 am - 12:00 pm',
    },
    {
        date: { day: '22', month: 'Oct' },
        title: 'Training Session',
        description:
            'A focused session designed to develop skills and knowledge in a specific area.',
        time: '10:30 am - 12:00 pm',
        location:
            '10/2, 4th Floor, Gawsia kashem Center, Arambagh, Motijheel, Dhaka.',
    },
    {
        date: { day: '07', month: 'Nov' },
        title: 'Meeting with HR',
        description:
            'A discussion with HR Admin to address personnel matters, policies, benefits, and more.',
        time: '10:30 am - 12:00 pm',
    },
    {
        date: { day: '23', month: 'Dec' },
        title: 'Meeting with Team',
        description:
            'A team gathering to discuss project updates, share progress, address challenges, and align on goals.',
        time: '10:30 am - 12:00 pm',
    },
    {
        date: { day: '12', month: 'Jan' },
        title: 'Meeting with Admin',
        description:
            'A meeting to discuss administrative tasks, policies, operations, or resource management.',
        time: '10:30 am - 12:00 pm',
    },
];

export const ApplicationSourceData = {
    "This Year": [
        { browser: "Agencies", visitors: 1200, fill: "#1E62E0" },
        { browser: "Referrals", visitors: 950, fill: "#1952BB" },
        { browser: "Direct from Students", visitors: 800, fill: "#144296" }
    ],
    "This Month": [
        { browser: "Agencies", visitors: 300, fill: "#1E62E0" },
        { browser: "Referrals", visitors: 250, fill: "#1952BB" },
        { browser: "Direct from Students", visitors: 200, fill: "#144296" }
    ],
    "This Week": [
        { browser: "Agencies", visitors: 175, fill: "#1E62E0" },
        { browser: "Referrals", visitors: 150, fill: "#1952BB" },
        { browser: "Direct from Students", visitors: 127, fill: "#144296" }
    ],
    "Today": [
        { browser: "Agencies", visitors: 50, fill: "#1E62E0" },
        { browser: "Referrals", visitors: 40, fill: "#1952BB" },
        { browser: "Direct from Students", visitors: 35, fill: "#144296" }
    ]
};

export const UniversityOnboardingData = [
    { month: 'January', This: 186, Previous: 80 },
    { month: 'February', This: 305, Previous: 200 },
    { month: 'March', This: 237, Previous: 120 },
    { month: 'April', This: 73, Previous: 190 },
    { month: 'May', This: 209, Previous: 130 },
    { month: 'June', This: 214, Previous: 140 },
    { month: 'July', This: 286, Previous: 180 },
    { month: 'August', This: 305, Previous: 220 },
    { month: 'September', This: 137, Previous: 150 },
    { month: 'October', This: 223, Previous: 400 },
    { month: 'November', This: 409, Previous: 300 },
    { month: 'December', This: 214, Previous: 160 },
];
export const ApprovalRateData = [
    { month: 'January', approval: 186, decline: 80 },
    { month: 'February', approval: 305, decline: 200 },
    { month: 'March', approval: 237, decline: 120 },
    { month: 'April', approval: 73, decline: 190 },
    { month: 'May', approval: 209, decline: 130 },
    { month: 'June', approval: 214, decline: 140 },
    { month: 'July', approval: 286, decline: 180 },
    { month: 'August', approval: 305, decline: 220 },
    { month: 'September', approval: 137, decline: 150 },
    { month: 'October', approval: 223, decline: 400 },
    { month: 'November', approval: 409, decline: 300 },
    { month: 'December', approval: 214, decline: 160 },
];
export const ApplicationProcessingTimeData = [
    { university: 'Harvard', days: 27 },
    { university: 'University of Bridgeport', days: 34  },
    { university: 'University of Michigan', days: 31  },
    { university: 'Westcliff University ', days: 30  },
    { university: 'University of Massachusetts', days: 34  },
    { university: 'Cornell University', days: 56  },
    { university: 'University of Central Missouri', days: 78  },
    { university: 'California Institute of Technology (Caltech)', days: 30  },
    { university: 'Massachusetts Institute of Technology (MIT)', days: 13  },
    { university: 'University of California, Berkeley (UCB)', days: 22  },
    { university: 'LUT University', days: 40  },
];
export const acceptanceRejectionRateData = [
    { university: 'Harvard', accepted: 27 , rejected:45 },
    { university: 'University of Bridgeport', accepted: 34  , rejected:56 },
    { university: 'University of Michigan', accepted: 31  , rejected:98 },
    { university: 'Westcliff University ', accepted: 30  , rejected:34 },
    { university: 'University of Massachusetts', accepted: 34  , rejected:87 },
    { university: 'Cornell University', accepted: 56  , rejected:65 },
    { university: 'University of Central Missouri', accepted: 78  , rejected:35 },
    { university: 'California Institute of Technology (Caltech)', accepted: 30  , rejected:46 },
    { university: 'Massachusetts Institute of Technology (MIT)', accepted: 13  , rejected:42 },
    { university: 'University of California, Berkeley (UCB)', accepted: 22  , rejected:56 },
    { university: 'LUT University', accepted: 40  , rejected:67 },
];
export const StudentEnrollmentData = [
    { month: 'January', visitors: 186, leads: 80, applicants: 100 },
    { month: 'February', visitors: 305, leads: 200, applicants: 150 },
    { month: 'March', visitors: 237, leads: 120, applicants: 180 },
    { month: 'April', visitors: 73, leads: 190, applicants: 120 },
    { month: 'May', visitors: 209, leads: 130, applicants: 160 },
    { month: 'June', visitors: 214, leads: 140, applicants: 170 },
    { month: 'July', visitors: 286, leads: 180, applicants: 190 },
    { month: 'August', visitors: 305, leads: 220, applicants: 200 },
    { month: 'September', visitors: 137, leads: 150, applicants: 210 },
    { month: 'October', visitors: 223, leads: 400, applicants: 220 },
    { month: 'November', visitors: 409, leads: 300, applicants: 230 },
    { month: 'December', visitors: 214, leads: 160, applicants: 240 },
];
export const LeadConversionInfoData = [
    { month: 'January', Leads: 186, },
    { month: 'February', Leads: 305,  },
    { month: 'March', Leads: 237,  },
    { month: 'April', Leads: 73,  },
    { month: 'May', Leads: 209,  },
    { month: 'June', Leads: 214,  },
    { month: 'July', Leads: 286,  },
    { month: 'August', Leads: 305,  },
    { month: 'September', Leads: 137,  },
    { month: 'October', Leads: 223,  },
    { month: 'November', Leads: 409,  },
    { month: 'December', Leads: 214,  },
];

export const ApplyGoalSuccessData = {
    'This Year': [
        { month: 'January', desktop: 186, mobile: 80 },
        { month: 'February', desktop: 305, mobile: 200 },
        { month: 'March', desktop: 237, mobile: 120 },
        { month: 'April', desktop: 73, mobile: 190 },
        { month: 'May', desktop: 209, mobile: 130 },
        { month: 'June', desktop: 214, mobile: 140 },
        { month: 'July', desktop: 486, mobile: 50 },
        { month: 'August', desktop: 505, mobile: 300 },
        { month: 'September', desktop: 137, mobile: 220 },
        { month: 'October', desktop: 43, mobile: 390 },
        { month: 'November', desktop: 609, mobile: 730 },
        { month: 'December', desktop: 714, mobile: 540 },
    ],
    'This Month': [
        { day: 'Week 1', desktop: 14, mobile: 330 },
        { day: 'Week 2', desktop: 300, mobile: 450 },
        { day: 'Week 3', desktop: 430, mobile: 250 },
        { day: 'Week 4', desktop: 100, mobile: 350 },
    ],
    'This Week': [
        { day: 'Monday', desktop: 220, mobile: 30 },
        { day: 'Tuesday', desktop: 600, mobile: 335 },
        { day: 'Wednesday', desktop: 300, mobile: 40 },
        { day: 'Thursday', desktop: 890, mobile: 225 },
        { day: 'Friday', desktop: 90, mobile: 50 },
        { day: 'Saturday', desktop: 200, mobile: 55 },
        { day: 'Sunday', desktop: 110, mobile: 60 },
    ],
    Today: [
        { hour: '8 AM', desktop: 10, mobile: 5 },
        { hour: '10 AM', desktop: 15, mobile: 10 },
        { hour: '12 PM', desktop: 20, mobile: 15 },
        { hour: '2 PM', desktop: 25, mobile: 20 },
        { hour: '4 PM', desktop: 30, mobile: 25 },
    ],
};

export const CounselorBarChartData = [
        { month: 'USA', agency: 186},
        { month: 'UK', agency: 305 },
        { month: 'Australia', agency: 237 },
        { month: 'Finland', agency: 73 },
        { month: 'Canada', agency: 209 },
        { month: 'Germany', agency: 214 },
        { month: 'Russia', agency: 286 },
        { month: 'Poland', agency: 305 },
        { month: 'Ukraine', agency: 137},
        { month: 'China', agency: 43 },
    ];
export const RevenueGenerationChartData = [
    { agency: 'FICC', revenue: 186},
    { agency: 'GNB', revenue: 305 },
    { agency: 'Future Abroad', revenue: 237 },
    { agency: 'PQW', revenue: 73 },
    { agency: 'MNO', revenue: 209 },
    { agency: 'RTS', revenue: 214 },
    { agency: 'BSB', revenue: 286 },
    { agency: 'BNB', revenue: 305 },
    { agency: 'NPM', revenue: 137},
    { agency: 'GuzBee', revenue: 43 },
    { agency: 'HTS', revenue: 43 },
    { agency: 'Intro', revenue: 43 },
];
export const applicationOverviewChartData = [
    { month: 'january', application: 186},
    { month: 'February', application: 305 },
    { month: 'March', application: 237 },
    { month: 'April', application: 73 },
    { month: 'May', application: 209 },
    { month: 'Jun', application: 214 },
    { month: 'July', application: 286 },
    { month: 'August', application: 305 },
    { month: 'September', application: 137},
    { month: 'October', application: 43 },
    { month: 'November', application: 43 },
    { month: 'December', application: 43 },
];

export const applicationStatusData = [
    { category: "In process", value: 55, fill: "#1E62E0" },
    { category: "Accepted", value: 35, fill: "#144296" },
    { category: "Rejected", value: 10, fill: "#E7EFFF" },
];
export const acceptanceRateChartData = [
    { university: 'Harvard', percentage: 86},
    { university: 'University of Bridgeport', percentage: 35 },
    { university: 'University of Michigan', percentage: 27 },
    { university: 'Westcliff University ', percentage: 73 },
    { university: 'University of Massachusetts', percentage: 29 },
    { university: 'Cornell University', percentage: 24 },
    { university: 'University of Central Missouri', percentage: 86 },
    { university: 'LUT University', percentage: 35 },
];

export const taskCompletionData = [
    { category: 'HR', completed: 75, remaining: 25 },
    { category: 'Accounts', completed: 60, remaining: 40 },
    { category: 'IT', completed: 80, remaining: 20 },
    { category: 'Sales', completed: 40, remaining: 20 },
    { category: 'Marketing', completed: 50, remaining: 50 },
    { category: 'Application Officer', completed: 90, remaining: 10 },
  ];
export const studentApplicationVolumnData = [
    { category: 'University of Michigan', applications: 75 },
    { category: 'Harvard University', applications: 60 },
    { category: 'University of Bridgeport', applications: 80 },
    { category: 'Westcliff University', applications: 40 },
    { category: 'University of Central Missouri', applications: 50 },
    { category: 'California Institute of Technology (Caltech)', applications: 43 },
    { category: 'Massachusetts Institute of Technology', applications: 56 },
    { category: 'University of California', applications: 87 },
    { category: 'LUT University', applications: 26 },
    { category: 'Cornell University', applications: 45 },
  ];

export const TopFiveSalesChartData = [
    { country: 'UK', sales: 500000},
    { country: 'USA', sales: 280000 },
    { country: 'Canada', sales: 260000 },
    { country: 'Australia', sales: 190000 },
    { country: 'Germany', sales: 140000 }
];

export const ProgramPopularityData = [
    { month: '2 year Undergraduate Diploma', percentage: 100},
    { month: '3 year Undergraduate Diploma', percentage: 40 },
    { month: '4 year Bachelor’s Degree', percentage: 5 },
    { month: '1 year Master’s Degree', percentage: 10 },
    { month: 'PhD', percentage: 50 },
    { month: 'Doctoral', percentage: 20 },
    { month: '3 year Bachelor’s Degree', percentage: 100 },
    { month: '2 year Master’s Degree', percentage: 60 },
    { month: '3 year Undergraduate Diploma', percentage: 10}
];
export const LeadDemographicData =  [
    { month: 'MBA', agency: 186},
    { month: 'Engineering', agency: 305 },
    { month: 'Medicine', agency: 237 },
    { month: 'Computer Science', agency: 73 },
    { month: 'BBA', agency: 209 },
    { month: 'Applied Science', agency: 214 },
    { month: 'Advertising', agency: 286 },
    { month: 'Accountancy', agency: 305 },
    { month: 'Agriculture Business', agency: 137},
    { month: 'Anthropology', agency: 43 },
];

export const TeamPerformanceData = [
        { designation: 'IT', performance: 186},
        { designation: 'HR', performance: 305 },
        { designation: 'Accounts', performance: 237 },
        { designation: 'BDO', performance: 73 },
        { designation: 'Applicaion Officer', performance: 209 },
        { designation: 'Application Manager', performance: 214 },
        { designation: 'Counselor', performance: 286 },
    ];

export const StudentDemographicsData = [
    { designation: '2012', performance: 50},
    { designation: '2013', performance: 9 },
    { designation: '2014', performance: 15 },
    { designation: '2015', performance: 12 },
    { designation: '2016', performance: 15 },
    { designation: '2017', performance: 7 },
    { designation: '2018', performance: 40 },
    { designation: '2019', performance: 19 },
    { designation: '2020', performance: 70 },
    { designation: '2021', performance: 57 },
    { designation: '2022', performance: 62 },
    { designation: '2023', performance: 85 },
    { designation: '2024', performance: 15 },
    { designation: '2025', performance: 20 },
];

export const slideContent = [
    {
        id: 1,
        img: UniversityImageOne,
    },
    {
        id: 2,
        img: UniversityImageTwo,
    },
    {
        id: 3,
        img: UniversityImageFour,
    },
    {
        id: 4,
        img: UniversityImageFive,
    },
    {
        id: 5,
        img: UniversityImageSix,
    },
    {
        id: 6,
        img: UniversityImageOne,
    },
    {
        id: 7,
        img: UniversityImageTwo,
    },
    {
        id: 8,
        img: UniversityImageFour,
    },
    {
        id: 9,
        img: UniversityImageFive,
    },
    {
        id: 10,
        img: UniversityImageSix,
    }
 ]

export const onGoingApplications = [
    {
        title: 'Master of Business Administration',
        university: 'University of Birmingham',
        location: 'Birmingham, England',
        createdOn: '21 Dec 2024',
        status: 'Accepted',
        logo: HarvardUniLogo, 
    },
    {
        title: 'Master of Science in Data Science & AI',
        university: 'Arizona State University',
        location: 'Tempe, Arizona, US',
        createdOn: '19 Oct 2024',
        status: 'Awaiting Documents',
        logo: CaltechNniLogo,
    },
    {
        title: 'Master of Communication Systems and Networks',
        university: 'Tampere University',
        location: 'Tampere, Finland',
        createdOn: '21 Sept 2024',
        status: 'Submitted',
        logo: MichiganUniLogo,
    },
    {
        title: 'Master of Communication Systems and Networks',
        university: 'Tampere University',
        location: 'Tampere, Finland',
        createdOn: '21 Sept 2024',
        status: 'Completed',
        logo: MichiganUniLogo,
    },
];

export const ApplicationVisaSuccessData = {
    'This Year': [
        { month: 'January', applications: 186, success: 80 },
        { month: 'February', applications: 305, success: 200 },
        { month: 'March', applications: 237, success: 120 },
        { month: 'April', applications: 73, success: 190 },
        { month: 'May', applications: 209, success: 130 },
        { month: 'June', applications: 214, success: 140 },
        { month: 'July', applications: 286, success: 50 },
        { month: 'August', applications: 305, success: 300 },
        { month: 'September', applications: 137, success: 220 },
        { month: 'October', applications: 43, success: 390},
        { month: 'November', applications: 409, success: 330 },
        { month: 'December', applications: 214, success: 140 },
    ],
    'This Month': [
        { day: 'Week 1', applications: 14, success: 330 },
        { day: 'Week 2', applications: 300, success: 450 },
        { day: 'Week 3', applications: 430, success: 250 },
        { day: 'Week 4', applications: 100, success: 350 },
    ],
    'This Week': [
        { day: 'Monday', applications: 220, success: 30 },
        { day: 'Tuesday', applications: 600, success: 335 },
        { day: 'Wednesday', applications: 300, success: 40 },
        { day: 'Thursday', applications: 890, success: 225},
        { day: 'Friday', applications: 90, success: 50 },
        { day: 'Saturday', applications: 200, success: 55},
        { day: 'Sunday', applications: 110, success: 60 },
    ],
    Today: [
        { hour: '4 PM', applications: 30, success: 25 },
    ],
};
export const visaApplicationVolumnData = {
    'This Year': [
        { agency: 'FICC', applications: 42, pending: 78, rejected: 70},
        { agency: 'GNB', applications: 23, pending: 16, rejected: 34 },
        { agency: 'Future Abroad', applications: 57, pending: 43, rejected: 9 },
        { agency: 'MNO', applications: 63, pending: 41, rejected: 10 },
        { agency: 'RTS', applications: 45, pending: 83, rejected: 23 },
        { agency: 'BSB', applications: 37, pending: 55, rejected: 20 },
        { agency: 'BNB', applications: 12, pending: 10, rejected: 2 },
        { agency: 'NPM', applications: 46, pending: 64, rejected: 24 },
        { agency: 'PQW', applications: 10, pending: 23, rejected: 24 },
        { agency: 'GuzBee', applications: 43, pending: 39, rejected: 40}
    ],
};
export const applicationDestinationData = {
    'This Year': [
        { country: 'USA', africa: 42, asia: 78, europe: 70, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'UK', africa: 23, asia: 16, europe: 34, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'Canada', africa: 57, asia: 43, europe: 9, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'Australia', africa: 63, asia: 41, europe: 10, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'Germany', africa: 45, asia: 83, europe: 23, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'Italy', africa: 37, asia: 55, europe: 20, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'Japan', africa: 12, asia: 10, europe: 2, northAmerica: 54 , southAmerica: 34, oceania: 75 },
        { country: 'China', africa: 46, asia: 64, europe: 24, northAmerica: 54 , southAmerica: 34, oceania: 75 }
    ],
};
export const regionalAnalysisData = {
    'This Year': [
        { country: 'USA', applicationsSubmitted: 42, acceptanceRate: 78 },
        { country: 'UK', applicationsSubmitted: 23, acceptanceRate: 16 },
        { country: 'Canada', applicationsSubmitted: 57, acceptanceRate: 43},
        { country: 'Australia', applicationsSubmitted: 63, acceptanceRate: 41 },
        { country: 'Finland', applicationsSubmitted: 45, acceptanceRate: 83 },
        { country: 'New Zealand', applicationsSubmitted: 37, acceptanceRate: 55 },
        { country: 'India', applicationsSubmitted: 12, acceptanceRate: 10},
    ],
};
export const paidUnpaidApplicationData = {
    'This Year': [
        { applicant: `Alena George (India)`, paidApplication: 42, unpaidApplication: 78 },
        { applicant: `Gusavo Curtis (Bangladesh)`, paidApplication: 23, unpaidApplication: 16 },
        { applicant: `Jaxson Mango (Finland)`, paidApplication: 57, unpaidApplication: 43},
        { applicant: `Rayna Lipshutz (USA)`, paidApplication: 63, unpaidApplication: 41 },
        { applicant: `Zaire Lipshutz (USA)`, paidApplication: 45, unpaidApplication: 83 },
        { applicant: `Ryan Geidt (Canada)`, paidApplication: 37, unpaidApplication: 55 },
        { applicant: `Kaylynn Curtis (Australia)`, paidApplication: 12, unpaidApplication: 10},
        { applicant: `Talan George (New Zealand)`, paidApplication: 12, unpaidApplication: 10},
        { applicant: `Zaire Baptista (Finland)`, paidApplication: 12, unpaidApplication: 10},
        { applicant: `Nolan Levin (Bangladesh)`, paidApplication: 12, unpaidApplication: 10},
        { applicant: `Angel Levin (Finland)`, paidApplication: 12, unpaidApplication: 10},
        { applicant: `Makenna Gouse (India)`, paidApplication: 12, unpaidApplication: 10},
    ],
};
export const feedbackRatingsData = {
    'This Year': [
        { university: 'California Institute of Technology (Caltech)', communication: 42, responsiveness: 78, admissionSupport: 70},
        { university: 'Harvard', communication: 23, responsiveness: 16, admissionSupport: 34 },
        { university: 'University of Bridgeport', communication: 57, responsiveness: 43, admissionSupport: 9 },
        { university: 'Massachusetts Institute of Technology (MIT)', communication: 63, responsiveness: 41, admissionSupport: 10 },
        { university: 'University of California, Berkeley (UCB)', communication: 45, responsiveness: 83, admissionSupport: 23 },
        { university: 'University of Michigan', communication: 37, responsiveness: 55, admissionSupport: 20 },
        { university: 'University of Massachusetts', communication: 12, responsiveness: 10, admissionSupport: 2 },
        { university: 'Westcliff University ', communication: 46, responsiveness: 64, admissionSupport: 24 },
        { university: 'University of Central Missouri', communication: 10, responsiveness: 23, admissionSupport: 24 },
        { university: 'Massachusetts Institute of Technology (MIT)', communication: 43, responsiveness: 39, admissionSupport: 40},
        { university: 'Cornell University', communication: 43, responsiveness: 39, admissionSupport: 40},
        { university: 'LUT University', communication: 43, responsiveness: 39, admissionSupport: 40}
    ],
};

export const AcademicData = [
    {
        degree: 'Master’s',
        data: [
            { 
                label: 'Institute', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'Group/ Subject', 
                value: 'M.Sc in CSE'
            },
            { 
                label: 'Board/ University', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'CGPA/GPA/ Division', 
                value: '3.35'
            },
            { 
                label: 'Passing Year', 
                value: '2023'
            }
        ]
    },
    {
        degree: 'Bachelor’s',
        data: [
            { 
                label: 'Institute', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'Group/ Subject', 
                value: 'M.Sc in CSE'
            },
            { 
                label: 'Board/ University', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'CGPA/GPA/ Division', 
                value: '3.35'
            },
            { 
                label: 'Passing Year', 
                value: '2023'
            }
        ]
    },
    {
        degree: 'HSC',
        data: [
            { 
                label: 'Institute', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'Group/ Subject', 
                value: 'M.Sc in CSE'
            },
            { 
                label: 'Board/ University', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'CGPA/GPA/ Division', 
                value: '3.35'
            },
            { 
                label: 'Passing Year', 
                value: '2023'
            }
        ]
    },
    {
        degree: 'SSC',
        data: [
            { 
                label: 'Institute', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'Group/ Subject', 
                value: 'M.Sc in CSE'
            },
            { 
                label: 'Board/ University', 
                value: 'Bangladesh University of Professionals'
            },
            { 
                label: 'CGPA/GPA/ Division', 
                value: '3.35'
            },
            { 
                label: 'Passing Year', 
                value: '2023'
            }
        ]
    }
]

export const Proficiency = [
    {
        degree: 'IELTS',
        data: [
            { 
                label: 'Score', 
                value: 'Overall 7.5 (R-7.5 W-8.0 L-7.5 S-7.5)'
            },
            { 
                label: 'Exam Date', 
                value: '3/23/2021'
            },
            { 
                label: 'Expiry Date', 
                value: '3/23/2023'
            },
            { 
                label: 'Note', 
                value: 'will be expired soon, will give duolingo'
            }
        ]
    },
    {
        degree: 'TOEFL',
        data: [
            { 
                label: 'Score', 
                 value: 'Overall 7.5 (R-7.5 W-8.0 L-7.5 S-7.5)'
            },
            { 
                label: 'Exam Date', 
                value: '3/23/2021'
            },
            { 
                label: 'Expiry Date', 
                value: '3/23/2023'
            },
            { 
                label: 'Note', 
                value: 'will be expired soon, will give duolingo'
            }
        ]
    },
    
];

export const FAQData = [
    {
        SectionTitle: 'Admissions Process',
        faqItem: [
            {
                id: 1,
                question: 'What is the process for applying to a program?',
                answer: 'The application process typically begins with a preliminary application, followed by a more detailed application and interview process.'
            },
            {
                id: 2,
                question: 'How long does the application process typically take?',
                answer: 'The application process can take anywhere from 2-3 months, depending on the program and the university.'
            },
            {
                id: 3,
                question: 'What happens if I miss my interview date?',
                answer: 'If you miss your interview date, you will be notified and have 30 days to make a rescheduling request. If you do not make a rescheduling request by the deadline, your application will be denied.'
            },
            {
                id: 4,
                question: 'What if I have a medical condition that prevents me from attending an interview?',
                answer: 'In such cases, you can request a make-up interview date and submit the application as soon as possible. You will be notified if your request is approved and you will be able to attend the interview.'
            },
            {
                id: 5,
                question: 'What happens if I have a financial issue that prevents me from paying for the application fees?',
                answer: 'If you have a financial issue that prevents you from paying for the application fees, you can request a waiver of fees. You will be notified if your request is approved and you will be able to pay the remaining fees on your own.'
            },
            {
                id: 6,
                question: 'How long does the application process typically take?',
                answer: 'The application process can take anywhere from 2-3 months, depending on the program and the university.'
            },
            // {
            //     id: 7,
            //     question: 'What if I have a medical condition that prevents me from attending an interview?',
            //     answer: 'In such cases, you can request a make-up interview date and submit the application as soon as possible. You will be notified if your request is approved and you will be able to attend the interview.'
            // },
        ]
    },
    {
        SectionTitle: 'Program and Course Details',
        faqItem: [
            {
                id: 1,
                question: 'What is the process for applying to a program?',
                answer: 'The application process typically begins with a preliminary application, followed by a more detailed application and interview process.'
            },
            {
                id: 2,
                question: 'How long does the application process typically take?',
                answer: 'The application process can take anywhere from 2-3 months, depending on the program and the university.'
            },
            {
                id: 3,
                question: 'What happens if I miss my interview date?',
                answer: 'If you miss your interview date, you will be notified and have 30 days to make a rescheduling request. If you do not make a rescheduling request by the deadline, your application will be denied.'
            },
            {
                id: 4,
                question: 'What if I have a medical condition that prevents me from attending an interview?',
                answer: 'In such cases, you can request a make-up interview date and submit the application as soon as possible. You will be notified if your request is approved and you will be able to attend the interview.'
            },
            {
                id: 5,
                question: 'What happens if I have a financial issue that prevents me from paying for the application fees?',
                answer: 'If you have a financial issue that prevents you from paying for the application fees, you can request a waiver of fees. You will be notified if your request is approved and you will be able to pay the remaining fees on your own.'
            },
        ]
    },
    {
        SectionTitle: 'Tuition and Financial Aid',
        faqItem: [
            {
                id: 1,
                question: 'What is the process for applying to a program?',
                answer: 'The application process typically begins with a preliminary application, followed by a more detailed application and interview process.'
            },
            {
                id: 2,
                question: 'How long does the application process typically take?',
                answer: 'The application process can take anywhere from 2-3 months, depending on the program and the university.'
            },
            {
                id: 3,
                question: 'What happens if I miss my interview date?',
                answer: 'If you miss your interview date, you will be notified and have 30 days to make a rescheduling request. If you do not make a rescheduling request by the deadline, your application will be denied.'
            },
            {
                id: 4,
                question: 'What if I have a medical condition that prevents me from attending an interview?',
                answer: 'In such cases, you can request a make-up interview date and submit the application as soon as possible. You will be notified if your request is approved and you will be able to attend the interview.'
            },
            {
                id: 5,
                question: 'What happens if I have a financial issue that prevents me from paying for the application fees?',
                answer: 'If you have a financial issue that prevents you from paying for the application fees, you can request a waiver of fees. You will be notified if your request is approved and you will be able to pay the remaining fees on your own.'
            },
        ]
    },

];

export const topUniversitiesList = [
    {
        id:1,
        universityName: "Massachusetts Institute of Technology (MIT)",
        universityLogo: Harvard,
        location: "Cambridge, AL",
        country: { label: 'Canada', logo: Canada },
        type: "Private",
    },
    {
        id:2,
        universityName: "Harvard University",
        universityLogo: Harvard,
        location: "Philadelphia, AK",
        country: { label: 'United States', logo: Canada },
        type: "Public",
    },
    {
        id:3,
        universityName: "California Institute of Technology (Caltech)",
        universityLogo: Harvard,
        location: "Pasadena, IN",
        country: { label: 'Canada', logo: Canada },
        type: "Non-Profit",
    },
    {
        id:4,
        universityName: "University of California, Berkeley (UCB)",
        universityLogo: Harvard,
        location: "Cambridge, AL",
        country: { label: 'Canada', logo:Canada },
        type: "Private, Non-Profit",
    }
];

export const FaqData = [
    {
        question: 'What is the Preferred Partner Program',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'Am i a Preferred Partner?',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'What are the benefits of being a Preferred Partner?',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'What is a status?',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'How can I raise my status?',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'Where can i see my current status?',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'How and when my status update?',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
    {
        question: 'I have more question!',
        answer: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.'
    },
]


export const BadgeStatusData = [
    {
        icon : StarterStar,
        heading: 'Starter Status',
        description: '1 unique submitted applicant in the previous calender year.',
        attributes: [
            'Standard Application Processing',
            'ABC Training',
            'Trainhub'
        ],
        achieved: true
    },
    {
        icon : BronzeStar,
        heading: 'Bronze Status',
        description: '2-8 unique submitted applicant in the previous calender year.',
        attributes: [
            'Ireland Istitutes (By Invitation)',
            'ABC Training',
            'Trainhub'
        ],
        achieved: true
    },
    {
        icon : SilverStar,
        heading: 'Silver Status',
        description: '9-24 unique submitted applicant in the previous calender year.',
        attributes: [
            'Standard Application Processing',
            'ABC Training',
            'Trainhub'
        ],
        achieved: false
    },
    {
        icon : GoldStar,
        heading: 'Gold Status',
        description: '25-74 unique submitted applicant in the previous calender year.',
        attributes: [
            'Standard Application Processing',
            'ABC Training',
            'Trainhub'
        ],
        achieved: false
    },
    {
        icon : PlatinumStar,
        heading: 'Platinum Status',
        description: '75+ unique submitted applicant in the previous calender year.',
        attributes: [
            'Standard Application Processing',
            'ABC Training',
            'Trainhub'
        ],
        achieved: false
    },
]


export const mostRecentVisaSuccess = [
    {
        id: 262,
        name: 'Nolan Westervelt',
        intake: 'Oct 2024',
        program: "2 year Undergraduate Diploma",
        country: { label: 'Canada', logo: Canada },
    },
    {
        id: 343,
        name: 'Nolan Westervelt',
        intake: 'Oct 2024',
        program: "2 year Undergraduate Diploma",
        country: { label: 'Canada', logo: Canada },
    },
    {
        id: 543,
        name: 'Nolan Westervelt',
        intake: 'Oct 2024',
        program: "2 year Undergraduate Diploma",
        country: { label: 'Canada', logo: Canada },
    },
    {
        id: 654,
        name: 'Nolan Westervelt',
        intake: 'Oct 2024',
        program: "2 year Undergraduate Diploma",
        country: { label: 'Canada', logo: Canada },
    },{
        id:223,
        name: 'Nolan Westervelt',
        intake: 'Oct 2024',
        program: "2 year Undergraduate Diploma",
        country: { label: 'Canada', logo: Canada },
    }
];

export const employeePerformanceList = [
    {
        id: '01',
        name: 'Nolan Westervelt',
        designation: 'UI UX Designer',
        performance: 'Good',
        progress: 91
    },
   {
        id: '03',
        name: 'Zain Herwitz',
        designation: 'Accounts',
        performance: 'Good',
        progress: 91
    },
    {
        id: '04',
        name: 'Kaylynn Vetrovs',
        designation: 'BDO',
        performance: 'Avarage',
        progress: 67
    },
    {
        id: '05',
        name: 'Jaxson Stanton',
        designation: 'Applications Manager',
        performance: 'Weak',
        progress: 35
    },
    {
        id: '08',
        name: 'Emery Septimus',
        designation: 'Applications Officer',
        performance: 'Avarage',
        progress: 55
    }
];

export const employeeList = [
    {
        eid: '262',
        name: 'Nolan Westervelt',
        designation: 'Counselor',
        email: '<EMAIL>',
        mobile: '+8801962-446543',
        joinDate: '22 Oct 2023',
        status: 'On Leave'
    },
    {
        eid: '159',
        name: 'Zain Herwitz',
        designation: 'Application Officer',
        email: '<EMAIL>',
        mobile: '+8801952-654654',
        joinDate: '12 Aug 2022',
        status: 'Inactive'
    },
    {
        eid: '237',
        name: 'Kaylynn Vetrovs',
        designation: 'Application Officer',
        email: '<EMAIL>',
        mobile: '+8801553-446543',
        joinDate: '15 Sept 2023',
        status: 'Active'
    },
    {
        eid: '356',
        name: 'Jaxson Stanton',
        designation: 'Counselor',
        email: '<EMAIL>',
        mobile: '+8801964-546585',
        joinDate: '17 Sept 2023',
        status: 'On Leave'
    },
    {
        eid: '345',
        name: 'Emery Septimus',
        designation: 'Counselor',
        email: '<EMAIL>',
        mobile: '+8801825-264854',
        joinDate: '23 Oct 2022',
        status: 'Active'
    }
];

export const invitedMembers = [
    {
        name: 'Nolan Westervelt',
        Designation: 'Counselor',
        Email: '<EMAIL>'
    },
    {
        name: 'Zain Herwitz',
        Designation: 'Application Officer',
        Email: '<EMAIL>'
    },
    {
        name: 'Kaylynn Vetrovs',
        Designation: 'Application Manager',
        Email: '<EMAIL>'
    },
    {
        name: 'Jaxson Stanton',
        Designation: 'Counselor',
        Email: '<EMAIL>'
    },
    {
        name: 'Emery Septimus',
        Designation: 'Admin',
        Email: '<EMAIL>'
    }
];

export const tabData = [
    { 
        label: 'General', 
        value: 'General',
        Data: [
            {
                CardLabel: 'Visitors/ Leads',
                CardValue: 32,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Students',
                CardValue: 120,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Applications',
                CardValue: 134,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'VISA Success',
                CardValue: 134,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Employee',
                CardValue: 40,
                Decrease: 2.2,
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Employee',
                CardValue: 1200,
                Decrease: 2.2,
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
        ]

    },    
    { 
        label: 'Students', 
        value: 'Students',
        Data: [
            {
                CardLabel: 'Students',
                CardValue: 134,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Pending',
                CardValue: 120,
                Decrease: undefined,
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'In Process',
                CardValue: 134,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Success',
                CardValue: 60,
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Rejected',
                CardValue: 20,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
        ]
    },  
    { 
        label: 'Applications', 
        value: 'Applications',
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ] 
    },
    { 
        label: 'Leads', 
        value: 'Leads',
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ]
    },
    { 
        label: 'HR', 
        value: 'HR',
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ] 
    },
    { 
        label: 'Accounts',
        value: 'Accounts',
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ]
    },
    { 
        label: 'Reports', 
        value: 'Reports',
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ]
    },
];

export const ApplicationData = {
    'This Year': [
        { month: 'August', applications: 305, success: 300 },
        { month: 'September', applications: 137, success: 220 },
        { month: 'October', applications: 43, success: 390},
        { month: 'November', applications: 409, success: 330 },
        { month: 'December', applications: 214, success: 140 },
    ],
    'This Month': [
        { day: 'Week 1', applications: 14, success: 330 },
        { day: 'Week 2', applications: 300, success: 450 },
        { day: 'Week 3', applications: 430, success: 250 },
        { day: 'Week 4', applications: 100, success: 350 },
    ],
    'This Week': [
        { day: 'Monday', applications: 220, success: 30 },
        { day: 'Tuesday', applications: 600, success: 335 },
        { day: 'Wednesday', applications: 300, success: 40 },
        { day: 'Thursday', applications: 890, success: 225},
        { day: 'Friday', applications: 90, success: 50 },
        { day: 'Saturday', applications: 200, success: 55},
        { day: 'Sunday', applications: 110, success: 60 },
    ],
    Today: [
        { hour: '4 PM', applications: 30, success: 25 },
    ],
};

export const chartConfig = {
    applications: { label: 'Accepted', color: '#1E62E0' },
    success: { label: 'Rejected', color: '#E3E7FC' },
};

export const TopDestinationChartConfig = {
    agency: { label: 'Total Student', color: '#1E62E0' },
}

export const revenueData = {
    'This Year': [
        { category: 'Overall Revenue', value: 1000, fill: '#E3E7FC' },
        { category: 'Net Profit', value: 600, fill: '#1E62E0' },
        // { category: 'Service Charge', value: 400, fill: '#FF6384' },
    ],
    'This Month': [
        { category: 'Overall Revenue', value: 800, fill: '#E3E7FC' },
        { category: 'Net Profit', value: 500, fill: '#1E62E0' },
        // { category: 'Service Charge', value: 300, fill: '#FF6384' },
    ],
    // Add more ranges as needed
};

export const revenueChartConfig = {
    value: {
        label: 'Value',
    },
    'Overall Revenue': {
        label: 'Overall Revenue',
        color: '#D2E3FC',
    },
    'Net Profit': {
        label: 'Net Profit',
        color: '#1E62E0',
    },
    // 'Service Charge': {
    //     label: 'Service Charge',
    //     color: '#dd3384',
    // },
} satisfies ChartConfig;

export const performanceStyles: Record<string, string> = {
    Good: 'bg-[#ECFDF3] text-[#027A48]', 
    Avarage: 'bg-[#F7E1C140] text-[#F2A735]', 
    Weak: 'bg-[#FF3B301A] text-[#FF3B30]', 
};

export const visaSuccessTableHead = [
    'ID',
    'Name',
    'Intake',
    'Program',
    'Country'
];

export const employeePerformanceTableHead = [
    'ID',
    'Name',
    'Designation',
    'Performance',
    'Progress'
]; 

export const StatusColor = (status: string) => {
    switch (status) {
        case 'Pending':
            return 'bg-[#42A5F51A] text-[#42A5F5]';
        case 'On Hold':
            return 'bg-[#F7E1C140] text-[#F2A735]';
        case 'Paid':
            return 'bg-[#ECFDF3] text-[#027A48]';
        case 'Unpaid':
            return 'bg-[#FF3B301A] text-[#FF3B30]';
        default:
            return 'Unknown status.';
    }
};


export const UniTypeSelectButton = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' }
]


export const ApplicationStatusPieChartData = [
    { status: "Applied", value: 200, fill: "#1E62E0" },
    { status: "Rejected", value: 80, fill: "#1952BB" },
    { status: "Accepted", value: 120, fill: "#144296" },
]
export const AgencyStatusPieChartData = [
    { status: "Active", value: 65, fill: "#1E62E0" },
    { status: "Pending", value: 25, fill: "#1952BB" },
    { status: "Inactive", value: 10, fill: "#144296" },
]
export const DemographicsChartData = [
    { country: "Bangladesh", value: 65, fill: "#0F3170" },
    { country: "USA", value: 25, fill: "#123B86" },
    { country: "Finland", value: 10, fill: "#15459D" },
    { country: "UK", value: 40, fill: "#184EB3" },
    { country: "Australia", value: 20, fill: "#1B58CA" },
    { country: "New Zealand", value: 10, fill: "#1E62E0" },
    { country: "Canada", value: 80, fill: "#3572E3" },
    { country: "Malaysia", value: 45, fill: "#4B81E6" },
    { country: "China", value: 32, fill: "#6291E9" },
    { country: "India", value: 78, fill: "#78A1EC" }
]
export const ageWiseApplicationChartData = [
    { ageGroup: "18-22", value: 65, fill: "#0F3170" },
    { ageGroup: "23-26", value: 25, fill: "#184EB3" },
    { ageGroup: "27-30", value: 10, fill: "#78A1EC" },
    { ageGroup: "30+", value: 40, fill: "#1E62E0" },
]
export const StudentStatusPieChartData = [
    { status: "Applied", value: 320, fill: "#1E62E0" },
    { status: "Accepted", value: 240, fill: "#144296" },
    { status: "Enrolled", value: 180, fill: "#6291E9" },
    { status: "Deferred", value: 20, fill: "#A1C2FF" },
    { status: "Rejected", value: 50, fill: "#537FF1" },
    { status: "Withdrawn", value: 15, fill: "#005CD2" },
]


export const PopularCoursesChartData = [
    { label: "Bachelor of Computer Science and Engineering", value: 83655 },
    { label: "MBA in Business Analytics", value: 72643 },
    { label: "Master of Applied Data Science", value: 63655 },
    { label: "Bachelor of Science in Information Technology", value: 56324 },
    { label: "Bachelor of Science in Artificial Intelligence", value: 41633},
    { label: "BBA in Hospital Management", value: 20675},
];

export const AwardedStudentsListTableHead= [
    'SID',
    'Name',
    'Nationality',
    'Program',
    'Scholership',
    'Grant Year'
];
export const SuperAdminGeneralMeetingsHead= [
    'Agency',
    'Key Accounts Manager',
    'Meeting Agenda',
    'Date & Time',
    'Join Link'
];
export const StudentsListTableHead= [
    'SID',
    'Name',
    'Nationality',
    'Program',
    'Status'
];
export const ComissionsListTableHead= [
    'SID',
    'Name',
    'Comission Type',
    'Comission Deadline',
    'Comission',
    'Status'
];

export const ScholarshipAwardStudentList = [
    {
        sid: 456,
        name: 'Nolan Westervelt',
        Nationality: { label: 'Canada', logo: Canada },
        program: "2 year Undergraduate Diploma",
        scholership: 'Fulbright Foreign Student Program',
        grantYear: '2025'
    },
    {
        sid: 456,
        name: 'Zain Herwitz',
        Nationality: { label: 'Canada', logo: Canada },
        program: "3 year Undergraduate Diploma",
        scholership: 'Humphrey Fellowship Program',
        grantYear: '2025'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        Nationality: { label: 'Canada', logo: Canada },
        program: "4 year Bachelor's Degree",
        scholership: 'Commonwealth Scholarships',
        grantYear: '2024'
    },
    {
        sid: 456,
        name: 'Jaxson Stanton',
        Nationality: { label: 'Canada', logo: Canada },
        program: "1 year Master’s Degree",
        scholership: 'AAUW International Fellowship ',
        grantYear: '2024'
    },
    {
        sid: 456,
        name: 'Emery Septimus',
        Nationality: { label: 'Canada', logo: Canada },
        program: "PhD",
        scholership: 'Humphrey Fellowship Program',
        grantYear: '2024'
    },
    {
        sid: 456,
        name: 'Nolan Rosser',
        Nationality: { label: 'Canada', logo: Canada },
        program: "Doctoral",
        scholership: 'Joint Japan World Bank Scholarship',
        grantYear: '2024'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        Nationality: { label: 'Canada', logo: Canada },
        program: "3 year Bachelor’s Degree",
        scholership: 'University-Specific Scholarships',
        grantYear: '2024'
    },

];
export const superAdminGeneralMeetingsData = [
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: ' 14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: '14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: '14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: '14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: '14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: '14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },
    {
        
        agency: { label: 'Canada', logo: Canada },
        keyAccountManager: 'Chance Carder',
        meetingAgenda: "Business Growth",
        dateAndTime: '14 Oct 2024 01:00 pm',
        joinLink: '/fdsfsdfsdfsdf'
    },

];

export const StudentListTableData = [
    {
        sid: 456,
        name: 'Nolan Westervelt',
        Nationality: { label: 'Canada', logo: Canada },
        program: "2 year Undergraduate Diploma",
        scholership: 'Fulbright Foreign Student Program',
        status: 'On Hold'
    },
    {
        sid: 456,
        name: 'Zain Herwitz',
        Nationality: { label: 'Canada', logo: Canada },
        program: "3 year Undergraduate Diploma",
        scholership: 'Humphrey Fellowship Program',
        status: 'Withdrawn'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        Nationality: { label: 'Canada', logo: Canada },
        program: "4 year Bachelor's Degree",
        scholership: 'Commonwealth Scholarships',
        status: 'Transferred'
    },
    {
        sid: 456,
        name: 'Jaxson Stanton',
        Nationality: { label: 'Canada', logo: Canada },
        program: "1 year Master’s Degree",
        scholership: 'AAUW International Fellowship ',
        status: 'Withdrawn'
    },
    {
        sid: 456,
        name: 'Emery Septimus',
        Nationality: { label: 'Canada', logo: Canada },
        program: "PhD",
        scholership: 'Humphrey Fellowship Program',
        status: 'Graduated'
    },
    {
        sid: 456,
        name: 'Nolan Rosser',
        Nationality: { label: 'Canada', logo: Canada },
        program: "Doctoral",
        scholership: 'Joint Japan World Bank Scholarship',
        status: 'Transferred'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        Nationality: { label: 'Canada', logo: Canada },
        program: "3 year Bachelor’s Degree",
        scholership: 'University-Specific Scholarships',
        status: 'Active'
    },

];

export const ComissionsListTableData = [
    {
        sid: 456,
        name: 'Nolan Westervelt',
        ComissionType: 'Fixed Commission',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Pending'
    },
    {
        sid: 456,
        name: 'Zain Herwitz',
        ComissionType: 'Percentage of Tuition',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'On Hold'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        ComissionType: 'Tiered Structure',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Paid'
    },
    {
        sid: 456,
        name: 'Jaxson Stanton',
        ComissionType: 'Performance Based',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Unpaid'
    },
    {
        sid: 456,
        name: 'Emery Septimus',
        ComissionType: 'Performance Bonus',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Paid'
    },
    {
        sid: 456,
        name: 'Nolan Rosser',
        ComissionType: 'Performance Bonus',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Paid'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        ComissionType: 'Performance Bonus',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Unpaid'
    },
    {
        sid: 456,
        name: 'Kaylynn Vetrovs',
        ComissionType: 'Performance Bonus',
        ComissionDeadline: "22 Oct 2023",
        Comission: '$ 1000.72',
        status: 'Pending'
    },

];

export const currency = [
    { value: 'AUD', label: 'AUD' },
    { value: 'CAD', label: 'CAD' },
    { value: 'EUR', label: 'EUR' },
    { value: 'GBP', label: 'GBP' },
    { value: 'USD', label: 'USD' }
]

export const paymentFrecuencyData = [
    { value: 'Per Semester', label: 'Per Semester' },
    { value: 'Per Year', label: 'Per Year' },
    { value: 'One Time', label: 'One Time' }
]

export const paymentTerms = [
    { value: '15 days', label: '15 days' },
    { value: '30 days', label: '30 days' },
    { value: '45 days', label: '45 days' }
]

export const commissionPaymentMethod = [
    { value: 'Bank Transfer', label: 'Bank Transfer' },
    { value: 'PayPal', label: 'PayPal' },
    { value: 'Wire Transfer', label: 'Wire Transfer' }
]

export const commissionPayoutCycle = [
    { value: 'Rolling Basis', label: 'Rolling Basis' },
]