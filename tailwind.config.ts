import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
    darkMode: ["class"],
    content: [
        "./pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./components/**/*.{js,ts,jsx,tsx,mdx}",
        "./app/**/*.{js,ts,jsx,tsx,mdx}",
    ],
	
    theme: {
    	extend: {
    		colors: {
    			grayOne: '#EFF0F0',
    			grayTwo: '#C7C7CA',
    			grayThree: '#9FA0A6',
    			grayFour: '#7A7B82',
    			grayFive: '#57585E',
    			graySix: '#36373B',
    			graySeven: '#19191C',
    			grayEight: '#DEDEE0',
    			primary: {
    				DEFAULT: 'hsl(var(--primary))',
    				foreground: 'hsl(var(--primary-foreground))'
    			},
    			primaryColor: '#1E62E0',
    			primaryOne: '#F4F7FE',
    			primaryTwo: '#E3E7FC',
    			primaryThree: '#E7EFFF',
    			primaryFour: '#E9F0FF',
    			secondaryColor: '#144296',
    			secondary: {
    				DEFAULT: 'hsl(var(--secondary))',
    				foreground: 'hsl(var(--secondary-foreground))'
    			},
    			tertiary: '#1952BB',
    			background: 'hsl(var(--background))',
    			foreground: 'hsl(var(--foreground))',
    			card: {
    				DEFAULT: 'hsl(var(--card))',
    				foreground: 'hsl(var(--card-foreground))'
    			},
    			popover: {
    				DEFAULT: 'hsl(var(--popover))',
    				foreground: 'hsl(var(--popover-foreground))'
    			},
    			muted: {
    				DEFAULT: 'hsl(var(--muted))',
    				foreground: 'hsl(var(--muted-foreground))'
    			},
    			accent: {
    				DEFAULT: 'hsl(var(--accent))',
    				foreground: 'hsl(var(--accent-foreground))'
    			},
    			destructive: {
    				DEFAULT: 'hsl(var(--destructive))',
    				foreground: 'hsl(var(--destructive-foreground))'
    			},
    			border: 'hsl(var(--border))',
    			input: 'hsl(var(--input))',
    			ring: 'hsl(var(--ring))',
    			chart: {
    				'1': 'hsl(var(--chart-1))',
    				'2': 'hsl(var(--chart-2))',
    				'3': 'hsl(var(--chart-3))',
    				'4': 'hsl(var(--chart-4))',
    				'5': 'hsl(var(--chart-5))'
    			},
    			sidebar: {
    				DEFAULT: 'hsl(var(--sidebar-background))',
    				foreground: 'hsl(var(--sidebar-foreground))',
    				primary: 'hsl(var(--sidebar-primary))',
    				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
    				accent: 'hsl(var(--sidebar-accent))',
    				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
    				border: 'hsl(var(--sidebar-border))',
    				ring: 'hsl(var(--sidebar-ring))'
    			}
    		},
    		fontFamily: {
    			inter: [
    				'var(--font-inter)'
    			]
    		},
    		borderRadius: {
    			lg: 'var(--radius)',
    			md: 'calc(var(--radius) - 2px)',
    			sm: 'calc(var(--radius) - 4px)'
    		},
    		dropShadow: {
    			'3xl': '0 1px 2px rgba(16, 24, 40, 0.05)',
    			'4xl': '4px 4px 15px rgba(55, 125, 255, 0.1)',
    			'5xl': '0px 1px 4px rgba(0, 0, 0, 0.05)',
    			'6xl': '0 1px 4px rgba(0, 0, 0, 0.05)',
    			'7xl': '0 2px 6px rgba(87, 88, 94, 0.12)',
    			'8xl': '0px 1px 4px rgba(0, 0, 0, 0.05)',
    			'9xl': '0 2px 4px rgba(0,0,0,0.1)',
    			'10xl': '0px 1px 2px rgba(25, 82, 187, 0.25)'
    		},
    		keyframes: {
    			'accordion-down': {
    				from: {
    					height: '0'
    				},
    				to: {
    					height: 'var(--radix-accordion-content-height)'
    				}
    			},
    			'accordion-up': {
    				from: {
    					height: 'var(--radix-accordion-content-height)'
    				},
    				to: {
    					height: '0'
    				}
    			}
    		},
    		animation: {
    			'accordion-down': 'accordion-down 0.2s ease-out',
    			'accordion-up': 'accordion-up 0.2s ease-out'
    		},
    		zIndex: {
    			'35': '35',
    			'45': '45',
    		}
    	}
    },
    plugins: [
		tailwindcssAnimate,
		require('tailwind-scrollbar')({ 
			preferredStrategy: 'pseudoelements' 
			// nocompatible: true
		}),
		
	],
} satisfies Config;
