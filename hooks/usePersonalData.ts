import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useSaveStudentMutation, useGetStudentQuery, StudentData } from '@/lib/redux/api/studentApi';
import { setStudentId, setCurrentStudent } from '@/lib/redux/slices/studentSlice';
import { transformFormDataToStudentData, validateStudentData } from '@/lib/utils/studentDataTransform';
import { useToast } from '@/hooks/use-toast';
import { FormData } from '@/types';

export const usePersonalData = (studentId?: string | number | null) => {
  const dispatch = useDispatch();
  const [saveStudent, { isLoading: isSaving }] = useSaveStudentMutation();
  const { toast } = useToast();

  // Get student data using the studentId
  const { 
    data: studentData, 
    isLoading: isFetching, 
    error: fetchError,
    refetch 
  } = useGetStudentQuery(studentId as string | number, {
    skip: !studentId, // Skip the query if no studentId
  });

  const savePersonalData = useCallback(async (formData: FormData) => {
    try {
      // Transform and validate form data
      const studentData = transformFormDataToStudentData(formData);
      const validationErrors = validateStudentData(studentData);

      if (validationErrors.length > 0) {
        toast({
          title: "Validation Error",
          description: validationErrors.join(', '),
          variant: "destructive",
        });
        return { success: false, error: validationErrors.join(', ') };
      }

      // Submit to API
      const result = await saveStudent(studentData).unwrap() as any;

      if (result.success && result.data?.student?.studentId) {
        // Only store the student ID in Redux, not the full data
        dispatch(setStudentId(result.data.student.studentId));

        toast({
          title: "Success",
          description: "Student information saved successfully!",
        });

        return {
          success: true,
          studentId: result.data.student.studentId,
          data: result.data
        };
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to save student information",
          variant: "destructive",
        });
        return { success: false, error: result.message };
      }
    } catch (error: any) {
      console.error('Error saving student:', error);
      const errorMessage = error?.data?.message || "An unexpected error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { success: false, error: errorMessage };
    }
  }, [saveStudent, dispatch, toast]);

  const fetchPersonalData = useCallback(async () => {
    if (!studentId) {
      return { success: false, error: "No student ID available" };
    }

    try {
      const result = await refetch();
      const apiResponse = result.data as any;
      if (apiResponse?.success && apiResponse?.data?.student?.personalInfo) {
        // Extract personalInfo and transform to match StudentData interface
        const personalInfo = apiResponse.data.student.personalInfo;
        const studentData: StudentData = {
          studentId: apiResponse.data.student.studentId,
          firstName: personalInfo.firstName,
          lastName: personalInfo.lastName,
          nameInNative: personalInfo.nameInNative,
          email: personalInfo.email,
          phone: personalInfo.phone,
          guardianPhone: personalInfo.guardianPhone,
          dateOfBirth: personalInfo.dateOfBirth,
          gender: personalInfo.gender,
          fatherName: personalInfo.fatherName,
          motherName: personalInfo.motherName,
          nid: personalInfo.nid,
          passport: personalInfo.passport,
          presentAddress: personalInfo.presentAddress,
          permanentAddress: personalInfo.permanentAddress,
          maritalStatus: personalInfo.maritalStatus,
          sponsor: personalInfo.sponsor,
          emergencyContact: personalInfo.emergencyContact,
          preferredSubject: personalInfo.preferredSubject,
          preferredCountry: personalInfo.preferredCountry,
          socialLinks: personalInfo.socialLinks,
          reference: personalInfo.reference,
          note: personalInfo.note,
        };

        // Store the transformed student data in Redux for display purposes
        dispatch(setCurrentStudent(studentData));
        return { success: true, data: studentData };
      } else {
        return { success: false, error: apiResponse?.message || "Failed to fetch student data" };
      }
    } catch (error: any) {
      console.error('Error fetching student:', error);
      const errorMessage = error?.data?.message || "Failed to fetch student data";
      return { success: false, error: errorMessage };
    }
  }, [studentId, refetch, dispatch]);

  return {
    savePersonalData,
    fetchPersonalData,
    studentData: studentData?.data || null,
    isSaving,
    isFetching,
    fetchError,
    isLoading: isSaving || isFetching,
  };
};
