import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useSaveApplicationsMutation, useGetApplicationsQuery } from '@/lib/redux/api/studentApi';
import { setApplicationData } from '@/lib/redux/slices/studentSlice';
import { useToast } from '@/hooks/use-toast';

interface ApplicationFormData {
  applications: Array<{
    university: string;
    country: string;
    program: string;
    campus: string;
    intake: string;
    course: string;
    note: string;
  }>;
}

export const useApplicationData = (studentId?: string | number | null) => {
  const dispatch = useDispatch();
  const [saveApplications, { isLoading: isSaving }] = useSaveApplicationsMutation();
  const { toast } = useToast();

  // Get application data using the studentId
  const { 
    data: applicationData, 
    isLoading: isFetching, 
    error: fetchError,
    refetch 
  } = useGetApplicationsQuery(studentId as string | number, {
    skip: !studentId, // Skip the query if no studentId
  });

  const saveApplicationData = useCallback(async (formData: ApplicationFormData) => {
    try {
      if (!studentId) {
        toast({
          title: "Error",
          description: "Student ID is required",
          variant: "destructive",
        });
        return { success: false, error: "Student ID is required" };
      }

      // Transform form data to API format
      const applications = formData.applications.map((app: any) => ({
        universityId: parseInt(app.university) || 0,
        universityCountryId: parseInt(app.country) || 0,
        universityCountryCampus: parseInt(app.campus) || 0,
        programId: parseInt(app.program) || 0,
        intakeId: parseInt(app.intake) || 0,
        courseId: parseInt(app.course) || 0,
        note: app.note || '',
        status: 'applied',
        paymentStatus: 'unpaid',
        applicationType: 'undergraduate',
        applicationId: `APP-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        currentStage: 'collecting_documents',
        overallProgress: 0,
        totalAmount: 500.00,
        deliveryMethod: 'online',
        deadlineDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
      }));

      const payload = {
        studentId: studentId.toString(),
        applications: applications
      };

      // Submit to API
      const result = await saveApplications(payload).unwrap();
      console.log('result in use application', result);
      
      
      if (result.status === 'success') {
        // Store the application data in Redux
        dispatch(setApplicationData(applications));

        toast({
          title: "Success",
          description: "Applications saved successfully!",
        });
        
        return { 
          success: true, 
          data: result.data 
        };
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to save applications",
          variant: "destructive",
        });
        return { success: false, error: result.message };
      }
    } catch (error: any) {
      console.error('Error saving applications:', error);
      const errorMessage = error?.data?.message || "An unexpected error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { success: false, error: errorMessage };
    }
  }, [saveApplications, dispatch, toast, studentId]);

  const fetchApplicationData = useCallback(async () => {
    if (!studentId) {
      return { success: false, error: "No student ID available" };
    }

    try {
      const result = await refetch();
      if (result.data?.success && result.data?.data) {
        // Store the application data in Redux
        dispatch(setApplicationData(result.data.data));
        return { success: true, data: result.data.data };
      } else {
        return { success: false, error: result.data?.message || "Failed to fetch application data" };
      }
    } catch (error: any) {
      console.error('Error fetching applications:', error);
      const errorMessage = error?.data?.message || "Failed to fetch application data";
      return { success: false, error: errorMessage };
    }
  }, [studentId, refetch, dispatch]);

  return {
    saveApplicationData,
    fetchApplicationData,
    applicationData: applicationData?.data || null,
    isSaving,
    isFetching,
    fetchError,
    isLoading: isSaving || isFetching,
  };
};
