import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useSaveEducationMutation } from '@/lib/redux/api/studentApi';
import { setEducationData } from '@/lib/redux/slices/studentSlice';
import { useToast } from '@/hooks/use-toast';
import { EducationFormData } from '@/types';

export const useEducationSave = () => {
  const dispatch = useDispatch();
  const [saveEducation, { isLoading }] = useSaveEducationMutation();
  const { toast } = useToast();

  // Transform form data to API format
  const transformToApiFormat = (formData: EducationFormData) => {
    const formatDate = (date: Date | string) => {
      if (!date) return '';
      if (typeof date === 'string') return date;
      return date.toISOString().split('T')[0];
    };

    return {
      academic: formData.academic.map(item => ({
        ...item,
      })),
      proficiency: formData.proficiency.map(item => ({
        nameOfExam: item.nameOfExam,
        score: {
          overall: Number(item.overall) || 0,
          R: Number(item.R) || 0,
          L: Number(item.L) || 0,
          W: Number(item.W) || 0,
          S: Number(item.S) || 0,
        },
        examDate: formatDate(item.examDate),
        expiryDate: formatDate(item.expiryDate),
        note: item.note || ''
      })),
      publications: formData.publications.map(item => ({
        ...item,
        publicationDate: formatDate(item.publicationDate)
      })),
      otherActivities: formData.otherActivities.map(item => ({
        ...item,
        startDate: formatDate(item.startDate),
        endDate: formatDate(item.endDate)
      }))
    };
  };

  const saveEducationData = useCallback(async (
    studentId: string | number,
    formData: EducationFormData
  ) => {
    try {
      if (!studentId) {
        toast({
          title: "Error",
          description: "Student ID is required",
          variant: "destructive",
        });
        return { success: false, error: "Student ID is required" };
      }

      const apiData = transformToApiFormat(formData);
      const result = await saveEducation({
        studentId: studentId,
        educationData: apiData
      }).unwrap();
      console.log('result in use education', result);
      

      if (result.success) {
        // Store education data in Redux
        if (result.data) {
          dispatch(setEducationData(result.data));
        }

        toast({
          title: "Success",
          description: "Education information saved successfully!",
        });

        return { success: true, data: result.data };
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to save education information",
          variant: "destructive",
        });
        return { success: false, message: result.message };
      }
    } catch (error: any) {
      console.error('Error saving education:', error);
      const errorMessage = error?.data?.message || "Failed to save education information";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { success: false, error: errorMessage };
    }
  }, [saveEducation, toast, dispatch]);

  return {
    saveEducationData,
    isLoading,
  };
};
