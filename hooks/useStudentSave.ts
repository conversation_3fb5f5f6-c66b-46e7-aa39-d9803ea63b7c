import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useSaveStudentMutation } from '@/lib/redux/api/studentApi';
import { setCurrentStudent } from '@/lib/redux/slices/studentSlice';
import { transformFormDataToStudentData, validateStudentData } from '@/lib/utils/studentDataTransform';
import { useToast } from '@/hooks/use-toast';
import { FormData } from '@/types';

export const useStudentSave = () => {
  const dispatch = useDispatch();
  const [saveStudent, { isLoading }] = useSaveStudentMutation();
  const { toast } = useToast();

  const saveStudentData = useCallback(async (formData: FormData) => {
    try {
      // Transform form data to API format
      const studentData = transformFormDataToStudentData(formData);
      
      // Validate the data
      const validationErrors = validateStudentData(studentData);
      if (validationErrors.length > 0) {
        toast({
          title: "Validation Error",
          description: validationErrors.join(', '),
          variant: "destructive",
        });
        return { success: false, errors: validationErrors };
      }

      // Submit to API
      const result = await saveStudent(studentData).unwrap();
      
      if (result.success) {
        // Update Redux store with the saved student data
        if (result.data) {
          dispatch(setCurrentStudent(result.data));
        }

        toast({
          title: "Success",
          description: "Student information saved successfully!",
        });
        return { success: true, data: result.data };
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to save student information",
          variant: "destructive",
        });
        return { success: false, message: result.message };
      }
    } catch (error: any) {
      console.error('Error saving student:', error);
      const errorMessage = error?.data?.message || "An unexpected error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { success: false, error: errorMessage };
    }
  }, [saveStudent, toast, dispatch]);

  return {
    saveStudentData,
    isLoading,
  };
};
