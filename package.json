{"name": "apply-goal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@shadcn/ui": "^0.0.4", "@tanstack/query-sync-storage-persister": "^5.76.0", "@tanstack/react-query": "^5.76.0", "@tanstack/react-query-persist-client": "^5.76.0", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-bullet-list": "^2.11.0", "@tiptap/extension-hard-break": "^2.11.5", "@tiptap/extension-heading": "^2.11.0", "@tiptap/extension-link": "^2.11.0", "@tiptap/extension-list-item": "^2.11.0", "@tiptap/extension-ordered-list": "^2.11.0", "@tiptap/extension-placeholder": "^2.11.0", "@tiptap/extension-subscript": "^2.11.0", "@tiptap/extension-superscript": "^2.11.0", "@tiptap/pm": "^2.11.0", "@tiptap/react": "^2.11.0", "@tiptap/starter-kit": "^2.11.0", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "country-state-city": "^3.2.1", "date-fns": "^3.6.0", "js-cookie": "^3.0.5", "lucide-react": "^0.460.0", "next": "15.0.3", "next-auth": "^5.0.0-beta.27", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-pdf": "^9.1.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "recharts": "^2.13.3", "redux-persist": "^6.0.0", "swiper": "^11.2.4", "tailwind-merge": "^2.5.4", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.1", "world-countries": "^5.0.0", "zod": "^3.23.8"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}