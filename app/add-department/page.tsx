'use client'

import React, { useState } from 'react';
import Heading from '../components/Heading';
import TreeItem from '../components/ui/TreeItem';
import InputField from '../components/InputField';
import DashboardLayout from '../components/layout/DashboardLayout';
import NoDepartmentPreview from '../assets/svg/NoDepartmentPreview';
import SelectAndSearchCombobox from '../components/SelectAndSearchCombobox';
import { useGetDepartmentsQuery, usePostDepartmentsMutation } from '@/lib/redux/api/departmentApi';
import Image from 'next/image';
import Loader from '@/app/assets/Loader.gif'

// TypeScript interface for department structure
interface Department {
    id: number;
    name: string;
    parentId?: number;
    children?: Department[];
}


const page = () => {
    const [departmentName, setDepartmentName] = useState('');
    const [parentDepartment, setParentDepartment] = useState<string | null>(null);
    const { data, isLoading, error } = useGetDepartmentsQuery();
    const [postDepartments, { isLoading: isPosting }] = usePostDepartmentsMutation();
    const [message, setMessage] = useState<string | null>(null);
      if (isLoading) {
        return (
            <DashboardLayout>
                <div className="flex flex-col justify-center items-center h-64">
                    <Image 
                        src={Loader}
                        alt="Loading" 
                        // width={150}
                        // height={150}
                        className="mb-2"
                    />
                    {/* <div className="text-lg text-grayFive">Loading...</div> */}
                </div>
            </DashboardLayout>
        );
    }

    // Error state
    if (error) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg text-red-500">
                        Error loading roles: {JSON.stringify(error)}
                    </div>
                </div>
            </DashboardLayout>
        );
    }
    if (!data?.departments) return <div>No departments found</div>;

    // Helper to flatten department tree
    const flattenDepartments = (departments: Department[]): { label: string; value: string }[] => {
        let result: { label: string; value: string }[] = [];
        for (const dept of departments) {
            result.push({ label: dept.name, value: dept.name });
            if (dept.children && dept.children.length > 0) {
                result = result.concat(flattenDepartments(dept.children));
            }
        }
        return result;
    };

    // Prepare options for parent department select (all departments, including children)
    const parentOptions = flattenDepartments(data.departments);
    const renderTreeItems = (items: Department[], depth = 0): React.ReactNode =>
        items && items.map((item: Department, idx: number) => (
            <TreeItem
                key={`${item.id}-${depth}-${idx}`}
                label={item.name}
                defaultOpen={false}
                depth={depth}
            >
                {item.children && renderTreeItems(item.children, depth + 1)}
            </TreeItem>
        ));

    // Handle form submit
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setMessage(null);
        if (!departmentName) {
            setMessage('Department name is required.');
            return;
        }
        try {
            const body = [{ name: departmentName, parent: parentDepartment || null }];
            const res = await postDepartments(body).unwrap();
            setMessage(res.message || 'Department added!');
            setDepartmentName('');
            setParentDepartment(null);
        } catch (err: any) {
            setMessage('Failed to add department.');
        }
    };

    return (
        <DashboardLayout>
            {isLoading && <div>Loading...</div>}
            {error && <div>Error loading departments</div>}
            {!data?.departments && <div>No departments found</div>}
            <Heading level='h1'>
                Add Department/Team
            </Heading>
            <div className='bg-white rounded-[20px] p-[30px] mt-5'>
                <div className='grid grid-cols-2 gap-6'>
                    <form className='space-y-[30px]' onSubmit={handleSubmit}>
                        <div>
                            <InputField 
                                id='department-name' 
                                label='Department/Team Name' 
                                placeholder='Enter department/team name' 
                                className='w-full'
                                value={departmentName}
                                onChange={e => setDepartmentName(e.target.value)}
                            />
                        </div>
                        <SelectAndSearchCombobox
                            label='Parent Department/Team Name'
                            placeholder='Select'
                            options={parentOptions}
                            selectedValue={parentDepartment || ''}
                            onChange={setParentDepartment}
                            className='w-full'
                        />
                        <div className='flex gap-4 justify-end'>
                            <button type='button' className='rounded-full border border-grayFour py-2.5 px-4 font-semibold text-sm leading-5 text-grayFour' onClick={() => { setDepartmentName(''); setParentDepartment(null); setMessage(null); }}>Cancel</button>
                            <button type='submit' className='rounded-full py-2.5 px-6 bg-primaryColor text-white font-semibold text-sm leading-5' disabled={isPosting}>
                                {isPosting ? 'Saving...' : 'Save'}
                            </button> 
                        </div>
                    </form>
                    <div className='rounded-[10px] border p-5 border-[#1952BB33] w-full'>
                        <p className='font-medium text-sm leading-5 text-grayFive border-b border-[#1952BB33]'>Preview</p>
                        <div className='mt-5 pl-12 mb-5 overflow-y-auto max-h-[600px] '>
                            { data?.departments && data?.departments.length === 0 ? (
                                <div className='flex flex-col items-center justify-center gap-2'>
                                    <NoDepartmentPreview />
                                    <p className='font-normal text-base leading-6 text-[#144296] py-5'>No Preview Available</p>
                                </div>
                            ) : (
                                renderTreeItems(data?.departments, 0)
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page
