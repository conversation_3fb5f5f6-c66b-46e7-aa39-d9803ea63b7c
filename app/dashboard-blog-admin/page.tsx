import React from 'react';
import StatCardLayout from '../components/StatCardLayout';
// import DateRangeSelect from '../components/DateRangeSelect';
// import Applications from '@/app/assets/svg/Applications.svg';
import StatInfoCardSmall from '../components/StatInfoCardSmall';
import VerticalTabLayout from '../components/VerticalTabLayout';
import DashboardLayout from '../components/layout/DashboardLayout';
import { SuperAdminStatCardData } from '@/common';

const totalApplications = 0

const page = () => {
    const general = () => {
        return (
            <div className='py-5'>
                <div className='grid md:grid-cols-4 gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value}
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                        ))}
                </div>
            </div>
        )
    };

    const blog = () => {
        return (
            <div className='py-5'>
                <div className='grid md:grid-cols-4 gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value} 
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                        ))}
                </div>
            </div>
        )
    };

    const tabContentsBlogs = [
        {
            value: 'general',
            label: 'General',
            content: general
        },
        {
            value: 'blog',
            label: 'Blog',
            content: blog
        }
    ];

    const tabContents = tabContentsBlogs.map((tab) => ({
        value: tab.value,
        children: <tab.content />,
    }));

    return (
        <DashboardLayout>
            <div className='mt-10'>
                <h1 className='font-bold text-[28px] text-[#36373B]'>Welcome Flora!</h1>
                <div className='text-primaryColor md:text-sm text-[10px] md:leading-[17px] leading-3 font-semibold'>
                    {/* <DateRangeSelect
                        options={options}
                        selectedValue={selectedRange}
                        onChange={(value) => setSelectedRange(value as string)}
                        placeholder="Select Time Range"
                    /> */}
                </div>
            </div>

            <div className='mt-10'>
                <VerticalTabLayout 
                    tabMenu={tabContentsBlogs}
                    tabContents={tabContents}
                    defaultValue='general'
                    tabListClassName='!p-0 max-w-[1193px] md:!justify-start'
                    tabContentClassName = 'pb-[96px]'
                    tabTriggerClassName=''
                />
            </div>

        </DashboardLayout>
    )
}

export default page