"use client"

import Image from 'next/image';
import React, { useState } from 'react';
import Heading from '../../components/Heading';
import Cross from '@/app/assets/svg/Cross';
import InputField from '../../components/InputField';
import Distance from '@/app/assets/svg/distance';
import Westcliff from '@/app/assets/img/westcliff.png';
import DropDownButton from '../../components/DropDownButton';
import SectionLayout from '../../components/layout/SectionLayout';
import DashboardLayout from '../../components/layout/DashboardLayout';
import AddApplication from '@/app/assets/svg/add-application.svg';
import { courseData as initialCourseData, courseTableRows } from '@/common';

const Page = () => {
    const [courseData, setCourseData] = useState(initialCourseData);

    const handleRemoveColumn = (indexToRemove: number) => {
        setCourseData(courseData.filter((_, index) => index !== indexToRemove));
    };

    return (
        <DashboardLayout>
            <div className='pb-5 pt-10 flex justify-between'>
                <Heading level='h1'>
                    Compare Courses
                </Heading>
                <div>
                    <DropDownButton />
                </div>
            </div>
            <div className='pb-[101px]'>
                <SectionLayout>
                    <div 
                        className='
                        [&::-webkit-scrollbar-track]:rounded-full 
                        [&::-webkit-scrollbar-thumb]:rounded-full 
                        [&::-webkit-scrollbar]:h-4
                        scrollbar 
                        scrollbar-thumb-grayTwo 
                        scrollbar-track-grayOne 
                        overflow-x-auto 
                        rounded-lg border 
                        border-grayOne'
                    >
                        <table className='w-full table-auto'>
                            <thead>
                                <tr className=''>
                                    <th className='border-grayOne border-r'></th>
                                    <th className='p-4 border-r border-grayOne'>
                                        <InputField 
                                            id='first_name'
                                            placeholder='Search' 
                                            type='text' 
                                            // register={register('first_name', { required: 'First name is required' })}
                                            label='Compare with' 
                                            className='border-grayOne border-opacity-none'
                                        />
                                    </th>
                                    <th className='p-4 border-r border-grayOne'>
                                        <InputField 
                                            id='first_name'
                                            placeholder='Search' 
                                            type='text' 
                                            // register={register('first_name', { required: 'First name is required' })}
                                            label='Compare with' 
                                            className='border-grayOne border-opacity-none'
                                        />
                                    </th>
                                    <th className='p-4 border-grayOne'>
                                        <InputField 
                                            id='first_name'
                                            placeholder='Search' 
                                            type='text' 
                                            // register={register('first_name', { required: 'First name is required' })}
                                            label='Compare with' 
                                            className='border-grayOne border-opacity-none'
                                        />
                                    </th>
                                </tr>
                                <tr className='border-t border-grayOne'>
                                    <th className='w-[20%] py-2 border-r border-grayOne font-medium text-xl text-graySix'>
                                        University and Requirements
                                    </th>
                                    {courseData.map((item, index) => (
                                        <th
                                            key={index}
                                            className='w-[26%] border-r px-6 py-5  border-l border-grayOne'
                                        >
                                            <div className='flex justify-end'>
                                                <button onClick={() => handleRemoveColumn(index)}>
                                                    <Cross className='cursor-pointer' />
                                                </button>
                                            </div>
                                            <div className='flex gap-3 items-center'>
                                                <div>
                                                    <Image src={Westcliff} alt='University Logo' />
                                                </div>
                                                <div className='flex flex-col text-left'>
                                                    <p className='font-medium text-base leading-5 text-graySix'>
                                                        {item.university}
                                                    </p>
                                                    <p className='pt-2 flex gap-1.5 font-normal leading-4 text-xs text-grayFour'>
                                                        <Distance />
                                                        {item.location}
                                                    </p>
                                                </div>
                                            </div>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {courseTableRows.map((row, rowIndex) => (
                                    <tr key={rowIndex}>
                                        <td className='min-w-[200px] border-r font-medium text-graySix tracking-[0.25px] text-sm px-6 py-3.5 border-t border-grayOne'>
                                            {row.label}
                                        </td>
                                        {courseData.map((item, colIndex) => (
                                            <td
                                                key={colIndex}
                                                className='font-normal border-r text-graySix tracking-[0.25px] text-xs px-6 py-3.5 border-l border-t border-grayOne'
                                            >
                                                {['gre', 'ielts', 'toefl'].includes(row.key) ? (
                                                    <div className='flex flex-wrap space-x-4'>
                                                        {item[row.key]
                                                            .split(' ')
                                                            .map((part, idx) => (
                                                                <span key={idx} className='inline-block'>
                                                                    {part}
                                                                </span>
                                                            ))}
                                                    </div>
                                                ) : (
                                                    item[row.key]
                                                )}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                                <tr>
                                    <td className='px-6 py-5 border-r border-t border-grayOne'></td>
                                    {courseData.map((_, index) => (
                                        <td
                                            key={index}
                                            className='px-6 py-5 border-r border-t border-grayOne text-center'
                                        >
                                            <button className='font-semibold text-sm leading-4 flex gap-3 items-center w-full justify-center border border-primaryColor text-primaryColor px-4 py-2.5 rounded-md'>
                                                <Image src={AddApplication} alt='Add application button' />
                                                Add Application
                                            </button>
                                        </td>
                                    ))}
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </SectionLayout>
            </div>
        </DashboardLayout>
    )
}

export default Page