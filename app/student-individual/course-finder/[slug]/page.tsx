import React from 'react';
import Image from 'next/image';
import { courses, CourseAccordionData } from '@/common';
import { Button } from '@/components/ui/button';
import ArrowUp from '@/app/assets/svg/arrowUp';
import { Separator } from '@/components/ui/separator';
import ArrowDown from '@/app/assets/svg/arrowDown';
import UniversityCover from '@/app/assets/img/university-cover.png';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import CourseAccordion from '@/app/components/CourseAccordion';

export async function generateStaticParams() {
    return courses.map((course) => ({
        slug: course.slug,
    }));
}

interface PageProps {
    params: Promise<{ slug: string }>;
}

export default async function Page({ params }: PageProps) {
    const { slug } = await params;
    const course = courses.find((course) => course.slug === slug);

    if (!course) {
        return <div>Course not found</div>;
    }

    return (
        <DashboardLayout>
            <section className='mt-10 relative overflow-hidden'>
                <div className='absolute inset-0 -z-10'>
                    <Image
                        src={UniversityCover}
                        alt='Meet the team Image'
                        className='object-cover rounded-[11px]'
                        quality={75}
                        fill
                    />
                </div>

                <div className='absolute bg-[#1E62E0] bg-opacity-30 inset-0 -z-10 rounded-[11px]'></div>

                <div className='gap-[30px] md:gap-0 flex flex-col md:flex-row justify-between relative z-10 px-6 py-4 md:py-12 items-center'>
                    <div className='flex flex-col text-white'>
                        <Image
                            src={course.universityLogo}
                            alt='Meet the team Image'
                            className='object-cover'
                            quality={75}
                            width={36}
                            height={36}
                        />
                        <h2 className='font-bold text-xl md:text-2xl'>{course.program}</h2>
                        <span className='font-medium text-xs md:text-sm leading-4'>{course.university}</span>
                    </div>
                    <div className='flex flex-col gap-2 rounded-md py-3 px-4 bg-white'>
                        <h3 className='font-semibold text-xs text-graySix'>Winter 2025 applications are now open.</h3>
                        <p className='font-normal text-[10px] text-grayFive'>Get started today or request more info about the MADS degree.</p>
                        <Button className='font-semibold text-[10px] hover:bg-primaryColor bg-primaryColor' variant= 'destructive' >Apply Now</Button>
                        <Button className='border-[0.5px] border-primaryColor font-semibold text-[10px] hover:text-primaryColor text-primaryColor hover:bg-white' variant= 'outline'>Request Info</Button>
                    </div>
                </div>
            </section>

            <section className='space-y-5 pb-20'>
                <h1 className='mt-5 font-semibold text-2xl text-graySix leading-[30px]'>Course Overview</h1>
                <div className='rounded-[16px] border bg-white border-[#1E62E0] border-opacity-20'>
                    <h2 className='p-4 md:p-6 font-semibold text-xl leading-5 text-graySix'>{course.program}</h2>
                    <Separator className='' />

                    <div className='grid grid-cols-1 md:grid-cols-3 gap-4 p-4 md:p-6 items-center'>
                        <div className='space-y-5'>
                            <p className='text-base text-grayFive leading-5 font-medium'>
                            <span className='font-normal'>Tuition Fees:</span> $1325.0 - $2678.0 CAD / Year
                            </p>
                            <p className='text-base text-grayFive leading-5 font-medium'>
                            <span className='font-normal'>Application Fees:</span> $132.0 CAD
                            </p>
                            <p className='text-base text-grayFive leading-5 font-medium'>
                            <span className='font-normal'>Location:</span> Ontario, Canada
                            </p>
                        </div>

                        <div className='space-y-5'>
                            <p className='text-base text-grayFive leading-5 font-medium'>
                            <span className='font-normal'>Intakes and Submissions:</span> Jan, May, Sept
                            </p>
                            <p className='text-base text-grayFive leading-5 font-medium'>
                            <span className='font-normal'>Program:</span> 2 year Master Degree
                            </p>
                            <p className='text-base text-grayFive leading-5 font-medium'>
                            <span className='font-normal'>Campus City:</span> New Westminster
                            </p>
                        </div>

                        <div className='mt-8 md:mt-0 flex flex-col md:flex-row gap-6 md:gap-11'>
                            <div className='text-center'>
                                <p className='font-semibold text-base leading-[18px] text-grayFive'>Course Rank</p>
                                <p className='mt-2.5 font-semibold text-4xl leading-[43px] text-primaryColor'>124</p>
                            </div>
                            <div className='text-center'>
                                <p className='font-semibold text-base leading-[18px] text-grayFive'>Acceptance Rate</p>
                                <p className='mt-2.5 font-semibold text-4xl leading-[43px] text-primaryColor'>65%</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='rounded-[16px] border bg-white border-[#1E62E0] border-opacity-20'>
                    <h2 className='p-4 md:p-6 font-semibold text-xl leading-5 text-graySix'>Standardized Test Requirements</h2>
                    {/* <h2 className='p-4 md:p-6 font-semibold text-xl leading-5 text-graySix'>{course.program}</h2> */}
                    <Separator className='' />
                    <div className='grid grid-cols-1 md:grid-cols-4 gap-4 p-4 md:p-6'>
                        <div className='space-y-[30px]'>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>IELTS Overall</span> 
                                6.5
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>PTE Overall</span> 
                                71
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>GPA/ CGPA/ Division</span> 
                                2.5
                            </p>
                        </div>

                        <div className='space-y-[30px]'>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>IELTS No Band Less Than</span> 
                                6.0
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>PTE No Band Less Than</span> 
                                65
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>SAT</span> 
                                1350
                            </p>
                        </div>

                        <div className='space-y-[30px]'>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>TOEFL Overall</span> 
                                87
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>Duolingo Overall</span> 
                                87
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>ACT</span> 
                                31
                            </p>
                        </div>
                        <div className='space-y-[30px]'>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>TOEFL iBT No Band Less Than</span> 
                                19
                            </p>
                            <p className='text-base text-grayFive leading-5 font-semibold flex flex-col'>
                                <span className='pb-2.5 font-normal'>GRE/ GMAT</span> 
                                124
                            </p>
                        </div>
                    </div>
                </div>

                {/* <div className='rounded-[16px] border bg-white border-[#1E62E0] border-opacity-20'>
                    <h2 className='p-4 md:p-6 font-semibold text-xl leading-5 text-graySix'>Entry Requirements</h2>
                    <Separator className='' />
                    <p className='p-4 md:p-6 font-normal text-base text-grayFive text-center'>No Information</p>
                </div> */}

                <div className='rounded-[16px] border bg-white border-[#1E62E0] border-opacity-20'>
                    <h2 className='p-4 md:p-6 font-semibold text-xl leading-5 text-graySix'>Entry Requirements</h2>
                    <Separator className='' />
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-10 p-4 md:p-6'>
                        <div className='space-y-2'>
                            <h3 className='font-semibold text-base text-grayFive'>The University of Michigan's entry requirements include:</h3>
                            <p className='font-normal text-base leading-6 text-grayFive'>1. A competitive high school GPA, typically around 3.8-4.0 (unweighted).</p>
                            <p className='font-normal text-base leading-6 text-grayFive'>2. Strong SAT (1350-1530) or ACT (31-34) scores.</p>
                            <p className='font-normal text-base leading-6 text-grayFive'>3. Completion of challenging coursework, including AP/IB classes.</p>
                            <p className='pt-5 font-normal text-base leading-6 text-grayFive'>These requirements can vary slightly by program</p>
                        </div>

                        <div className='space-y-2'>
                            <p className='font-normal text-base leading-6 text-grayFive'>4. Extracurricular involvement and leadership experience.                            </p>
                            <p className='font-normal text-base leading-6 text-grayFive'>5. Compelling personal essays and strong letters of recommendation.
                            </p>
                        </div>
                    </div>
                </div>
                <div className='rounded-[16px] border bg-white border-[#1E62E0] border-opacity-20'>
                    <h2 className='p-4 md:p-6 font-semibold text-xl leading-5 text-graySix'>Courses</h2>
                    <Separator className='' />
                    <div className='p-4 md:p-6'>
                        <div className=' gap-4'>
                            {/* {CourseAccordionData.map(( course, index ) => (
                                <div key={index} className='bg-[#F6F8FA] rounded-[16px] p-4 md:p-6'>
                                    <h3 className='font-semibold text-base text-graySix'>{course.title}</h3>
                                    <p className='font-normal text-base text-grayFive'>{course.description}</p>
                                </div>
                            ))} */}
                            <CourseAccordion data={CourseAccordionData} />
                        </div>
                    </div>
                </div>
            </section>
        </DashboardLayout>
    );
};

// export default Page;