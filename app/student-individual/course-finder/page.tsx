'use client'

import React, { useState } from 'react';
import Search from '@/app/assets/svg/search';
import { useToast } from '@/hooks/use-toast';
import Heading from '@/app/components/Heading';
import Switcher from '@/app/components/Switcher';
import { CourseFinderFiltersProps } from '@/types';
import CourseCard from '@/app/components/CourseCard';
import Pagination from '@/app/components/Pagination';
import InputWithIcon from '@/app/components/InputWithIcon';
import CoursesFilter from '@/app/components/CoursesFilter';
import  CompareButton  from '@/app/components/CompareButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { 
    courses, 
    countries, 
    programs, 
    sortByOptions, 
    intakes 
} from '@/common';

const page = () => {
    const [isChecked, setIsChecked] = useState(false);
    const [selectedCards, setSelectedCards] = useState<number[]>([]);
    const [filters, setFilters] = useState<CourseFinderFiltersProps>({
        courseTitle: '',
        program: [],
        intake: [],
        fieldOfStudy: [],
        country: '',
        tuitionFees: '',
        sortBy: [],
    });

    console.log(filters)
    const { toast } = useToast();

    const handleSelect = (id: number) => {
        setSelectedCards((prevSelectedCards) => {
            if (prevSelectedCards.includes(id)) {
                return prevSelectedCards.filter((cardId) => cardId !== id);
            } else if (prevSelectedCards.length < 4) {
                return [...prevSelectedCards, id];
            } else {
                toast({
                    description: 'Sorry, you can compare up to 04 courses!',
                    variant: 'destructive',
                });
                return prevSelectedCards;
            }
        });
    };
    
    const handleToggle = (checked: boolean) => {
        setIsChecked(checked);
        setSelectedCards([])
    };

    const filteredCourses = courses.filter((course) => {
        if (filters.courseTitle && !course.university.toLowerCase().includes(filters.courseTitle.toLowerCase())) return false;

        if (filters.program.length > 0 && !filters.program.includes(course.program)) return false;

        if (filters.intake.length > 0 && !filters.intake.includes(course.intake)) return false;

        if (filters.fieldOfStudy.length > 0 && !filters.fieldOfStudy.includes(course.programType)) return false;

        if (filters.country && !course.country.toLowerCase().includes(filters.country.toLowerCase())) return false;

        if (
            filters.tuitionFees &&
            (
                course.tuitionFees[1] < parseInt(filters.tuitionFees) ||
                course.tuitionFees[0] > parseInt(filters.tuitionFees)
            )
        )
        return false;

        return true;
    });
 
    
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    const currentCources = courses.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    const filterLists =['New York Institute of Technology - Vancouver...', 'Oct 2025', '11 weeks left', '2 days left'];

    return (
        <DashboardLayout>
            <>
            <div className='flex flex-col overflow-hidden'>
                <div className='my-5 rounded-[20px] py-[60px] text-center bg-white drop-shadow-[0_2px_2px_rgba(0, 0, 0, 0.05)]'>
                    <Heading level='h1'>
                        Discover Courses, Pursue Passion!
                    </Heading>
                    <div className='flex justify-center mt-10 px-4'>
                        <div className='max-w-[360px] w-full'>
                            <InputWithIcon 
                                icon={Search} 
                                placeholder='Search courses' 
                                className='rounded-[50px] py-2.5 border-[1px] bg-white border-[#1E62E0] border-opacity-20' 
                            />
                        </div>
                    </div>
                </div>

                <div>
                    <CoursesFilter 
                        programs={programs}
                        intakes={intakes}
                        countries={countries}
                        tuitionFees={filters.tuitionFees}
                        sortByOptions={sortByOptions}
                        onFilterChange={setFilters}
                        className='rounded-2xl border bg-white border-[#1E62E0] border-opacity-20 py-[30px] px-5'
                    />
                </div>
                <div className='flex flex-col md:flex-row gap-5 py-[22.5px] items-center'>
                    <div className='flex gap-7'>
                        <h2 className='font-semibold text-xl md:text-2xl leading-[29.05px] text-graySix'>Explore Courses</h2>
                        <div className='flex gap-3 items-center'>
                            <span className='font-normal text-base leading-6 text-grayFive'>Compare</span>
                            <Switcher   
                                isChecked={isChecked}       
                                handleToggle={handleToggle} 
                                className='border border-grayFive' 
                            />
                        </div>
                    </div>
                    <p className='font-semibold text-sm leading-4 text-primaryColor'>(Select maximum 04 courses for comparison)</p>
                </div>
                <div className='pb-[170px] grid grid-cols-1 md:grid-cols-4 gap-6'>
                    {filteredCourses.map((course) => (
                        <CourseCard 
                            key={course.id} 
                            course={course} 
                            isSelected={selectedCards.includes(course.id)}
                            onSelect={handleSelect}
                            isChecked={isChecked}
                        />
                    ))}
                </div>
                <div className='pb-[101px]'>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                    />
                </div>
            </div>
            {selectedCards.length > 0 && (
                <div className='mr-14 w-[90%] md:w-[90%] fixed bottom-0 overflow-hidden'>
                    <CompareButton selectedCourseNumber={selectedCards.length} />
                </div>
            )}
            </>
        </DashboardLayout>
    )
}

export default page