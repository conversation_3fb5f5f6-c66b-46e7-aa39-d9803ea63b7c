'use client'

import Menu from '@/app/assets/svg/menu';
import Grid from '@/app/assets/svg/grid';
import Check from '@/app/assets/svg/check';
import Canada from '@/app/assets/svg/canada';
import Search from '@/app/assets/svg/search';
import Heading from '@/app/components/Heading';
import { Button } from '@/components/ui/button';
import Switcher from '@/app/components/Switcher';
import React,{ useState, useEffect  } from 'react';
import Harvard from '@/app/assets/img/Harvard.png';
import DataTable from '@/app/components/DataTable';
import Pagination from '@/app/components/Pagination';
import FilterLists from '@/app/components/FilterLists';
import MultiSelect from '@/app/components/MultiSelect';
import SelectField from '@/app/components/SelectField';
import InputWithIcon from '@/app/components/InputWithIcon';
import UniversityCard from '@/app/components/UniversityCard';
import DropDownButton from '@/app/components/DropDownButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import SelectAndSearchCombobox from '@/app/components/SelectAndSearchCombobox';
import { 
    city, 
    fees,
    types, 
    country, 
    province, 
    sortByOptions, 
    universityList
} 
from '@/common'

const page = () => {
    const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
    const [isChecked, setIsChecked] = useState(false);
    const [selectedCards, setSelectedCards] = useState<number[]>([]);
    const [layout, setLayout] = useState(true)
    const [isClient, setIsClient] = useState(false);

    const handleToggle = (checked: boolean) => {
        setIsChecked(checked);
    };

    useEffect(() => {
        setIsClient(true);
    }, []);

    const handleSelect = (id: number) => {
        setSelectedCards((prevSelectedCards) => {
            if (prevSelectedCards.includes(id)) {
                return prevSelectedCards.filter((cardId) => cardId !== id);
            } else if (prevSelectedCards.length < 4) {
                return [...prevSelectedCards, id];
            } else {
                // console.log('you are the out of the limit')
                return prevSelectedCards;
            }
        });
    };

    const handleInputChange = (key: string, value: any) => {
        // onFilterChange((prev: any) => ({
        //   ...prev,
        //   [key]: value,
        // }));
    };
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(universityList.length / itemsPerPage);

    const currentCources = universityList.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    if (!isClient) {
        return null; // or a simple loading state
    };

    const filterLists =['New York Institute of Technology - Vancouver...', 'Oct 2025', '11 weeks left', '2 days left'];

    return (
        <DashboardLayout>
            <div className='flex justify-center rounded-[20px] bg-white py-[60px] drop-shadow-[0_2px_2px_rgba(0,0,0,0.05)]'>
                <div className='flex flex-col space-y-10'>
                    <h1 className='font-bold text-[28px] leading-none text-graySix'>Discover Universities, Define Your Path!</h1>
                    <div className='mx-20'>
                        <InputWithIcon 
                            icon={Search} 
                            placeholder='Search' 
                            className='rounded-[50px] py-2.5 border-[0.5px] bg-white border-grayTwo' 
                        />
                    </div>
                </div>
            </div>
            <div className='bg-white px-5 py-[30px] rounded-[16px] border border-[#1E62E033] drop-shadow-[0_2px_2px_rgba(30,98,224,0.1)] mt-5'>
                <div className='grid grid-cols-1 md:grid-cols-6 gap-6'>
                    <div>
                        <MultiSelect
                            options={types}
                            selectedValues={selectedOptions}
                            onChange={(values: any) => setSelectedOptions(values)}
                            label='Type'
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={country}
                            selectedValues={selectedOptions}
                            onChange={(values: any) => setSelectedOptions(values)}
                            label='Country'
                        />
                    </div>
                    <div>
                        <SelectAndSearchCombobox 
                            options={province} 
                            label='Province'
                            type='select'
                        />
                    </div>
                    <div>
                        <SelectAndSearchCombobox 
                            options={city} 
                            label='City'
                            type='select'
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={fees}
                            selectedValues={selectedOptions}
                            onChange={(values: any) => setSelectedOptions(values)}
                            label='Fees'
                        />
                    </div>
                    <div>
                        <SelectField 
                            label='Sort by'
                            options={sortByOptions}
                            placeholder=''
                            onChange={(value) => handleInputChange('sortBy', value)}
                        />
                    </div>
                </div>
                <div className='mt-5'>
                    <FilterLists lists={filterLists} />
                </div>
            </div>
            <div className='flex flex-col md:flex-row justify-between pt-10 pb-5'>
                <div className='flex gap-7'>
                    <h2 className='font-semibold text-xl md:text-2xl leading-[29.05px] text-graySix'>All Universities</h2>
                    <div className='flex gap-3 items-center'>
                        <span className='font-normal text-base leading-6 text-grayFive'>Compare</span>
                        <Switcher 
                            isChecked={isChecked} 
                            handleToggle={handleToggle} 
                            className='border border-grayFive' 
                        />
                    </div>
                </div>
                <div className='flex items-center gap-5 md:mt-0 mt-5'>
                    <div className='flex h-8 border-[0.5px] border-grayFive rounded-[20px]'>
                        <Button 
                            className={`py-0 w-[60px] border-r rounded-l-[50px] border-grayFive ${layout===true ? 'bg-white' : ''} `}
                            onClick={() => setLayout(true)}
                        >
                            {layout === true && (
                                <Check />
                            )}
                            <Menu />
                        </Button>
                        <Button 
                            className={`w-[60px] py-0 rounded-r-[50px] ${layout===false ? 'bg-white' : ''}`} 
                            onClick={() => setLayout(false)}
                        >
                            {layout === false && (
                                <Check />
                            )}
                            <Grid />
                        </Button>
                    </div>
                    <DropDownButton />
                </div>
            </div>
            <div>
                {layout ? (
                    <DataTable 
                        isChecked={isChecked} 
                        universityList={universityList} 
                    />
                ): (
                    <>
                    <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
                        {universityList.map((university, index) => (
                            // <p key={index}>{university.universityName}</p>
                            <UniversityCard 
                                key={university.id}
                                university = {university}
                                isSelected={selectedCards.includes(university.id)}
                                onSelect={handleSelect}
                                isChecked={isChecked}
                            />
                        ))}
                    </div>
                    </>
                )}
                <div className='pb-[101px] pt-16'>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                    />
            </div>
            </div>
        </DashboardLayout>
    )
}

export default page