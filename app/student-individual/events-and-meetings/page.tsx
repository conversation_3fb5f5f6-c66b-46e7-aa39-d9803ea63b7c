'use client';

import React, { useState } from 'react';
import Alarm from '@/app/assets/svg/Alarm';
import Canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import { EventandMettingsData } from '@/common';
import SideDrawer from '@/app/components/SideDrawer';
import Pagination from '@/app/components/Pagination';
import LocationOn from '@/app/assets/svg/location_on';
import AssignmentBoard from '@/app/assets/svg/AssignmentBoard';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

// interface Meeting {
//     date: {
//         day: string;
//         month: string;
//     };
//     title: string;
//     description: string;
//     time: string;
//     location?: string;
// }

const itemsPerPage = 2;

const Page = () => {
    const [currentPage, setCurrentPage] = useState(1);

    const totalPages = Math.ceil(EventandMettingsData.length / itemsPerPage);

    const currentMeetings = EventandMettingsData.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    return (
        <DashboardLayout>
            <div className="flex flex-col h-auto justify-between py-5">
                <div className="flex justify-between items-center">
                    {/* <h2 className="md:text-[28px] text-[18px] md:leading-[33px] leading-[21px] font-bold text-graySix">
                        Events & Meetings
                    </h2> */}
                    <Heading level='h1'>
                        Events & Meetings
                    </Heading>
                    <div>
                        {/* <button className="bg-primaryColor text-xs leading-[14px] text-white px-[18px] py-2 rounded-2xl hover:bg-secondaryColor">
                            Add Event
                        </button> */}
                        <SideDrawer />
                    </div>
                </div>
                <div className="flex flex-col gap-6 mt-5 mb-[50px]">
                    {currentMeetings.map((meeting, index) => (
                        <div
                            key={index}
                            className="bg-white hover:drop-shadow-9xl duration-500 rounded-xl grid md:grid-flow-col md:grid-cols-10 grid-cols-1 items-center md:pr-6"
                        >
                            <div className="bg-primaryThree md:rounded-l-xl md:rounded-tr-none rounded-tr-xl rounded-tl-xl flex flex-col items-center justify-center gap-0.5 h-full py-[22.5px] px-[38.5px] col-span-1">
                                <span className="text-4xl leading-[43px] font-bold text-secondaryColor">
                                    {meeting.date.day}
                                </span>
                                <span className="text-primaryColor font-semibold text-2xl leading-[29px]">
                                    {meeting.date.month}
                                </span>
                            </div>
                            <div className="md:py-[15px] py-[22px] md:px-[22px] px-4 md:col-span-6 col-span-1 space-y-2">
                                <h3 className="text-lg leading-[18px] font-semibold text-graySix">
                                    {meeting.title}
                                </h3>
                                <p className="text-[13px] leading-[19px] font-normal text-grayFive">
                                    {meeting.description}
                                </p>
                                {/* <span className="text-sm leading-[17px] font-normal text-grayFour">
                                    {meeting.time}
                                </span> */}
                                <div className='flex flex-wrap items-center gap-5'>
                                    <span className="text-sm leading-[17px] font-normal text-primaryColor flex items-center gap-1">
                                        <Canada /> FICC
                                    </span>
                                    <span className="text-sm leading-[17px] font-normal text-primaryColor flex items-center gap-1">
                                        <AssignmentBoard /> Kierra Franci
                                    </span>
                                    <span className="text-sm leading-[17px] font-normal text-primaryColor flex items-center gap-1">
                                        <Alarm /> {meeting.time}
                                    </span>
                                </div>
                            </div>
                            <div className="flex md:justify-end justify-start items-center md:col-span-3 col-span-1 md:pb-0 pb-5 md:px-0 px-4">
                                {meeting.location ? (
                                    <div className='flex items-center gap-2.5 max-w-[280px]'>
                                        <div className='p-[7px] bg-primaryThree rounded-full text-primaryColor'>
                                            <LocationOn />
                                        </div>
                                        <span className='font-normal text-xs leading-[18px] text-grayFive'>
                                            {meeting.location}
                                        </span>
                                    </div>
                                ) : (
                                    <button className="bg-primaryColor text-xs leading-[14px] text-white px-[18px] py-2 rounded-2xl hover:bg-secondaryColor">
                                        Join
                                    </button>
                                )}
                            </div>
                        </div>
                    ))}
                </div>

                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    );
};

export default Page;
