'use client';

import Image from 'next/image';
import React, { useState } from 'react';
import Heading from '@/app/components/Heading';
import Cross from '@/app/assets/svg/Cross';
import InputField from '@/app/components/InputField';
import Courses from '@/app/assets/svg/courses';
import Distance from '@/app/assets/svg/distance';
import SelectField from '@/app/components/SelectField';
import LinkWithIcon from '@/app/components/LinkWithIcon';
import DropDownButton from '@/app/components/DropDownButton';
import Westcliff from '@/app/assets/img/westcliff.png';
import SectionLayout from '@/app/components/layout/SectionLayout';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { 
    levelOfStudy, 
    courseTableRows, 
    courseData as initialCourseData, 
} 
from '@/common';

const Page = () => {
    const [courseData, setCourseData] = useState(initialCourseData);
    const [hiddenColumns, setHiddenColumns] = useState<boolean[]>(
        Array(initialCourseData.length).fill(false)
    );

    const handleHideColumn = (index: number) => {
        const updatedHiddenColumns = [...hiddenColumns];
        updatedHiddenColumns[index] = true;
        setHiddenColumns(updatedHiddenColumns);
    };

    return (
        <DashboardLayout>
            <div className='pb-5 pt-10 flex items-center justify-between'>
                <Heading level='h1'>Compare Universities</Heading>
                <div>
                    <DropDownButton />
                </div>
            </div>
            <div className='pb-[101px]'>
                <SectionLayout>
                    <div
                        className='
                        [&::-webkit-scrollbar-track]:rounded-full 
                        [&::-webkit-scrollbar-thumb]:rounded-full 
                        [&::-webkit-scrollbar]:h-4
                        scrollbar 
                        scrollbar-thumb-grayTwo 
                        scrollbar-track-grayOne 
                        overflow-x-auto 
                        rounded-lg border 
                        border-grayOne'
                    >
                        <table className='w-full table-auto'>
                            <thead>
                                <tr>
                                    <th className='border-grayOne border-r p-4'>
                                        <SelectField
                                            label='Level of study'
                                            options={levelOfStudy}
                                            value={'field.value'}
                                            // onChange={field.onChange}
                                            className="bg-primaryColor text-white "
                                        />
                                    </th>
                                    {courseData.map((_, index) => (
                                        <th key={index} className='p-4 border-r border-grayOne'>
                                        <InputField
                                            id={`compare_${index}`}
                                            placeholder='Search'
                                            type='text'
                                            label='Compare with'
                                            className='border-grayOne border-opacity-none'
                                        />
                                        </th>
                                    ))}
                                </tr>
                                <tr className='border-t border-grayOne'>
                                    <th className='w-[20%] py-2 border-r border-grayOne font-medium text-xl text-graySix'>
                                        University and Requirements
                                    </th>
                                    {courseData.map((item, index) => (
                                        <th
                                            key={index}
                                            className='w-[26%] border-r px-6 py-5 border-l border-grayOne'
                                        >
                                        {!hiddenColumns[index] && (
                                            <>
                                            <div className='flex justify-end'>
                                                <button onClick={() => handleHideColumn(index)}>
                                                <Cross className='cursor-pointer' />
                                                </button>
                                            </div>
                                            <div className='flex gap-3 items-center'>
                                                <div>
                                                    <Image src={Westcliff} alt='University Logo' />
                                                </div>
                                                <div className='flex flex-col text-left'>
                                                    <p className='font-medium text-base leading-5 text-graySix'>
                                                        {item.university}
                                                    </p>
                                                    <p className='pt-2 flex gap-1.5 font-normal leading-4 text-xs text-grayFour'>
                                                        <Distance />
                                                        {item.location}
                                                    </p>
                                                </div>
                                            </div>
                                            </>
                                        )}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {courseTableRows.map((row, rowIndex) => (
                                    <tr key={rowIndex}>
                                        <td className='min-w-[200px] border-r font-medium text-graySix tracking-[0.25px] text-sm px-6 py-3.5 border-t border-grayOne'>
                                            {row.label}
                                        </td>
                                        {courseData.map((item, colIndex) => (
                                            <td
                                                key={colIndex}
                                                className='font-normal border-r text-graySix tracking-[0.25px] text-xs px-6 py-3.5 border-l border-t border-grayOne'
                                            >
                                                {!hiddenColumns[colIndex] && item[row.key]}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                                <tr>
                                    <td className='px-6 py-5 border-r border-t border-grayOne'></td>
                                    {courseData.map((_, index) => (
                                        <td
                                            key={index}
                                            className='px-6 py-5 border-r border-t border-grayOne text-center'
                                        >
                                        {!hiddenColumns[index] && (
                                            <LinkWithIcon url='/'>
                                                <Courses className='w-4 h-4' />
                                                <span>Explore Course</span>
                                            </LinkWithIcon>
                                        )}
                                        </td>
                                    ))}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </SectionLayout>
            </div>
        </DashboardLayout>
    );
};

export default Page;
