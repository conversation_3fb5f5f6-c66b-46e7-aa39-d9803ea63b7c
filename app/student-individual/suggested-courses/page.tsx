'use client'

import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import Switcher from '@/app/components/Switcher';
import Pagination from '@/app/components/Pagination';
import CourseCard from '@/app/components/CourseCard';
import { CourseFinderFiltersProps } from '@/types';
import CoursesFilter from '@/app/components/CoursesFilter';
import  CompareButton  from '@/app/components/CompareButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import StudentIndividualBanner from '@/app/components/StudentIndividualBanner';
import { courses, countries, programs, sortByOptions, intakes } from '@/common';
import SuggestedCoursesBannerImg from '@/app/assets/svg/suggested-courses-banner-img';


const page = () => {
    const [isChecked, setIsChecked] = useState(false);
    const [selectedCards, setSelectedCards] = useState<number[]>([]);
    const [filters, setFilters] = useState<CourseFinderFiltersProps>({
        program: '',
        intake: '',
        country: [],
        tuitionFees: '',
        sortBy: '',
    });

    const handleToggle = (checked: boolean) => {
        setIsChecked(checked);
        setSelectedCards([])
    };
    
    const { toast } = useToast();
    const handleSelect = (id: number) => {
        setSelectedCards((prevSelectedCards) => {
            if (prevSelectedCards.includes(id)) {
                return prevSelectedCards.filter((cardId) => cardId !== id);
            } else if (prevSelectedCards.length < 4) {
                return [...prevSelectedCards, id];
            } else {
                toast({
                    description: 'Sorry, you can compare up to 04 courses!',
                    variant: 'destructive',
                });
                // console.log('you are the out of the limit')
                return prevSelectedCards;
            }
        });
    };

    const filteredCourses = courses.filter((course) => {
            if (filters.program && course.program !== filters.program) return false;
            if (filters.intake && course.intake !== filters.intake) return false;
            if (filters.country.length > 0 && !filters.country.includes(course.country)) return false;
            if (
                filters.tuitionFees &&
                (course.tuitionFees[1] < parseInt(filters.tuitionFees) ||
                course.tuitionFees[0] > parseInt(filters.tuitionFees))
            )
                return false;
            return true;
          }).map((course) => ({
            ...course,
            tuitionFees: [course.tuitionFees[0], course.tuitionFees[1]] as [number, number],
    }));   

    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    const currentCources = courses.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    return (
        <DashboardLayout>
            <div className='flex flex-col'>
                <div className='pt-5 pb-10'>
                    <StudentIndividualBanner
                        date='September 4, 2023'
                        description={`'Fast-Track Your Student Visa: Join Our Free Webinar!'`}
                        heading='Lee'
                        BannerImage={<SuggestedCoursesBannerImg />}
                    />
                </div>
                <div>
                    <CoursesFilter 
                        programs={programs}
                        intakes={intakes}
                        countries={countries}
                        tuitionFees={filters.tuitionFees}
                        sortByOptions={sortByOptions}
                        onFilterChange={setFilters}
                        className='rounded-2xl border bg-white border-[#1E62E0] border-opacity-20 py-[30px] px-5'
                    />
                </div>
                <div className='flex flex-col md:flex-row gap-5 py-[22.5px] items-center'>
                    <div className='flex gap-7'>
                        <h2 className='font-semibold text-xl md:text-2xl leading-[29.05px] text-graySix'>Suggested Courses</h2>
                        <div className='flex gap-3 items-center'>
                            <span className='font-normal text-base leading-6 text-grayFive'>Compare</span>
                            <Switcher isChecked={isChecked} handleToggle={handleToggle} className='border border-grayFive' />
                        </div>
                    </div>
                    <p className='font-semibold text-sm leading-4 text-primaryColor'>(Select maximum 04 courses for comparison)</p>
                </div>
                <div className='pb-[170px] grid grid-cols-1 md:grid-cols-4 gap-6'>
                    {filteredCourses.map((course) => (
                        <CourseCard 
                            key={course.id} 
                            course={course} 
                            isSelected={selectedCards.includes(course.id)}
                            onSelect={handleSelect}
                            isChecked={isChecked}
                        />
                    ))}
                </div>
                <div className='pb-[101px]'>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                    />
                </div>
            </div>
            {selectedCards.length > 0 && (
                <div className='mr-14 w-[90%] md:w-[90%] fixed bottom-0 overflow-hidden'>
                    <CompareButton selectedCourseNumber={selectedCards.length} />
                </div>
            )}
        </DashboardLayout>
    )
}

export default page
