"use client"

import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from "@/components/ui/toaster"
import ReactQueryProvider from './providers/ReactQueryProvider';
import AuthProvider from './providers/AuthProviders';
import ReduxProvider from './providers/ReduxProvider';
import { GoogleOAuthProvider } from '@react-oauth/google';


const inter = Inter({
    subsets: ['latin'],
    variable: '--font-inter',
    display: "swap",
    preload: true,
})

const metadata: Metadata = {
    title: 'Apply Goal',
    description: 'Generated by create next app',
};

console.log('client id', process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID);


export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {    
    return (
        <html lang='en'>
            <body
                className={`${inter.variable} font-inter bg-primaryOne antialiased`}
            >
                <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!}>
                    <ReduxProvider>
                        {children}
                    </ReduxProvider>
                </GoogleOAuthProvider>
                <Toaster />
            </body>
        </html>
    );
}
