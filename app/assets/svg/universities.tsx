import React from 'react';
import { IconProps } from '@/types';

const Universities: React.FC<IconProps> = ({ className }) => {
    return (
        <div>
            <svg className={className} width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_2875_5430" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
            <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_2875_5430)">
            <path d="M6.0502 18.2754C5.71686 18.0921 5.45853 17.8462 5.2752 17.5379C5.09186 17.2296 5.0002 16.8837 5.0002 16.5004V11.7004L2.6002 10.3754C2.41686 10.2754 2.28353 10.1504 2.2002 10.0004C2.11686 9.85039 2.0752 9.68372 2.0752 9.50039C2.0752 9.31706 2.11686 9.15039 2.2002 9.00039C2.28353 8.85039 2.41686 8.72539 2.6002 8.62539L11.0502 4.02539C11.2002 3.94206 11.3544 3.87956 11.5127 3.83789C11.671 3.79622 11.8335 3.77539 12.0002 3.77539C12.1669 3.77539 12.3294 3.79622 12.4877 3.83789C12.646 3.87956 12.8002 3.94206 12.9502 4.02539L22.4752 9.22539C22.6419 9.30872 22.771 9.42956 22.8627 9.58789C22.9544 9.74622 23.0002 9.91706 23.0002 10.1004V16.5004C23.0002 16.7837 22.9044 17.0212 22.7127 17.2129C22.521 17.4046 22.2835 17.5004 22.0002 17.5004C21.7169 17.5004 21.4794 17.4046 21.2877 17.2129C21.096 17.0212 21.0002 16.7837 21.0002 16.5004V10.6004L19.0002 11.7004V16.5004C19.0002 16.8837 18.9085 17.2296 18.7252 17.5379C18.5419 17.8462 18.2835 18.0921 17.9502 18.2754L12.9502 20.9754C12.8002 21.0587 12.646 21.1212 12.4877 21.1629C12.3294 21.2046 12.1669 21.2254 12.0002 21.2254C11.8335 21.2254 11.671 21.2046 11.5127 21.1629C11.3544 21.1212 11.2002 21.0587 11.0502 20.9754L6.0502 18.2754ZM12.0002 13.2004L18.8502 9.50039L12.0002 5.80039L5.1502 9.50039L12.0002 13.2004ZM12.0002 19.2254L17.0002 16.5254V12.7504L12.9752 14.9754C12.8252 15.0587 12.6669 15.1212 12.5002 15.1629C12.3335 15.2046 12.1669 15.2254 12.0002 15.2254C11.8335 15.2254 11.6669 15.2046 11.5002 15.1629C11.3335 15.1212 11.1752 15.0587 11.0252 14.9754L7.0002 12.7504V16.5254L12.0002 19.2254Z" fill="currentColor"/>
            </g>
            </svg>
        </div>
    );
};

export default Universities;
