import React from 'react';

const UniProfileCostIcon = () => {
    return (
        <svg
            width="17"
            height="16"
            viewBox="0 0 17 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27010"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="17"
                height="16"
            >
                <rect x="0.333984" width="16" height="16" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27010)">
                <path
                    d="M7.73464 12.6663H8.9013V11.833C9.45686 11.733 9.93464 11.5163 10.3346 11.183C10.7346 10.8497 10.9346 10.3552 10.9346 9.69967C10.9346 9.23301 10.8013 8.80523 10.5346 8.41634C10.268 8.02745 9.73464 7.68856 8.93464 7.39967C8.26797 7.17745 7.80686 6.98301 7.5513 6.81634C7.29575 6.64967 7.16797 6.4219 7.16797 6.13301C7.16797 5.84412 7.27075 5.61634 7.4763 5.44967C7.68186 5.28301 7.97908 5.19967 8.36797 5.19967C8.72352 5.19967 9.0013 5.28579 9.2013 5.45801C9.4013 5.63023 9.54575 5.84412 9.63463 6.09967L10.7013 5.66634C10.5791 5.27745 10.3541 4.93856 10.0263 4.64967C9.69852 4.36079 9.33464 4.19967 8.93464 4.16634V3.33301H7.76797V4.16634C7.21241 4.28856 6.77908 4.53301 6.46797 4.89967C6.15686 5.26634 6.0013 5.67745 6.0013 6.13301C6.0013 6.65523 6.15408 7.07745 6.45964 7.39967C6.76519 7.7219 7.24575 7.99967 7.9013 8.23301C8.6013 8.48856 9.08741 8.71634 9.35964 8.91634C9.63186 9.11634 9.76797 9.37745 9.76797 9.69967C9.76797 10.0663 9.63741 10.3358 9.3763 10.508C9.11519 10.6802 8.8013 10.7663 8.43464 10.7663C8.06797 10.7663 7.74297 10.6525 7.45964 10.4247C7.1763 10.1969 6.96797 9.85523 6.83464 9.39967L5.73463 9.83301C5.89019 10.3663 6.13186 10.7969 6.45964 11.1247C6.78741 11.4525 7.21241 11.6775 7.73464 11.7997V12.6663ZM8.33463 14.6663C7.41241 14.6663 6.54575 14.4913 5.73463 14.1413C4.92352 13.7913 4.21797 13.3163 3.61797 12.7163C3.01797 12.1163 2.54297 11.4108 2.19297 10.5997C1.84297 9.78856 1.66797 8.9219 1.66797 7.99967C1.66797 7.07745 1.84297 6.21079 2.19297 5.39967C2.54297 4.58856 3.01797 3.88301 3.61797 3.28301C4.21797 2.68301 4.92352 2.20801 5.73463 1.85801C6.54575 1.50801 7.41241 1.33301 8.33463 1.33301C9.25686 1.33301 10.1235 1.50801 10.9346 1.85801C11.7457 2.20801 12.4513 2.68301 13.0513 3.28301C13.6513 3.88301 14.1263 4.58856 14.4763 5.39967C14.8263 6.21079 15.0013 7.07745 15.0013 7.99967C15.0013 8.9219 14.8263 9.78856 14.4763 10.5997C14.1263 11.4108 13.6513 12.1163 13.0513 12.7163C12.4513 13.3163 11.7457 13.7913 10.9346 14.1413C10.1235 14.4913 9.25686 14.6663 8.33463 14.6663ZM8.33463 13.333C9.82352 13.333 11.0846 12.8163 12.118 11.783C13.1513 10.7497 13.668 9.48856 13.668 7.99967C13.668 6.51079 13.1513 5.24967 12.118 4.21634C11.0846 3.18301 9.82352 2.66634 8.33463 2.66634C6.84575 2.66634 5.58464 3.18301 4.5513 4.21634C3.51797 5.24967 3.0013 6.51079 3.0013 7.99967C3.0013 9.48856 3.51797 10.7497 4.5513 11.783C5.58464 12.8163 6.84575 13.333 8.33463 13.333Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default UniProfileCostIcon;
