import React from 'react'

type InstitutionProps= {
    className:  string;
}
const Institution: React.FC<InstitutionProps> = ({ className }) => {
  return (
    <svg className={className} width="56" height="56" viewBox="0 0 56 56" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path d="M27.9677 12.02C27.961 12.0201 27.9543 12.0203 27.9476 12.0207C27.7616 12.0341 27.6172 12.1884 27.6177 12.3749V14.8534H28.326V14.0295L29.8741 13.4125C30.1723 13.2943 30.1723 12.8722 29.8741 12.754L28.104 12.045C28.0605 12.0276 28.0143 12.0194 27.9677 12.02ZM25.8475 15.5617V17.3319H26.1332L27.8397 16.6491C27.9245 16.615 28.0192 16.615 28.104 16.6491L29.8105 17.3319H30.0962V15.5617H25.8475ZM27.9705 17.3602L21.2434 20.0504V20.5214H34.7003V20.0504L27.9705 17.3602ZM16.4434 18.0402L14.2389 20.5214H20.535V19.8124C20.5348 19.6676 20.6227 19.5372 20.7571 19.4831L24.3631 18.0402H16.4434ZM31.5806 18.0402L35.1866 19.4831C35.321 19.5372 35.4089 19.6676 35.4087 19.8124V20.5214H41.7048L39.5003 18.0402H31.5806ZM12.3885 21.2298V21.9353H20.535V21.2298H12.3885ZM21.2434 21.2298V21.9353H34.7003V21.2298H21.2434ZM35.4087 21.2298V21.9353H43.5552V21.2298H35.4087ZM13.8059 22.6444V31.8548H20.7322L21.2434 23.6847V22.6444H13.8059ZM21.9524 22.6444V23.3527H24.0753V22.6444H21.9524ZM24.7837 22.6444V23.6847C25.1376 29.3577 25.4904 35.0392 25.8475 40.7075V35.0423C25.8475 33.8711 26.7993 32.9159 27.9705 32.9159C29.1416 32.9159 30.0962 33.8711 30.0962 35.0423V40.7075C30.4672 35.0394 30.7953 29.357 31.16 23.6847V22.6444H24.7837ZM31.8684 22.6444V23.3527H33.9913V22.6444H31.8684ZM34.7003 22.6444V23.6847L35.2101 31.8548H42.1378V22.6444H34.7003ZM21.9289 24.061L20.9113 40.3548H25.1157L24.0982 24.061H21.9289ZM31.8456 24.061L30.828 40.3548H35.0324L34.0148 24.061H31.8456ZM15.5753 25.4784H19.1184C19.314 25.4792 19.4719 25.6383 19.4712 25.8339V28.6652C19.4719 28.8608 19.314 29.02 19.1184 29.0207H15.5753C15.3797 29.02 15.2218 28.8608 15.2225 28.6652V25.8339C15.2218 25.6383 15.3797 25.4792 15.5753 25.4784ZM26.2003 25.4784H29.7434C29.939 25.4792 30.0969 25.6383 30.0962 25.8339V27.2478C30.0969 27.4434 29.939 27.6026 29.7434 27.6034H26.2003C26.0047 27.6026 25.8468 27.4434 25.8475 27.2478V25.8339C25.8468 25.6383 26.0047 25.4792 26.2003 25.4784ZM36.8253 25.4784H40.3684C40.564 25.4792 40.7219 25.6383 40.7212 25.8339V28.6652C40.7219 28.8608 40.564 29.02 40.3684 29.0207H36.8253C36.6297 29.02 36.4718 28.8608 36.4725 28.6652V25.8339C36.4718 25.6383 36.6297 25.4792 36.8253 25.4784ZM15.9309 26.1867V28.3124H16.9927V26.1867H15.9309ZM17.701 26.1867V28.3124H18.7628V26.1867H17.701ZM26.5559 26.1867V26.895H29.3878V26.1867H26.5559ZM37.1809 26.1867V28.3124H38.2427V26.1867H37.1809ZM38.951 26.1867V28.3124H40.0128V26.1867H38.951ZM12.0267 31.8548C11.8354 31.8588 11.6809 32.015 11.6802 32.2075C11.6809 32.4031 11.8401 32.5611 12.0357 32.5603H13.0968V31.8548H12.0357C12.0331 31.8547 12.0308 31.8547 12.0267 31.8548ZM42.8469 31.8548V32.5603H43.908C44.1036 32.5611 44.2628 32.4031 44.2635 32.2075C44.2628 32.0119 44.1036 31.854 43.908 31.8548H42.8469ZM13.8059 32.5603V41.7721H20.1795C20.1795 38.6986 20.5488 35.6338 20.6879 32.5603H13.8059ZM35.2544 32.5603C35.4458 35.6293 35.7562 38.6989 35.7642 41.7721H42.1378V32.5603H35.2544ZM27.9705 33.6249C27.1795 33.6249 26.5559 34.2513 26.5559 35.0423V41.7721H29.3878V35.0423C29.3878 34.2513 28.7614 33.6249 27.9705 33.6249ZM15.5573 35.395C15.564 35.3947 15.5694 35.3951 15.5753 35.395H19.1183C19.3139 35.3958 19.4719 35.555 19.4711 35.7506V38.5826C19.4719 38.7782 19.3139 38.9373 19.1183 38.9381H15.5753C15.3797 38.9373 15.2218 38.7782 15.2225 38.5826V35.7506C15.2218 35.5611 15.3702 35.4053 15.5573 35.395ZM36.8073 35.395C36.814 35.3947 36.8194 35.3951 36.8253 35.395H40.3683C40.5639 35.3958 40.7219 35.555 40.7211 35.7506V38.5826C40.7219 38.7782 40.5639 38.9373 40.3683 38.9381H36.8253C36.6297 38.9373 36.4718 38.7782 36.4725 38.5826V35.7506C36.4718 35.5611 36.6203 35.4053 36.8073 35.395ZM15.9309 36.1041V38.2291H16.9927V36.1041H15.9309ZM17.701 36.1041V38.2291H18.7628V36.1041H17.701ZM37.1809 36.1041V38.2291H38.2427V36.1041H37.1809ZM38.951 36.1041V38.2291H40.0128V36.1041H38.951ZM20.8878 41.0631V41.7721H25.1392V41.0631H20.8878ZM30.8045 41.0631V41.7721H35.0559V41.0631H30.8045ZM12.3885 42.4805V43.8951H43.5552V42.4805H12.3885Z" fill="#currentColor"/>
    </svg>
  )
}

export default Institution;