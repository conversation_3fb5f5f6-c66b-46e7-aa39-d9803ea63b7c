
const Account = () => {
    return (
        <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_2560_25288" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="20">
        <rect x="0.753906" width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_2560_25288)">
        <path d="M5.6263 14.2503C6.33464 13.7087 7.1263 13.2816 8.0013 12.9691C8.8763 12.6566 9.79297 12.5003 10.7513 12.5003C11.7096 12.5003 12.6263 12.6566 13.5013 12.9691C14.3763 13.2816 15.168 13.7087 15.8763 14.2503C16.3624 13.6809 16.7409 13.035 17.0117 12.3128C17.2825 11.5906 17.418 10.8198 17.418 10.0003C17.418 8.1531 16.7687 6.58019 15.47 5.28157C14.1714 3.98296 12.5985 3.33366 10.7513 3.33366C8.90408 3.33366 7.33116 3.98296 6.03255 5.28157C4.73394 6.58019 4.08464 8.1531 4.08464 10.0003C4.08464 10.8198 4.22005 11.5906 4.49089 12.3128C4.76172 13.035 5.14019 13.6809 5.6263 14.2503ZM10.7513 10.8337C9.93186 10.8337 9.24088 10.5524 8.67838 9.98991C8.11588 9.42741 7.83463 8.73644 7.83463 7.91699C7.83463 7.09755 8.11588 6.40658 8.67838 5.84408C9.24088 5.28158 9.93186 5.00032 10.7513 5.00032C11.5707 5.00032 12.2617 5.28158 12.8242 5.84408C13.3867 6.40658 13.668 7.09755 13.668 7.91699C13.668 8.73644 13.3867 9.42741 12.8242 9.98991C12.2617 10.5524 11.5707 10.8337 10.7513 10.8337ZM10.7513 18.3337C9.59852 18.3337 8.51519 18.1149 7.5013 17.6774C6.48741 17.2399 5.60547 16.6462 4.85547 15.8962C4.10547 15.1462 3.51172 14.2642 3.07422 13.2503C2.63672 12.2364 2.41797 11.1531 2.41797 10.0003C2.41797 8.84755 2.63672 7.76421 3.07422 6.75032C3.51172 5.73644 4.10547 4.85449 4.85547 4.10449C5.60547 3.35449 6.48741 2.76074 7.5013 2.32324C8.51519 1.88574 9.59852 1.66699 10.7513 1.66699C11.9041 1.66699 12.9874 1.88574 14.0013 2.32324C15.0152 2.76074 15.8971 3.35449 16.6471 4.10449C17.3971 4.85449 17.9909 5.73644 18.4284 6.75032C18.8659 7.76421 19.0846 8.84755 19.0846 10.0003C19.0846 11.1531 18.8659 12.2364 18.4284 13.2503C17.9909 14.2642 17.3971 15.1462 16.6471 15.8962C15.8971 16.6462 15.0152 17.2399 14.0013 17.6774C12.9874 18.1149 11.9041 18.3337 10.7513 18.3337Z" fill="currentColor"/>
        </g>
        </svg>
    )
}

export default Account;


