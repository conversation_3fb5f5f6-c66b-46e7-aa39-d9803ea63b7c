
const SupportAgent = () => {
    return (
        <svg
            width="21"
            height="21"
            viewBox="0 0 21 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_1480_3551"
                style={{ maskType: 'alpha' }}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="21"
                height="21"
            >
                <rect width="21" height="21" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1480_3551)">
                <path
                    d="M9.62891 18.375V16.625H16.6289V10.4125C16.6289 8.70625 16.0346 7.25885 14.8461 6.07031C13.6576 4.88177 12.2102 4.2875 10.5039 4.2875C8.79766 4.2875 7.35026 4.88177 6.16172 6.07031C4.97318 7.25885 4.37891 8.70625 4.37891 10.4125V15.75H3.50391C3.02266 15.75 2.61068 15.5786 2.26797 15.2359C1.92526 14.8932 1.75391 14.4812 1.75391 14V12.25C1.75391 11.9438 1.83047 11.6557 1.98359 11.3859C2.13672 11.1161 2.35182 10.901 2.62891 10.7406L2.69453 9.58125C2.8112 8.58958 3.09922 7.67083 3.55859 6.825C4.01797 5.97917 4.59401 5.24271 5.28672 4.61563C5.97943 3.98854 6.77422 3.5 7.67109 3.15C8.56797 2.8 9.51224 2.625 10.5039 2.625C11.4956 2.625 12.4362 2.8 13.3258 3.15C14.2154 3.5 15.0102 3.9849 15.7102 4.60469C16.4102 5.22448 16.9862 5.95729 17.4383 6.80312C17.8904 7.64896 18.182 8.56771 18.3133 9.55938L18.3789 10.6969C18.656 10.8281 18.8711 11.025 19.0242 11.2875C19.1773 11.55 19.2539 11.8271 19.2539 12.1188V14.1313C19.2539 14.4229 19.1773 14.7 19.0242 14.9625C18.8711 15.225 18.656 15.4219 18.3789 15.5531V16.625C18.3789 17.1062 18.2076 17.5182 17.8648 17.8609C17.5221 18.2036 17.1102 18.375 16.6289 18.375H9.62891ZM7.87891 12.25C7.63099 12.25 7.42318 12.1661 7.25547 11.9984C7.08776 11.8307 7.00391 11.6229 7.00391 11.375C7.00391 11.1271 7.08776 10.9193 7.25547 10.7516C7.42318 10.5839 7.63099 10.5 7.87891 10.5C8.12682 10.5 8.33463 10.5839 8.50234 10.7516C8.67005 10.9193 8.75391 11.1271 8.75391 11.375C8.75391 11.6229 8.67005 11.8307 8.50234 11.9984C8.33463 12.1661 8.12682 12.25 7.87891 12.25ZM13.1289 12.25C12.881 12.25 12.6732 12.1661 12.5055 11.9984C12.3378 11.8307 12.2539 11.6229 12.2539 11.375C12.2539 11.1271 12.3378 10.9193 12.5055 10.7516C12.6732 10.5839 12.881 10.5 13.1289 10.5C13.3768 10.5 13.5846 10.5839 13.7523 10.7516C13.9201 10.9193 14.0039 11.1271 14.0039 11.375C14.0039 11.6229 13.9201 11.8307 13.7523 11.9984C13.5846 12.1661 13.3768 12.25 13.1289 12.25ZM5.27578 10.8938C5.1737 9.34792 5.64036 8.02083 6.67578 6.9125C7.7112 5.80417 9.00182 5.25 10.5477 5.25C11.8456 5.25 12.9867 5.66198 13.9711 6.48594C14.9555 7.3099 15.5497 8.36354 15.7539 9.64688C14.4268 9.63229 13.2055 9.275 12.0898 8.575C10.9742 7.875 10.1174 6.92708 9.51953 5.73125C9.2862 6.89792 8.79401 7.93698 8.04297 8.84844C7.29193 9.7599 6.36953 10.4417 5.27578 10.8938Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default SupportAgent;
