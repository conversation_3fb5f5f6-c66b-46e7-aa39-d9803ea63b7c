import React from 'react'

const linkedin = () => {
    return (
        <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2560_23980)">
            <path d="M25.9331 0.801758H2.06694C1.51876 0.801758 0.993022 1.01952 0.605394 1.40715C0.217767 1.79478 0 2.32051 0 2.8687V26.7348C0 27.283 0.217767 27.8087 0.605394 28.1964C0.993022 28.584 1.51876 28.8018 2.06694 28.8018H25.9331C26.4812 28.8018 27.007 28.584 27.3946 28.1964C27.7822 27.8087 28 27.283 28 26.7348V2.8687C28 2.32051 27.7822 1.79478 27.3946 1.40715C27.007 1.01952 26.4812 0.801758 25.9331 0.801758ZM8.34556 24.6543H4.13583V11.2823H8.34556V24.6543ZM6.23778 9.42926C5.76026 9.42657 5.29423 9.28249 4.89852 9.01519C4.50281 8.7479 4.19515 8.36938 4.01436 7.92739C3.83357 7.48541 3.78776 6.99978 3.8827 6.53179C3.97765 6.06379 4.2091 5.63441 4.54785 5.29784C4.88659 4.96126 5.31745 4.73257 5.78604 4.64063C6.25464 4.54869 6.73996 4.59762 7.18078 4.78124C7.62159 4.96486 7.99813 5.27495 8.26288 5.67237C8.52762 6.06979 8.66871 6.53673 8.66833 7.01426C8.67284 7.33396 8.61293 7.6513 8.49218 7.94736C8.37143 8.24342 8.19231 8.51214 7.9655 8.7375C7.73869 8.96286 7.46882 9.14025 7.17199 9.2591C6.87517 9.37795 6.55745 9.43581 6.23778 9.42926ZM23.8622 24.6659H19.6544V17.3606C19.6544 15.2062 18.7386 14.5412 17.5564 14.5412C16.3081 14.5412 15.0831 15.4823 15.0831 17.4151V24.6659H10.8733V11.292H14.9217V13.1451H14.9761C15.3825 12.3226 16.8058 10.9168 18.9778 10.9168C21.3267 10.9168 23.8642 12.3109 23.8642 16.3943L23.8622 24.6659Z" fill="#0A66C2"/>
            </g>
            <defs>
            <clipPath id="clip0_2560_23980">
                <rect width="28" height="28" fill="white" transform="translate(0 0.801758)"/>
            </clipPath>
            </defs>
        </svg>
    )
}

export default linkedin