import React from 'react';

const NoStatus = () => {
    return (
        <svg
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_25611"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="19"
                height="19"
            >
                <rect
                    x="0.253906"
                    y="0.5"
                    width="18"
                    height="18"
                    fill="#D9D9D9"
                />
            </mask>
            <g mask="url(#mask0_2560_25611)">
                <path
                    d="M9.25391 17C8.21641 17 7.24141 16.8031 6.32891 16.4094C5.41641 16.0156 4.62266 15.4813 3.94766 14.8063C3.27266 14.1313 2.73828 13.3375 2.34453 12.425C1.95078 11.5125 1.75391 10.5375 1.75391 9.5C1.75391 8.4625 1.95078 7.4875 2.34453 6.575C2.73828 5.6625 3.27266 4.86875 3.94766 4.19375C4.62266 3.51875 5.41641 2.98438 6.32891 2.59063C7.24141 2.19687 8.21641 2 9.25391 2C10.2914 2 11.2664 2.19687 12.1789 2.59063C13.0914 2.98438 13.8852 3.51875 14.5602 4.19375C15.2352 4.86875 15.7695 5.6625 16.1633 6.575C16.557 7.4875 16.7539 8.4625 16.7539 9.5C16.7539 10.5375 16.557 11.5125 16.1633 12.425C15.7695 13.3375 15.2352 14.1313 14.5602 14.8063C13.8852 15.4813 13.0914 16.0156 12.1789 16.4094C11.2664 16.8031 10.2914 17 9.25391 17ZM9.25391 15.5C9.92891 15.5 10.5789 15.3906 11.2039 15.1719C11.8289 14.9531 12.4039 14.6375 12.9289 14.225L4.52891 5.825C4.11641 6.35 3.80078 6.925 3.58203 7.55C3.36328 8.175 3.25391 8.825 3.25391 9.5C3.25391 11.175 3.83516 12.5938 4.99766 13.7563C6.16016 14.9187 7.57891 15.5 9.25391 15.5ZM13.9789 13.175C14.3914 12.65 14.707 12.075 14.9258 11.45C15.1445 10.825 15.2539 10.175 15.2539 9.5C15.2539 7.825 14.6727 6.40625 13.5102 5.24375C12.3477 4.08125 10.9289 3.5 9.25391 3.5C8.57891 3.5 7.92891 3.60938 7.30391 3.82812C6.67891 4.04688 6.10391 4.3625 5.57891 4.775L13.9789 13.175Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default NoStatus;
