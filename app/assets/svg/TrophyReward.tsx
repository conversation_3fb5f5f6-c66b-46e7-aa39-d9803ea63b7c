import React from 'react'

const TrophyReward = () => {
    return (
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_653_4037" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                <rect x="0.873047" y="0.602539" width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_653_4037)">
                <path d="M12.873 11.6025C13.423 11.6025 13.8939 11.4067 14.2855 11.015C14.6772 10.6234 14.873 10.1525 14.873 9.60254C14.873 9.05254 14.6772 8.58171 14.2855 8.19004C13.8939 7.79837 13.423 7.60254 12.873 7.60254C12.323 7.60254 11.8522 7.79837 11.4605 8.19004C11.0689 8.58171 10.873 9.05254 10.873 9.60254C10.873 10.1525 11.0689 10.6234 11.4605 11.015C11.8522 11.4067 12.323 11.6025 12.873 11.6025ZM7.87305 11.4025V7.60254H5.87305V8.60254C5.87305 9.23587 6.05638 9.80671 6.42305 10.315C6.78971 10.8234 7.27305 11.1859 7.87305 11.4025ZM17.873 11.4025C18.473 11.1859 18.9564 10.8234 19.323 10.315C19.6897 9.80671 19.873 9.23587 19.873 8.60254V7.60254H17.873V11.4025ZM11.873 19.6025V16.5025C11.0564 16.3192 10.3272 15.9734 9.68555 15.465C9.04388 14.9567 8.57305 14.3192 8.27305 13.5525C7.02305 13.4025 5.97721 12.8567 5.13555 11.915C4.29388 10.9734 3.87305 9.86921 3.87305 8.60254V7.60254C3.87305 7.05254 4.06888 6.58171 4.46055 6.19004C4.85221 5.79837 5.32305 5.60254 5.87305 5.60254H7.87305C7.87305 5.05254 8.06888 4.58171 8.46055 4.19004C8.85221 3.79837 9.32305 3.60254 9.87305 3.60254H15.873C16.423 3.60254 16.8939 3.79837 17.2855 4.19004C17.6772 4.58171 17.873 5.05254 17.873 5.60254H19.873C20.423 5.60254 20.8939 5.79837 21.2855 6.19004C21.6772 6.58171 21.873 7.05254 21.873 7.60254V8.60254C21.873 9.86921 21.4522 10.9734 20.6105 11.915C19.7689 12.8567 18.723 13.4025 17.473 13.5525C17.173 14.3192 16.7022 14.9567 16.0605 15.465C15.4189 15.9734 14.6897 16.3192 13.873 16.5025V19.6025H16.873C17.1564 19.6025 17.3939 19.6984 17.5855 19.89C17.7772 20.0817 17.873 20.3192 17.873 20.6025C17.873 20.8859 17.7772 21.1234 17.5855 21.315C17.3939 21.5067 17.1564 21.6025 16.873 21.6025H8.87305C8.58971 21.6025 8.35221 21.5067 8.16055 21.315C7.96888 21.1234 7.87305 20.8859 7.87305 20.6025C7.87305 20.3192 7.96888 20.0817 8.16055 19.89C8.35221 19.6984 8.58971 19.6025 8.87305 19.6025H11.873Z" fill="#1952BB" />
            </g>
        </svg>
    )
}

export default TrophyReward