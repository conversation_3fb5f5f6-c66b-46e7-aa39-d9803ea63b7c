import React from 'react';
import { xIconProps } from '@/types';

const Subscript: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4501_6258"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4501_6258)">
                <path
                    d="M15.0012 15C14.7887 15 14.6106 14.9281 14.4668 14.7844C14.3231 14.6406 14.2512 14.4625 14.2512 14.25V13.5C14.2512 13.2875 14.3231 13.1094 14.4668 12.9656C14.6106 12.8219 14.7887 12.75 15.0012 12.75H16.5012V12H14.6262C14.5262 12 14.4387 11.9625 14.3637 11.8875C14.2887 11.8125 14.2512 11.725 14.2512 11.625C14.2512 11.525 14.2887 11.4375 14.3637 11.3625C14.4387 11.2875 14.5262 11.25 14.6262 11.25H16.5012C16.7137 11.25 16.8918 11.3219 17.0356 11.4656C17.1793 11.6094 17.2512 11.7875 17.2512 12V12.75C17.2512 12.9625 17.1793 13.1406 17.0356 13.2844C16.8918 13.4281 16.7137 13.5 16.5012 13.5H15.0012V14.25H16.8762C16.9762 14.25 17.0637 14.2875 17.1387 14.3625C17.2137 14.4375 17.2512 14.525 17.2512 14.625C17.2512 14.725 17.2137 14.8125 17.1387 14.8875C17.0637 14.9625 16.9762 15 16.8762 15H15.0012ZM5.94493 13.5C5.60743 13.5 5.36055 13.3563 5.2043 13.0688C5.04805 12.7813 5.05743 12.4938 5.23243 12.2063L7.87618 8.04375L5.47618 4.275C5.30118 4 5.2918 3.71875 5.44805 3.43125C5.6043 3.14375 5.84493 3 6.16993 3C6.31993 3 6.45743 3.03438 6.58243 3.10313C6.70743 3.17188 6.80743 3.26875 6.88243 3.39375L8.96368 6.75H9.03868L11.1012 3.39375C11.1762 3.26875 11.2793 3.17188 11.4106 3.10313C11.5418 3.03438 11.6762 3 11.8137 3C12.1512 3 12.3949 3.14375 12.5449 3.43125C12.6949 3.71875 12.6824 4.00625 12.5074 4.29375L10.1074 8.04375L12.7699 12.2063C12.9449 12.4938 12.9512 12.7813 12.7887 13.0688C12.6262 13.3563 12.3824 13.5 12.0574 13.5C11.9074 13.5 11.7699 13.4656 11.6449 13.3969C11.5199 13.3281 11.4199 13.2313 11.3449 13.1063L9.03868 9.43125H8.96368L6.65743 13.1063C6.58243 13.2313 6.4793 13.3281 6.34805 13.3969C6.2168 13.4656 6.08243 13.5 5.94493 13.5Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Subscript;
