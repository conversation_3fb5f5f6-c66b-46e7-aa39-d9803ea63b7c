import React from 'react'

const HelpIcon = () => {
  return (
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_499_3449" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="15" height="15">
        <rect x="0.335938" y="0.0146484" width="14" height="14" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_499_3449)">
        <path d="M7.30807 10.514C7.51224 10.514 7.68481 10.4435 7.82578 10.3025C7.96675 10.1616 8.03724 9.989 8.03724 9.78483C8.03724 9.58066 7.96675 9.40809 7.82578 9.26712C7.68481 9.12615 7.51224 9.05566 7.30807 9.05566C7.10391 9.05566 6.93134 9.12615 6.79036 9.26712C6.64939 9.40809 6.57891 9.58066 6.57891 9.78483C6.57891 9.989 6.64939 10.1616 6.79036 10.3025C6.93134 10.4435 7.10391 10.514 7.30807 10.514ZM7.33724 12.8473C6.53029 12.8473 5.77196 12.6942 5.06224 12.388C4.35252 12.0817 3.73516 11.6661 3.21016 11.1411C2.68516 10.6161 2.26953 9.99872 1.96328 9.289C1.65703 8.57928 1.50391 7.82094 1.50391 7.014C1.50391 6.20705 1.65703 5.44872 1.96328 4.739C2.26953 4.02927 2.68516 3.41191 3.21016 2.88691C3.73516 2.36191 4.35252 1.94629 5.06224 1.64004C5.77196 1.33379 6.53029 1.18066 7.33724 1.18066C8.14418 1.18066 8.90252 1.33379 9.61224 1.64004C10.322 1.94629 10.9393 2.36191 11.4643 2.88691C11.9893 3.41191 12.4049 4.02927 12.7112 4.739C13.0174 5.44872 13.1706 6.20705 13.1706 7.014C13.1706 7.82094 13.0174 8.57928 12.7112 9.289C12.4049 9.99872 11.9893 10.6161 11.4643 11.1411C10.9393 11.6661 10.322 12.0817 9.61224 12.388C8.90252 12.6942 8.14418 12.8473 7.33724 12.8473ZM7.39557 4.50566C7.63863 4.50566 7.85009 4.58344 8.02995 4.739C8.20981 4.89455 8.29974 5.089 8.29974 5.32233C8.29974 5.53622 8.23411 5.7258 8.10286 5.89108C7.97161 6.05636 7.82335 6.21191 7.65807 6.35775C7.43446 6.55219 7.23759 6.76608 7.06745 6.99941C6.89731 7.23275 6.81224 7.49525 6.81224 7.78691C6.81224 7.92302 6.86328 8.03726 6.96536 8.12962C7.06745 8.22198 7.18655 8.26816 7.32266 8.26816C7.46849 8.26816 7.59245 8.21955 7.69453 8.12233C7.79661 8.02511 7.86224 7.90358 7.89141 7.75775C7.93029 7.55358 8.0178 7.37129 8.15391 7.21087C8.29002 7.05046 8.43585 6.89733 8.59141 6.7515C8.81502 6.53761 9.00703 6.30428 9.16745 6.0515C9.32786 5.79872 9.40807 5.51678 9.40807 5.20566C9.40807 4.70983 9.20634 4.30393 8.80286 3.98796C8.39939 3.67198 7.93029 3.514 7.39557 3.514C7.02613 3.514 6.6737 3.59178 6.33828 3.74733C6.00286 3.90289 5.74766 4.14108 5.57266 4.46191C5.5046 4.57858 5.48273 4.70254 5.50703 4.83379C5.53134 4.96504 5.59696 5.06469 5.70391 5.13275C5.84002 5.21053 5.98099 5.23483 6.12682 5.20566C6.27266 5.1765 6.39418 5.09386 6.49141 4.95775C6.59835 4.81191 6.73203 4.70011 6.89245 4.62233C7.05286 4.54455 7.22057 4.50566 7.39557 4.50566Z" fill="#144296"/>
        </g>
    </svg>
  )
}

export default HelpIcon