import React from 'react';

const GraduationCap = () => {
    return (
        <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4174_29096"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="16"
                height="17"
            >
                <rect y="0.5" width="16" height="16" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4174_29096)">
                <path
                    d="M4.03477 12.35C3.81254 12.2278 3.64032 12.0639 3.5181 11.8583C3.39588 11.6528 3.33477 11.4222 3.33477 11.1667V7.96668L1.73477 7.08335C1.61254 7.01668 1.52365 6.93335 1.4681 6.83335C1.41254 6.73335 1.38477 6.62224 1.38477 6.50002C1.38477 6.37779 1.41254 6.26668 1.4681 6.16668C1.52365 6.06668 1.61254 5.98335 1.73477 5.91668L7.3681 2.85002C7.4681 2.79446 7.57088 2.75279 7.67643 2.72502C7.78199 2.69724 7.89032 2.68335 8.00143 2.68335C8.11254 2.68335 8.22088 2.69724 8.32643 2.72502C8.43199 2.75279 8.53477 2.79446 8.63477 2.85002L14.9848 6.31668C15.0959 6.37224 15.182 6.45279 15.2431 6.55835C15.3042 6.6639 15.3348 6.77779 15.3348 6.90002V11.1667C15.3348 11.3556 15.2709 11.5139 15.1431 11.6417C15.0153 11.7695 14.857 11.8333 14.6681 11.8333C14.4792 11.8333 14.3209 11.7695 14.1931 11.6417C14.0653 11.5139 14.0014 11.3556 14.0014 11.1667V7.23335L12.6681 7.96668V11.1667C12.6681 11.4222 12.607 11.6528 12.4848 11.8583C12.3625 12.0639 12.1903 12.2278 11.9681 12.35L8.63477 14.15C8.53477 14.2056 8.43199 14.2472 8.32643 14.275C8.22088 14.3028 8.11254 14.3167 8.00143 14.3167C7.89032 14.3167 7.78199 14.3028 7.67643 14.275C7.57088 14.2472 7.4681 14.2056 7.3681 14.15L4.03477 12.35ZM8.00143 8.96668L12.5681 6.50002L8.00143 4.03335L3.43477 6.50002L8.00143 8.96668ZM8.00143 12.9833L11.3348 11.1833V8.66668L8.65143 10.15C8.55143 10.2056 8.44588 10.2472 8.33477 10.275C8.22365 10.3028 8.11254 10.3167 8.00143 10.3167C7.89032 10.3167 7.77921 10.3028 7.6681 10.275C7.55699 10.2472 7.45143 10.2056 7.35143 10.15L4.6681 8.66668V11.1833L8.00143 12.9833Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default GraduationCap;
