import React from 'react'

const IntakeIcon = () => {
  return (
<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_9123_4945" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="21">
<rect x="0.855469" y="0.857422" width="20" height="20" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_9123_4945)">
<path d="M5.02214 19.1906C4.5638 19.1906 4.17144 19.0274 3.84505 18.701C3.51866 18.3746 3.35547 17.9823 3.35547 17.5239V5.85726C3.35547 5.39893 3.51866 5.00656 3.84505 4.68018C4.17144 4.35379 4.5638 4.19059 5.02214 4.19059H5.85547V3.35726C5.85547 3.12115 5.93533 2.92323 6.09505 2.76351C6.25477 2.60379 6.45269 2.52393 6.6888 2.52393C6.92491 2.52393 7.12283 2.60379 7.28255 2.76351C7.44227 2.92323 7.52214 3.12115 7.52214 3.35726V4.19059H14.1888V3.35726C14.1888 3.12115 14.2687 2.92323 14.4284 2.76351C14.5881 2.60379 14.786 2.52393 15.0221 2.52393C15.2582 2.52393 15.4562 2.60379 15.6159 2.76351C15.7756 2.92323 15.8555 3.12115 15.8555 3.35726V4.19059H16.6888C17.1471 4.19059 17.5395 4.35379 17.8659 4.68018C18.1923 5.00656 18.3555 5.39893 18.3555 5.85726V17.5239C18.3555 17.9823 18.1923 18.3746 17.8659 18.701C17.5395 19.0274 17.1471 19.1906 16.6888 19.1906H5.02214ZM5.02214 17.5239H16.6888V9.19059H5.02214V17.5239ZM5.02214 7.52393H16.6888V5.85726H5.02214V7.52393ZM10.8555 12.5239C10.6194 12.5239 10.4214 12.4441 10.2617 12.2843C10.102 12.1246 10.0221 11.9267 10.0221 11.6906C10.0221 11.4545 10.102 11.2566 10.2617 11.0968C10.4214 10.9371 10.6194 10.8573 10.8555 10.8573C11.0916 10.8573 11.2895 10.9371 11.4492 11.0968C11.6089 11.2566 11.6888 11.4545 11.6888 11.6906C11.6888 11.9267 11.6089 12.1246 11.4492 12.2843C11.2895 12.4441 11.0916 12.5239 10.8555 12.5239ZM7.52214 12.5239C7.28602 12.5239 7.08811 12.4441 6.92839 12.2843C6.76866 12.1246 6.6888 11.9267 6.6888 11.6906C6.6888 11.4545 6.76866 11.2566 6.92839 11.0968C7.08811 10.9371 7.28602 10.8573 7.52214 10.8573C7.75825 10.8573 7.95616 10.9371 8.11589 11.0968C8.27561 11.2566 8.35547 11.4545 8.35547 11.6906C8.35547 11.9267 8.27561 12.1246 8.11589 12.2843C7.95616 12.4441 7.75825 12.5239 7.52214 12.5239ZM14.1888 12.5239C13.9527 12.5239 13.7548 12.4441 13.5951 12.2843C13.4353 12.1246 13.3555 11.9267 13.3555 11.6906C13.3555 11.4545 13.4353 11.2566 13.5951 11.0968C13.7548 10.9371 13.9527 10.8573 14.1888 10.8573C14.4249 10.8573 14.6228 10.9371 14.7826 11.0968C14.9423 11.2566 15.0221 11.4545 15.0221 11.6906C15.0221 11.9267 14.9423 12.1246 14.7826 12.2843C14.6228 12.4441 14.4249 12.5239 14.1888 12.5239ZM10.8555 15.8573C10.6194 15.8573 10.4214 15.7774 10.2617 15.6177C10.102 15.458 10.0221 15.26 10.0221 15.0239C10.0221 14.7878 10.102 14.5899 10.2617 14.4302C10.4214 14.2705 10.6194 14.1906 10.8555 14.1906C11.0916 14.1906 11.2895 14.2705 11.4492 14.4302C11.6089 14.5899 11.6888 14.7878 11.6888 15.0239C11.6888 15.26 11.6089 15.458 11.4492 15.6177C11.2895 15.7774 11.0916 15.8573 10.8555 15.8573ZM7.52214 15.8573C7.28602 15.8573 7.08811 15.7774 6.92839 15.6177C6.76866 15.458 6.6888 15.26 6.6888 15.0239C6.6888 14.7878 6.76866 14.5899 6.92839 14.4302C7.08811 14.2705 7.28602 14.1906 7.52214 14.1906C7.75825 14.1906 7.95616 14.2705 8.11589 14.4302C8.27561 14.5899 8.35547 14.7878 8.35547 15.0239C8.35547 15.26 8.27561 15.458 8.11589 15.6177C7.95616 15.7774 7.75825 15.8573 7.52214 15.8573ZM14.1888 15.8573C13.9527 15.8573 13.7548 15.7774 13.5951 15.6177C13.4353 15.458 13.3555 15.26 13.3555 15.0239C13.3555 14.7878 13.4353 14.5899 13.5951 14.4302C13.7548 14.2705 13.9527 14.1906 14.1888 14.1906C14.4249 14.1906 14.6228 14.2705 14.7826 14.4302C14.9423 14.5899 15.0221 14.7878 15.0221 15.0239C15.0221 15.26 14.9423 15.458 14.7826 15.6177C14.6228 15.7774 14.4249 15.8573 14.1888 15.8573Z" fill="#1E62E0"/>
</g>
</svg>

  )
}

export default IntakeIcon;
