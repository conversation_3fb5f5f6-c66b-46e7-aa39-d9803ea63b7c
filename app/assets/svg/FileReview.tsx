import React from 'react'

const FileReview = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_7148_4243)">
    <path d="M3.3335 0.833496C1.95278 0.833496 0.833496 1.95278 0.833496 3.3335V12.6668C0.833496 14.0476 1.95278 15.1668 3.3335 15.1668H8.66683C8.94296 15.1668 9.16683 14.943 9.16683 14.6668C9.16683 14.3907 8.94296 14.1668 8.66683 14.1668H3.3335C2.50507 14.1668 1.8335 13.4952 1.8335 12.6668V3.3335C1.8335 2.50507 2.50507 1.8335 3.3335 1.8335H7.79303L11.5002 5.5406V6.66683C11.5002 6.94296 11.724 7.16683 12.0002 7.16683C12.2763 7.16683 12.5002 6.94296 12.5002 6.66683V5.3335C12.5002 5.20089 12.4475 5.07371 12.3537 4.97994L8.3537 0.979943C8.25996 0.886176 8.13276 0.833496 8.00016 0.833496H3.3335Z" fill="#1E62E0"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M7.80867 0.871569C7.99547 0.794183 8.21053 0.836956 8.35353 0.979956L12.3535 4.97996C12.4965 5.12296 12.5393 5.33802 12.4619 5.52485C12.3845 5.71169 12.2022 5.83351 12 5.83351H10C8.61927 5.83351 7.5 4.71422 7.5 3.33351V1.33351C7.5 1.13128 7.6218 0.948963 7.80867 0.871569ZM8.5 2.54062V3.33351C8.5 4.16194 9.1716 4.83351 10 4.83351H10.7929L8.5 2.54062Z" fill="#1E62E0"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M10.3333 8.5C9.3208 8.5 8.5 9.3208 8.5 10.3333C8.5 11.3459 9.3208 12.1667 10.3333 12.1667C11.3459 12.1667 12.1667 11.3459 12.1667 10.3333C12.1667 9.3208 11.3459 8.5 10.3333 8.5ZM7.5 10.3333C7.5 8.76853 8.76853 7.5 10.3333 7.5C11.8981 7.5 13.1667 8.76853 13.1667 10.3333C13.1667 11.8981 11.8981 13.1667 10.3333 13.1667C8.76853 13.1667 7.5 11.8981 7.5 10.3333Z" fill="#1E62E0"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M11.6465 11.6465C11.8417 11.4512 12.1583 11.4512 12.3535 11.6465L15.0202 14.3131C15.2155 14.5084 15.2155 14.8249 15.0202 15.0202C14.8249 15.2155 14.5084 15.2155 14.3131 15.0202L11.6465 12.3535C11.4512 12.1583 11.4512 11.8417 11.6465 11.6465Z" fill="#1E62E0"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M2.8335 4.66699C2.8335 4.39085 3.05736 4.16699 3.3335 4.16699H6.66683C6.94296 4.16699 7.16683 4.39085 7.16683 4.66699C7.16683 4.94313 6.94296 5.16699 6.66683 5.16699H3.3335C3.05736 5.16699 2.8335 4.94313 2.8335 4.66699Z" fill="#1E62E0"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M2.8335 8C2.8335 7.72387 3.05736 7.5 3.3335 7.5H6.66683C6.94296 7.5 7.16683 7.72387 7.16683 8C7.16683 8.27613 6.94296 8.5 6.66683 8.5H3.3335C3.05736 8.5 2.8335 8.27613 2.8335 8Z" fill="#1E62E0"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M2.8335 11.3335C2.8335 11.0574 3.05736 10.8335 3.3335 10.8335H6.66683C6.94296 10.8335 7.16683 11.0574 7.16683 11.3335C7.16683 11.6096 6.94296 11.8335 6.66683 11.8335H3.3335C3.05736 11.8335 2.8335 11.6096 2.8335 11.3335Z" fill="#1E62E0"/>
    </g>
    <defs>
    <clipPath id="clip0_7148_4243">
    <rect width="16" height="16" fill="white"/>
    </clipPath>
    </defs>
    </svg>
  )
}

export default FileReview
