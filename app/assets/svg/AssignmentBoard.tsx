import React from 'react'

const AssignmentBoard = () => {
  return (
<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2171_5158" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="17" height="17">
<rect width="17" height="17" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_2171_5158)">
<path d="M4.20825 13.9301C3.87061 13.9301 3.58157 13.8099 3.34113 13.5695C3.10069 13.329 2.98047 13.04 2.98047 12.7023V4.1079C2.98047 3.77026 3.10069 3.48122 3.34113 3.24078C3.58157 3.00034 3.87061 2.88012 4.20825 2.88012H6.78658C6.92982 2.51179 7.15491 2.21508 7.46186 1.98998C7.7688 1.76489 8.11667 1.65234 8.50547 1.65234C8.89426 1.65234 9.24213 1.76489 9.54908 1.98998C9.85602 2.21508 10.0811 2.51179 10.2244 2.88012H12.8027C13.1403 2.88012 13.4294 3.00034 13.6698 3.24078C13.9102 3.48122 14.0305 3.77026 14.0305 4.1079V12.7023C14.0305 13.04 13.9102 13.329 13.6698 13.5695C13.4294 13.8099 13.1403 13.9301 12.8027 13.9301H4.20825ZM8.50547 3.64748C8.63848 3.64748 8.74847 3.604 8.83543 3.51703C8.9224 3.43006 8.96588 3.32008 8.96588 3.18707C8.96588 3.05406 8.9224 2.94407 8.83543 2.8571C8.74847 2.77013 8.63848 2.72665 8.50547 2.72665C8.37246 2.72665 8.26247 2.77013 8.1755 2.8571C8.08854 2.94407 8.04505 3.05406 8.04505 3.18707C8.04505 3.32008 8.08854 3.43006 8.1755 3.51703C8.26247 3.604 8.37246 3.64748 8.50547 3.64748ZM4.20825 11.9964C4.76075 11.4541 5.40277 11.0269 6.13432 10.7149C6.86587 10.4028 7.65626 10.2468 8.50547 10.2468C9.35468 10.2468 10.1451 10.4028 10.8766 10.7149C11.6082 11.0269 12.2502 11.4541 12.8027 11.9964V4.1079H4.20825V11.9964ZM8.50547 9.01901C9.09889 9.01901 9.60535 8.80927 10.0248 8.38977C10.4443 7.97028 10.6541 7.46383 10.6541 6.8704C10.6541 6.27697 10.4443 5.77052 10.0248 5.35102C9.60535 4.93153 9.09889 4.72179 8.50547 4.72179C7.91204 4.72179 7.40558 4.93153 6.98609 5.35102C6.5666 5.77052 6.35686 6.27697 6.35686 6.8704C6.35686 7.46383 6.5666 7.97028 6.98609 8.38977C7.40558 8.80927 7.91204 9.01901 8.50547 9.01901ZM5.43602 12.7023H11.5749V12.5489C11.1452 12.1908 10.6694 11.9222 10.1476 11.7431C9.62581 11.5641 9.07843 11.4746 8.50547 11.4746C7.93251 11.4746 7.38512 11.5641 6.86332 11.7431C6.34151 11.9222 5.86575 12.1908 5.43602 12.5489V12.7023ZM8.50547 7.79123C8.24968 7.79123 8.03226 7.70171 7.85321 7.52266C7.67416 7.34361 7.58463 7.12619 7.58463 6.8704C7.58463 6.61461 7.67416 6.39719 7.85321 6.21814C8.03226 6.03909 8.24968 5.94957 8.50547 5.94957C8.76125 5.94957 8.97867 6.03909 9.15772 6.21814C9.33678 6.39719 9.4263 6.61461 9.4263 6.8704C9.4263 7.12619 9.33678 7.34361 9.15772 7.52266C8.97867 7.70171 8.76125 7.79123 8.50547 7.79123Z" fill="#1E62E0"/>
</g>
</svg>
  )
}

export default AssignmentBoard;


