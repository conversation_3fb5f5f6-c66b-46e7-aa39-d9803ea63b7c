import React from 'react'

const photoUpload = () => {
    return (
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_490_2795" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="-1" y="0" width="19" height="18">
            <rect x="-0.0078125" y="0.300781" width="17.2096" height="17.2096" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_490_2795)">
            <path d="M8.59644 12.8496C9.49277 12.8496 10.2547 12.5359 10.8821 11.9085C11.5095 11.281 11.8232 10.5191 11.8232 9.6228C11.8232 8.72647 11.5095 7.96459 10.8821 7.33716C10.2547 6.70973 9.49277 6.39601 8.59644 6.39601C7.70011 6.39601 6.93822 6.70973 6.31079 7.33716C5.68336 7.96459 5.36964 8.72647 5.36964 9.6228C5.36964 10.5191 5.68336 11.281 6.31079 11.9085C6.93822 12.5359 7.70011 12.8496 8.59644 12.8496ZM8.59644 11.4155C8.09449 11.4155 7.67023 11.2422 7.32365 10.8956C6.97706 10.549 6.80377 10.1248 6.80377 9.6228C6.80377 9.12086 6.97706 8.69659 7.32365 8.35001C7.67023 8.00343 8.09449 7.83014 8.59644 7.83014C9.09838 7.83014 9.52265 8.00343 9.86923 8.35001C10.2158 8.69659 10.3891 9.12086 10.3891 9.6228C10.3891 10.1248 10.2158 10.549 9.86923 10.8956C9.52265 11.2422 9.09838 11.4155 8.59644 11.4155ZM2.85991 15.3593C2.46553 15.3593 2.12791 15.2189 1.84706 14.9381C1.56621 14.6572 1.42578 14.3196 1.42578 13.9252V5.32041C1.42578 4.92602 1.56621 4.58841 1.84706 4.30756C2.12791 4.02671 2.46553 3.88628 2.85991 3.88628H5.11867L6.015 2.91824C6.14646 2.77483 6.30482 2.66129 6.49006 2.57763C6.6753 2.49398 6.8695 2.45215 7.07267 2.45215H10.1202C10.3234 2.45215 10.5176 2.49398 10.7028 2.57763C10.8881 2.66129 11.0464 2.77483 11.1779 2.91824L12.0742 3.88628H14.333C14.7273 3.88628 15.065 4.02671 15.3458 4.30756C15.6267 4.58841 15.7671 4.92602 15.7671 5.32041V13.9252C15.7671 14.3196 15.6267 14.6572 15.3458 14.9381C15.065 15.2189 14.7273 15.3593 14.333 15.3593H2.85991ZM2.85991 13.9252H14.333V5.32041H11.4288L10.1202 3.88628H7.07267L5.76403 5.32041H2.85991V13.9252Z" fill="#9FA0A6"/>
            </g>
        </svg>
    )
}

export default photoUpload