import React from 'react';

const CostofLivingIcon = () => {
    return (
        <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27046"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="19"
                height="18"
            >
                <rect x="0.664062" width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27046)">
                <path
                    d="M3.66523 14.25V8.71875L2.91523 9.3C2.74023 9.425 2.55273 9.475 2.35273 9.45C2.15273 9.425 1.99023 9.325 1.86523 9.15C1.74023 8.975 1.69023 8.7875 1.71523 8.5875C1.74023 8.3875 1.83398 8.225 1.99648 8.1L3.66523 6.825V5.25C3.66523 5.0375 3.73711 4.85937 3.88086 4.71562C4.02461 4.57187 4.20273 4.5 4.41523 4.5C4.62773 4.5 4.80586 4.57187 4.94961 4.71562C5.09336 4.85937 5.16523 5.0375 5.16523 5.25V5.68125L8.74648 2.94375C9.02148 2.73125 9.32773 2.625 9.66523 2.625C10.0027 2.625 10.309 2.73125 10.584 2.94375L17.334 8.1C17.4965 8.225 17.5902 8.3875 17.6152 8.5875C17.6402 8.7875 17.5902 8.975 17.4652 9.15C17.3402 9.3125 17.1777 9.40625 16.9777 9.43125C16.7777 9.45625 16.5965 9.40625 16.434 9.28125L15.6652 8.71875V14.25C15.6652 14.6625 15.5184 15.0156 15.2246 15.3094C14.9309 15.6031 14.5777 15.75 14.1652 15.75H5.16523C4.75273 15.75 4.39961 15.6031 4.10586 15.3094C3.81211 15.0156 3.66523 14.6625 3.66523 14.25ZM5.16523 14.25H8.91523V12C8.91523 11.7875 8.98711 11.6094 9.13086 11.4656C9.27461 11.3219 9.45273 11.25 9.66523 11.25C9.87773 11.25 10.0559 11.3219 10.1996 11.4656C10.3434 11.6094 10.4152 11.7875 10.4152 12V14.25H14.1652V7.575L9.66523 4.14375L5.16523 7.575V14.25ZM4.64023 3.75C4.35273 3.75 4.13086 3.63125 3.97461 3.39375C3.81836 3.15625 3.80898 2.9125 3.94648 2.6625C4.15898 2.3 4.43711 2.01562 4.78086 1.80937C5.12461 1.60312 5.50273 1.5 5.91523 1.5C6.05273 1.5 6.18398 1.46562 6.30898 1.39687C6.43398 1.32812 6.52773 1.23125 6.59023 1.10625C6.65273 0.99375 6.73711 0.90625 6.84336 0.84375C6.94961 0.78125 7.07148 0.75 7.20898 0.75C7.49648 0.75 7.71523 0.86875 7.86523 1.10625C8.01523 1.34375 8.02148 1.5875 7.88398 1.8375C7.67148 2.2 7.39336 2.48437 7.04961 2.69062C6.70586 2.89687 6.32773 3 5.91523 3C5.77773 3 5.64648 3.03125 5.52148 3.09375C5.39648 3.15625 5.30273 3.25625 5.24023 3.39375C5.17773 3.50625 5.09648 3.59375 4.99648 3.65625C4.89648 3.71875 4.77773 3.75 4.64023 3.75Z"
                    fill="#36373B"
                />
            </g>
        </svg>
    );
};

export default CostofLivingIcon;
