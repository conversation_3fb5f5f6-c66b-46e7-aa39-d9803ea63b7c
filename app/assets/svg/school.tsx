import React from 'react';

const School = () => {
    return (
        <svg
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2620_4392"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="20"
                height="21"
            >
                <rect y="0.989258" width="20" height="20" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2620_4392)">
                <path
                    d="M5.04102 15.8021C4.76324 15.6493 4.54796 15.4444 4.39518 15.1875C4.2424 14.9306 4.16602 14.6424 4.16602 14.3229V10.3229L2.16602 9.21875C2.01324 9.13542 1.90213 9.03125 1.83268 8.90625C1.76324 8.78125 1.72852 8.64236 1.72852 8.48958C1.72852 8.33681 1.76324 8.19792 1.83268 8.07292C1.90213 7.94792 2.01324 7.84375 2.16602 7.76042L9.20768 3.92708C9.33268 3.85764 9.46115 3.80556 9.5931 3.77083C9.72504 3.73611 9.86046 3.71875 9.99935 3.71875C10.1382 3.71875 10.2737 3.73611 10.4056 3.77083C10.5375 3.80556 10.666 3.85764 10.791 3.92708L18.7285 8.26042C18.8674 8.32986 18.975 8.43056 19.0514 8.5625C19.1278 8.69444 19.166 8.83681 19.166 8.98958V14.3229C19.166 14.559 19.0862 14.7569 18.9264 14.9167C18.7667 15.0764 18.5688 15.1562 18.3327 15.1562C18.0966 15.1562 17.8987 15.0764 17.7389 14.9167C17.5792 14.7569 17.4993 14.559 17.4993 14.3229V9.40625L15.8327 10.3229V14.3229C15.8327 14.6424 15.7563 14.9306 15.6035 15.1875C15.4507 15.4444 15.2355 15.6493 14.9577 15.8021L10.791 18.0521C10.666 18.1215 10.5375 18.1736 10.4056 18.2083C10.2737 18.2431 10.1382 18.2604 9.99935 18.2604C9.86046 18.2604 9.72504 18.2431 9.5931 18.2083C9.46115 18.1736 9.33268 18.1215 9.20768 18.0521L5.04102 15.8021ZM9.99935 11.5729L15.7077 8.48958L9.99935 5.40625L4.29102 8.48958L9.99935 11.5729ZM9.99935 16.5938L14.166 14.3438V11.1979L10.8118 13.0521C10.6868 13.1215 10.5549 13.1736 10.416 13.2083C10.2771 13.2431 10.1382 13.2604 9.99935 13.2604C9.86046 13.2604 9.72157 13.2431 9.58268 13.2083C9.44379 13.1736 9.31185 13.1215 9.18685 13.0521L5.83268 11.1979V14.3438L9.99935 16.5938Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default School;
