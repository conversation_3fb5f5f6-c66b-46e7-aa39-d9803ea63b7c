import React from 'react';
import { xIconProps } from '@/types';

const ListBulleted: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27578"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27578)">
                <path
                    d="M7.5 14.25C7.2875 14.25 7.10938 14.1781 6.96562 14.0344C6.82188 13.8906 6.75 13.7125 6.75 13.5C6.75 13.2875 6.82188 13.1094 6.96562 12.9656C7.10938 12.8219 7.2875 12.75 7.5 12.75H15C15.2125 12.75 15.3906 12.8219 15.5344 12.9656C15.6781 13.1094 15.75 13.2875 15.75 13.5C15.75 13.7125 15.6781 13.8906 15.5344 14.0344C15.3906 14.1781 15.2125 14.25 15 14.25H7.5ZM7.5 9.75C7.2875 9.75 7.10938 9.67813 6.96562 9.53438C6.82188 9.39063 6.75 9.2125 6.75 9C6.75 8.7875 6.82188 8.60938 6.96562 8.46562C7.10938 8.32188 7.2875 8.25 7.5 8.25H15C15.2125 8.25 15.3906 8.32188 15.5344 8.46562C15.6781 8.60938 15.75 8.7875 15.75 9C15.75 9.2125 15.6781 9.39063 15.5344 9.53438C15.3906 9.67813 15.2125 9.75 15 9.75H7.5ZM7.5 5.25C7.2875 5.25 7.10938 5.17813 6.96562 5.03438C6.82188 4.89063 6.75 4.7125 6.75 4.5C6.75 4.2875 6.82188 4.10938 6.96562 3.96563C7.10938 3.82188 7.2875 3.75 7.5 3.75H15C15.2125 3.75 15.3906 3.82188 15.5344 3.96563C15.6781 4.10938 15.75 4.2875 15.75 4.5C15.75 4.7125 15.6781 4.89063 15.5344 5.03438C15.3906 5.17813 15.2125 5.25 15 5.25H7.5ZM3.75 15C3.3375 15 2.98438 14.8531 2.69063 14.5594C2.39688 14.2656 2.25 13.9125 2.25 13.5C2.25 13.0875 2.39688 12.7344 2.69063 12.4406C2.98438 12.1469 3.3375 12 3.75 12C4.1625 12 4.51562 12.1469 4.80938 12.4406C5.10313 12.7344 5.25 13.0875 5.25 13.5C5.25 13.9125 5.10313 14.2656 4.80938 14.5594C4.51562 14.8531 4.1625 15 3.75 15ZM3.75 10.5C3.3375 10.5 2.98438 10.3531 2.69063 10.0594C2.39688 9.76563 2.25 9.4125 2.25 9C2.25 8.5875 2.39688 8.23438 2.69063 7.94063C2.98438 7.64687 3.3375 7.5 3.75 7.5C4.1625 7.5 4.51562 7.64687 4.80938 7.94063C5.10313 8.23438 5.25 8.5875 5.25 9C5.25 9.4125 5.10313 9.76563 4.80938 10.0594C4.51562 10.3531 4.1625 10.5 3.75 10.5ZM3.75 6C3.3375 6 2.98438 5.85313 2.69063 5.55938C2.39688 5.26562 2.25 4.9125 2.25 4.5C2.25 4.0875 2.39688 3.73438 2.69063 3.44063C2.98438 3.14688 3.3375 3 3.75 3C4.1625 3 4.51562 3.14688 4.80938 3.44063C5.10313 3.73438 5.25 4.0875 5.25 4.5C5.25 4.9125 5.10313 5.26562 4.80938 5.55938C4.51562 5.85313 4.1625 6 3.75 6Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default ListBulleted;
