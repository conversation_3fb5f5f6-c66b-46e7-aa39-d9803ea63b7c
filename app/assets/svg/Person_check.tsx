import React from 'react'

const Person_check = () => {
    return (
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_8501_25155" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
                <rect y="0.5" width="14" height="14" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_8501_25155)">
                <path d="M10.2372 5.85225L12.2934 3.78141C12.4101 3.66475 12.5486 3.60641 12.7091 3.60641C12.8695 3.60641 13.008 3.66475 13.1247 3.78141C13.2413 3.89808 13.2997 4.03662 13.2997 4.19704C13.2997 4.35745 13.2413 4.496 13.1247 4.61266L10.6455 7.09183C10.5288 7.2085 10.3927 7.26683 10.2372 7.26683C10.0816 7.26683 9.94551 7.2085 9.82884 7.09183L8.58926 5.85225C8.47259 5.73558 8.41426 5.59704 8.41426 5.43662C8.41426 5.2762 8.47259 5.13766 8.58926 5.021C8.70592 4.90433 8.84204 4.846 8.99759 4.846C9.15315 4.846 9.28926 4.90433 9.40592 5.021L10.2372 5.85225ZM5.24967 7.50016C4.60801 7.50016 4.0587 7.27169 3.60176 6.81475C3.14481 6.3578 2.91634 5.8085 2.91634 5.16683C2.91634 4.52516 3.14481 3.97586 3.60176 3.51891C4.0587 3.06197 4.60801 2.8335 5.24967 2.8335C5.89134 2.8335 6.44065 3.06197 6.89759 3.51891C7.35454 3.97586 7.58301 4.52516 7.58301 5.16683C7.58301 5.8085 7.35454 6.3578 6.89759 6.81475C6.44065 7.27169 5.89134 7.50016 5.24967 7.50016ZM0.583008 11.0002V10.5335C0.583008 10.2029 0.668077 9.89912 0.838216 9.62204C1.00836 9.34495 1.2344 9.1335 1.51634 8.98766C2.11912 8.68627 2.73162 8.46023 3.35384 8.30954C3.97606 8.15884 4.60801 8.0835 5.24967 8.0835C5.89134 8.0835 6.52329 8.15884 7.14551 8.30954C7.76773 8.46023 8.38023 8.68627 8.98301 8.98766C9.26495 9.1335 9.49099 9.34495 9.66113 9.62204C9.83127 9.89912 9.91634 10.2029 9.91634 10.5335V11.0002C9.91634 11.321 9.80211 11.5956 9.57363 11.8241C9.34516 12.0526 9.07051 12.1668 8.74967 12.1668H1.74967C1.42884 12.1668 1.15419 12.0526 0.925716 11.8241C0.697244 11.5956 0.583008 11.321 0.583008 11.0002ZM1.74967 11.0002H8.74967V10.5335C8.74967 10.4266 8.72294 10.3293 8.66947 10.2418C8.61599 10.1543 8.54551 10.0863 8.45801 10.0377C7.93301 9.77516 7.40315 9.57829 6.86842 9.44704C6.3337 9.31579 5.79412 9.25016 5.24967 9.25016C4.70523 9.25016 4.16565 9.31579 3.63092 9.44704C3.0962 9.57829 2.56634 9.77516 2.04134 10.0377C1.95384 10.0863 1.88336 10.1543 1.82988 10.2418C1.77641 10.3293 1.74967 10.4266 1.74967 10.5335V11.0002ZM5.24967 6.3335C5.57051 6.3335 5.84516 6.21926 6.07363 5.99079C6.3021 5.76232 6.41634 5.48766 6.41634 5.16683C6.41634 4.846 6.3021 4.57134 6.07363 4.34287C5.84516 4.1144 5.57051 4.00016 5.24967 4.00016C4.92884 4.00016 4.65419 4.1144 4.42572 4.34287C4.19724 4.57134 4.08301 4.846 4.08301 5.16683C4.08301 5.48766 4.19724 5.76232 4.42572 5.99079C4.65419 6.21926 4.92884 6.3335 5.24967 6.3335Z" fill="#57585E" />
            </g>
        </svg>
    )
}

export default Person_check