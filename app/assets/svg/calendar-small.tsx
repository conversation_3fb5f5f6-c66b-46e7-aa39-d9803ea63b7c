import React from 'react';
import { IconProps } from '@/types';

const CalendarSmall: React.FC<IconProps> = ({
    className
}) => {
    return (
        <svg
            className={className}
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4174_28994"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="16"
                height="17"
            >
                <rect y="0.5" width="16" height="16" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4174_28994)">
                <path
                    d="M3.33333 15.1666C2.96667 15.1666 2.65278 15.036 2.39167 14.7749C2.13056 14.5138 2 14.1999 2 13.8333V4.49992C2 4.13325 2.13056 3.81936 2.39167 3.55825C2.65278 3.29714 2.96667 3.16659 3.33333 3.16659H4V2.49992C4 2.31103 4.06389 2.1527 4.19167 2.02492C4.31944 1.89714 4.47778 1.83325 4.66667 1.83325C4.85556 1.83325 5.01389 1.89714 5.14167 2.02492C5.26944 2.1527 5.33333 2.31103 5.33333 2.49992V3.16659H10.6667V2.49992C10.6667 2.31103 10.7306 2.1527 10.8583 2.02492C10.9861 1.89714 11.1444 1.83325 11.3333 1.83325C11.5222 1.83325 11.6806 1.89714 11.8083 2.02492C11.9361 2.1527 12 2.31103 12 2.49992V3.16659H12.6667C13.0333 3.16659 13.3472 3.29714 13.6083 3.55825C13.8694 3.81936 14 4.13325 14 4.49992V13.8333C14 14.1999 13.8694 14.5138 13.6083 14.7749C13.3472 15.036 13.0333 15.1666 12.6667 15.1666H3.33333ZM3.33333 13.8333H12.6667V7.16659H3.33333V13.8333Z"
                    fill="#7A7B82"
                />
            </g>
        </svg>
    );
};

export default CalendarSmall;
