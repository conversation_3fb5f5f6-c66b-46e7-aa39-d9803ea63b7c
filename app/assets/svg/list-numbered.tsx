import { xIconProps } from '@/types';
import React from 'react';

const ListNumbered: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27575"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27575)">
                <path
                    d="M2.8125 16.5C2.65 16.5 2.51562 16.4469 2.40938 16.3406C2.30313 16.2344 2.25 16.1 2.25 15.9375C2.25 15.775 2.30313 15.6406 2.40938 15.5344C2.51562 15.4281 2.65 15.375 2.8125 15.375H4.125V14.8125H3.5625C3.4 14.8125 3.26562 14.7594 3.15937 14.6531C3.05312 14.5469 3 14.4125 3 14.25C3 14.0875 3.05312 13.9531 3.15937 13.8469C3.26562 13.7406 3.4 13.6875 3.5625 13.6875H4.125V13.125H2.8125C2.65 13.125 2.51562 13.0719 2.40938 12.9656C2.30313 12.8594 2.25 12.725 2.25 12.5625C2.25 12.4 2.30313 12.2656 2.40938 12.1594C2.51562 12.0531 2.65 12 2.8125 12H4.5C4.7125 12 4.89062 12.0719 5.03438 12.2156C5.17813 12.3594 5.25 12.5375 5.25 12.75V13.5C5.25 13.7125 5.17813 13.8906 5.03438 14.0344C4.89062 14.1781 4.7125 14.25 4.5 14.25C4.7125 14.25 4.89062 14.3219 5.03438 14.4656C5.17813 14.6094 5.25 14.7875 5.25 15V15.75C5.25 15.9625 5.17813 16.1406 5.03438 16.2844C4.89062 16.4281 4.7125 16.5 4.5 16.5H2.8125ZM2.8125 11.25C2.65 11.25 2.51562 11.1969 2.40938 11.0906C2.30313 10.9844 2.25 10.85 2.25 10.6875V9.1875C2.25 8.975 2.32187 8.79688 2.46562 8.65313C2.60938 8.50938 2.7875 8.4375 3 8.4375H4.125V7.875H2.8125C2.65 7.875 2.51562 7.82188 2.40938 7.71563C2.30313 7.60938 2.25 7.475 2.25 7.3125C2.25 7.15 2.30313 7.01562 2.40938 6.90938C2.51562 6.80312 2.65 6.75 2.8125 6.75H4.5C4.7125 6.75 4.89062 6.82188 5.03438 6.96562C5.17813 7.10938 5.25 7.2875 5.25 7.5V8.8125C5.25 9.025 5.17813 9.20313 5.03438 9.34688C4.89062 9.49063 4.7125 9.5625 4.5 9.5625H3.375V10.125H4.6875C4.85 10.125 4.98438 10.1781 5.09063 10.2844C5.19688 10.3906 5.25 10.525 5.25 10.6875C5.25 10.85 5.19688 10.9844 5.09063 11.0906C4.98438 11.1969 4.85 11.25 4.6875 11.25H2.8125ZM3.9375 6C3.775 6 3.64062 5.94688 3.53437 5.84062C3.42813 5.73438 3.375 5.6 3.375 5.4375V2.625H2.8125C2.65 2.625 2.51562 2.57187 2.40938 2.46563C2.30313 2.35938 2.25 2.225 2.25 2.0625C2.25 1.9 2.30313 1.76562 2.40938 1.65938C2.51562 1.55313 2.65 1.5 2.8125 1.5H3.9375C4.1 1.5 4.23438 1.55313 4.34063 1.65938C4.44688 1.76562 4.5 1.9 4.5 2.0625V5.4375C4.5 5.6 4.44688 5.73438 4.34063 5.84062C4.23438 5.94688 4.1 6 3.9375 6ZM7.5 14.25C7.2875 14.25 7.10938 14.1781 6.96562 14.0344C6.82188 13.8906 6.75 13.7125 6.75 13.5C6.75 13.2875 6.82188 13.1094 6.96562 12.9656C7.10938 12.8219 7.2875 12.75 7.5 12.75H15C15.2125 12.75 15.3906 12.8219 15.5344 12.9656C15.6781 13.1094 15.75 13.2875 15.75 13.5C15.75 13.7125 15.6781 13.8906 15.5344 14.0344C15.3906 14.1781 15.2125 14.25 15 14.25H7.5ZM7.5 9.75C7.2875 9.75 7.10938 9.67813 6.96562 9.53438C6.82188 9.39063 6.75 9.2125 6.75 9C6.75 8.7875 6.82188 8.60938 6.96562 8.46563C7.10938 8.32188 7.2875 8.25 7.5 8.25H15C15.2125 8.25 15.3906 8.32188 15.5344 8.46563C15.6781 8.60938 15.75 8.7875 15.75 9C15.75 9.2125 15.6781 9.39063 15.5344 9.53438C15.3906 9.67813 15.2125 9.75 15 9.75H7.5ZM7.5 5.25C7.2875 5.25 7.10938 5.17813 6.96562 5.03438C6.82188 4.89062 6.75 4.7125 6.75 4.5C6.75 4.2875 6.82188 4.10938 6.96562 3.96563C7.10938 3.82188 7.2875 3.75 7.5 3.75H15C15.2125 3.75 15.3906 3.82188 15.5344 3.96563C15.6781 4.10938 15.75 4.2875 15.75 4.5C15.75 4.7125 15.6781 4.89062 15.5344 5.03438C15.3906 5.17813 15.2125 5.25 15 5.25H7.5Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default ListNumbered;
