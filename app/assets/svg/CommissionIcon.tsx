import React from 'react'

const CommissionIcon = () => {
  return (
<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_9123_5071" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="21">
<rect x="0.862305" y="0.482422" width="20" height="20" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_9123_5071)">
<path d="M12.4875 17.4823L17.4458 15.9406C17.3764 15.8156 17.2757 15.708 17.1437 15.6177C17.0118 15.5274 16.8625 15.4823 16.6958 15.4823H12.4875C12.1125 15.4823 11.8139 15.4684 11.5916 15.4406C11.3694 15.4128 11.1402 15.3573 10.9041 15.2739L9.71663 14.8781C9.49441 14.8086 9.33469 14.6698 9.23747 14.4614C9.14025 14.2531 9.12636 14.0378 9.1958 13.8156C9.26524 13.5934 9.40066 13.4302 9.60205 13.326C9.80344 13.2218 10.0152 13.2045 10.2375 13.2739L11.1125 13.5656C11.3486 13.635 11.6159 13.6906 11.9146 13.7323C12.2132 13.7739 12.6194 13.8017 13.1333 13.8156H13.3625C13.3625 13.6628 13.3173 13.517 13.2271 13.3781C13.1368 13.2392 13.0291 13.1489 12.9041 13.1073L8.02913 11.3156H6.6958V15.8989L12.4875 17.4823ZM12.05 19.1073L6.6958 17.6073C6.58469 17.9684 6.36594 18.26 6.03955 18.4823C5.71316 18.7045 5.37636 18.8156 5.02913 18.8156H3.36247C2.90413 18.8156 2.51177 18.6524 2.18538 18.326C1.859 17.9996 1.6958 17.6073 1.6958 17.1489V11.3156C1.6958 10.8573 1.859 10.4649 2.18538 10.1385C2.51177 9.81212 2.90413 9.64893 3.36247 9.64893H8.02913C8.12636 9.64893 8.22358 9.65934 8.3208 9.68018C8.41802 9.70101 8.5083 9.72531 8.59163 9.75309L13.4875 11.5656C13.9458 11.7323 14.3173 12.0239 14.6021 12.4406C14.8868 12.8573 15.0291 13.3156 15.0291 13.8156H16.6958C17.3902 13.8156 17.9805 14.0448 18.4666 14.5031C18.9527 14.9614 19.1958 15.5656 19.1958 16.3156C19.1958 16.6211 19.1159 16.8607 18.9562 17.0343C18.7965 17.208 18.55 17.3503 18.2166 17.4614L13.0083 19.0864C12.8555 19.142 12.6958 19.1698 12.5291 19.1698C12.3625 19.1698 12.2027 19.1489 12.05 19.1073ZM3.36247 17.1489H5.02913V11.3156H3.36247V17.1489Z" fill="#1E62E0"/>
<path d="M14.2252 10.6441C14.0917 10.6441 13.9798 10.599 13.8894 10.5087C13.7991 10.4184 13.754 10.3065 13.754 10.173V9.63111C13.4006 9.55258 13.0904 9.41516 12.8234 9.21884C12.5564 9.02252 12.3405 8.74767 12.1756 8.39429C12.1206 8.28435 12.1186 8.16852 12.1697 8.04681C12.2207 7.92509 12.313 7.83674 12.4465 7.78177C12.5564 7.73466 12.6703 7.73662 12.7881 7.78766C12.9059 7.83871 12.9962 7.92312 13.059 8.04092C13.1925 8.2765 13.3613 8.45515 13.5655 8.57687C13.7697 8.69859 14.021 8.75945 14.3194 8.75945C14.6414 8.75945 14.9142 8.68681 15.138 8.54153C15.3618 8.39626 15.4737 8.17049 15.4737 7.86423C15.4737 7.58938 15.3874 7.37146 15.2146 7.21048C15.0418 7.0495 14.6414 6.86692 14.0131 6.66275C13.3378 6.45072 12.8745 6.19747 12.6232 5.90299C12.3719 5.60851 12.2462 5.24924 12.2462 4.82519C12.2462 4.31476 12.4112 3.91819 12.741 3.63549C13.0708 3.35279 13.4085 3.1918 13.754 3.15254V2.63425C13.754 2.50076 13.7991 2.38885 13.8894 2.29855C13.9798 2.20824 14.0917 2.16309 14.2252 2.16309C14.3586 2.16309 14.4706 2.20824 14.5609 2.29855C14.6512 2.38885 14.6963 2.50076 14.6963 2.63425V3.15254C14.9947 3.19966 15.2539 3.29585 15.4737 3.44113C15.6936 3.58641 15.8742 3.76506 16.0156 3.97709C16.0863 4.07917 16.1 4.19304 16.0568 4.31868C16.0136 4.44433 15.9253 4.53464 15.7918 4.58961C15.6818 4.63672 15.568 4.63869 15.4502 4.59549C15.3324 4.5523 15.2225 4.47574 15.1204 4.3658C15.0183 4.25586 14.8985 4.17144 14.7611 4.11255C14.6237 4.05365 14.4529 4.0242 14.2487 4.0242C13.9032 4.0242 13.6401 4.10077 13.4595 4.2539C13.2789 4.40703 13.1886 4.59746 13.1886 4.82519C13.1886 5.08433 13.3064 5.28851 13.542 5.43771C13.7775 5.58691 14.1859 5.74397 14.767 5.90888C15.3088 6.06593 15.7191 6.31526 15.9979 6.65686C16.2767 6.99846 16.4161 7.39306 16.4161 7.84067C16.4161 8.39822 16.2512 8.82227 15.9214 9.11283C15.5915 9.40338 15.1832 9.58399 14.6963 9.65467V10.173C14.6963 10.3065 14.6512 10.4184 14.5609 10.5087C14.4706 10.599 14.3586 10.6441 14.2252 10.6441Z" fill="#1E62E0" stroke="#1E62E0" strokeWidth="0.5"/>
</g>
</svg>
  )
}

export default CommissionIcon;
