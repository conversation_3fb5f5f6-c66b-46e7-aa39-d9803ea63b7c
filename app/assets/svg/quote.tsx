import React from 'react';
import { xIconProps } from '@/types';

const Quote: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4528_11528"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4528_11528)">
                <path
                    d="M4.9125 12.375L6 10.5C5.175 10.5 4.46875 10.2063 3.88125 9.61875C3.29375 9.03125 3 8.325 3 7.5C3 6.675 3.29375 5.96875 3.88125 5.38125C4.46875 4.79375 5.175 4.5 6 4.5C6.825 4.5 7.53125 4.79375 8.11875 5.38125C8.70625 5.96875 9 6.675 9 7.5C9 7.7875 8.96563 8.05312 8.89688 8.29688C8.82813 8.54063 8.725 8.775 8.5875 9L6.20625 13.125C6.14375 13.2375 6.05625 13.3281 5.94375 13.3969C5.83125 13.4656 5.70625 13.5 5.56875 13.5C5.28125 13.5 5.06562 13.375 4.92188 13.125C4.77813 12.875 4.775 12.625 4.9125 12.375ZM11.6625 12.375L12.75 10.5C11.925 10.5 11.2188 10.2063 10.6313 9.61875C10.0438 9.03125 9.75 8.325 9.75 7.5C9.75 6.675 10.0438 5.96875 10.6313 5.38125C11.2188 4.79375 11.925 4.5 12.75 4.5C13.575 4.5 14.2812 4.79375 14.8687 5.38125C15.4562 5.96875 15.75 6.675 15.75 7.5C15.75 7.7875 15.7156 8.05312 15.6469 8.29688C15.5781 8.54063 15.475 8.775 15.3375 9L12.9563 13.125C12.8938 13.2375 12.8063 13.3281 12.6938 13.3969C12.5813 13.4656 12.4563 13.5 12.3188 13.5C12.0313 13.5 11.8156 13.375 11.6719 13.125C11.5281 12.875 11.525 12.625 11.6625 12.375ZM6 8.625C6.3125 8.625 6.57812 8.51563 6.79688 8.29688C7.01563 8.07812 7.125 7.8125 7.125 7.5C7.125 7.1875 7.01563 6.92188 6.79688 6.70312C6.57812 6.48437 6.3125 6.375 6 6.375C5.6875 6.375 5.42188 6.48437 5.20312 6.70312C4.98437 6.92188 4.875 7.1875 4.875 7.5C4.875 7.8125 4.98437 8.07812 5.20312 8.29688C5.42188 8.51563 5.6875 8.625 6 8.625ZM12.75 8.625C13.0625 8.625 13.3281 8.51563 13.5469 8.29688C13.7656 8.07812 13.875 7.8125 13.875 7.5C13.875 7.1875 13.7656 6.92188 13.5469 6.70312C13.3281 6.48437 13.0625 6.375 12.75 6.375C12.4375 6.375 12.1719 6.48437 11.9531 6.70312C11.7344 6.92188 11.625 7.1875 11.625 7.5C11.625 7.8125 11.7344 8.07812 11.9531 8.29688C12.1719 8.51563 12.4375 8.625 12.75 8.625Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Quote;
