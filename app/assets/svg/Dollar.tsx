import React from 'react'

const Dollar = () => {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_9812_16204" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
        <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_9812_16204)">
        <path d="M10.0209 17.5C9.78482 17.5 9.5869 17.4201 9.42718 17.2604C9.26746 17.1007 9.1876 16.9028 9.1876 16.6667V15.7083C8.5626 15.5694 8.01399 15.3264 7.54176 14.9792C7.06954 14.6319 6.6876 14.1458 6.39593 13.5208C6.29871 13.3264 6.29524 13.1215 6.38551 12.9062C6.47579 12.691 6.63899 12.5347 6.8751 12.4375C7.06954 12.3542 7.27093 12.3576 7.47926 12.4479C7.6876 12.5382 7.84732 12.6875 7.95843 12.8958C8.19454 13.3125 8.49315 13.6285 8.85426 13.8438C9.21537 14.059 9.65982 14.1667 10.1876 14.1667C10.757 14.1667 11.2397 14.0382 11.6355 13.7812C12.0313 13.5243 12.2293 13.125 12.2293 12.5833C12.2293 12.0972 12.0765 11.7118 11.7709 11.4271C11.4654 11.1424 10.757 10.8194 9.64593 10.4583C8.45149 10.0833 7.63204 9.63542 7.1876 9.11458C6.74315 8.59375 6.52093 7.95833 6.52093 7.20833C6.52093 6.30556 6.8126 5.60417 7.39593 5.10417C7.97926 4.60417 8.57649 4.31944 9.1876 4.25V3.33333C9.1876 3.09722 9.26746 2.89931 9.42718 2.73958C9.5869 2.57986 9.78482 2.5 10.0209 2.5C10.257 2.5 10.455 2.57986 10.6147 2.73958C10.7744 2.89931 10.8543 3.09722 10.8543 3.33333V4.25C11.382 4.33333 11.8404 4.50347 12.2293 4.76042C12.6182 5.01736 12.9376 5.33333 13.1876 5.70833C13.3126 5.88889 13.3369 6.09028 13.2605 6.3125C13.1841 6.53472 13.0279 6.69444 12.7918 6.79167C12.5973 6.875 12.3959 6.87847 12.1876 6.80208C11.9793 6.72569 11.7848 6.59028 11.6043 6.39583C11.4237 6.20139 11.2119 6.05208 10.9688 5.94792C10.7258 5.84375 10.4237 5.79167 10.0626 5.79167C9.45149 5.79167 8.98621 5.92708 8.66676 6.19792C8.34732 6.46875 8.1876 6.80556 8.1876 7.20833C8.1876 7.66667 8.39593 8.02778 8.8126 8.29167C9.22926 8.55556 9.95149 8.83333 10.9793 9.125C11.9376 9.40278 12.6633 9.84375 13.1563 10.4479C13.6494 11.0521 13.8959 11.75 13.8959 12.5417C13.8959 13.5278 13.6043 14.2778 13.0209 14.7917C12.4376 15.3056 11.7154 15.625 10.8543 15.75V16.6667C10.8543 16.9028 10.7744 17.1007 10.6147 17.2604C10.455 17.4201 10.257 17.5 10.0209 17.5Z" fill="#57585E"/>
        </g>
        </svg>
    )
}

export default Dollar
