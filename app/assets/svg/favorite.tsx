
// interface FavoriteProps {
//     isFavorited: boolean;
// }

// const favorite: React.FC<FavoriteProps> = ({ isFavorited }) => {
//     return (
//         // <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
//         //     <path
//         //         d="M8.88007 15.9406C8.67806 15.9406 8.47245 15.9046 8.26323 15.8324C8.05401 15.7603 7.87004 15.6448 7.71132 15.4861L6.21792 14.1226C4.68844 12.723 3.30686 11.3342 2.07318 9.95621C0.839497 8.57824 0.222656 7.05958 0.222656 5.40025C0.222656 4.04392 0.67717 2.91124 1.5862 2.00221C2.49523 1.09319 3.62791 0.638672 4.98423 0.638672C5.74897 0.638672 6.47042 0.800998 7.14859 1.12565C7.82675 1.4503 8.40391 1.894 8.88007 2.45673C9.35623 1.894 9.93339 1.4503 10.6116 1.12565C11.2897 0.800998 12.0112 0.638672 12.7759 0.638672C14.1322 0.638672 15.2649 1.09319 16.1739 2.00221C17.083 2.91124 17.5375 4.04392 17.5375 5.40025C17.5375 7.05958 16.9242 8.58184 15.6978 9.96703C14.4713 11.3522 13.0789 12.7446 11.5206 14.1442L10.0488 15.4861C9.8901 15.6448 9.70613 15.7603 9.49691 15.8324C9.28769 15.9046 9.08208 15.9406 8.88007 15.9406Z"
//         //         fill={isFavorited ? "#1E62E0" : "none"}
//         //     />
//         //     <path
//         //         d="M9.00018 15.6511C8.79817 15.6511 8.59256 15.615 8.38334 15.5429C8.17412 15.4707 7.99015 15.3553 7.83143 15.1966L6.33803 13.833C4.80855 12.4334 3.42698 11.0446 2.19329 9.66666C0.959614 8.28868 0.342773 6.77003 0.342773 5.11069C0.342773 3.75437 0.797287 2.62169 1.70632 1.71266C2.61534 0.803635 3.74802 0.349121 5.10435 0.349121C5.86909 0.349121 6.59054 0.511447 7.2687 0.8361C7.94686 1.16075 8.52402 1.60445 9.00018 2.16718C9.47634 1.60445 10.0535 1.16075 10.7317 0.8361C11.4098 0.511447 12.1313 0.349121 12.896 0.349121C14.2523 0.349121 15.385 0.803635 16.294 1.71266C17.2031 2.62169 17.6576 3.75437 17.6576 5.11069C17.6576 6.77003 17.0444 8.29229 15.8179 9.67748C14.5914 11.0627 13.199 12.4551 11.6407 13.8547L10.1689 15.1966C10.0102 15.3553 9.82624 15.4707 9.61702 15.5429C9.4078 15.615 9.20219 15.6511 9.00018 15.6511Z"
//         //         fill={isFavorited ? "#1E62E0" : "none"}
//         //         stroke="#1E62E0"
//         //         strokeWidth="1.5"
//         //     />
//         // </svg>
//         <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
// <path d="M8.99921 15.6506C8.7972 15.6506 8.59159 15.6145 8.38237 15.5424C8.17315 15.4702 7.98918 15.3548 7.83046 15.1961L6.33706 13.8325C4.80758 12.4329 3.426 11.0441 2.19232 9.66617C0.958638 8.2882 0.341797 6.76954 0.341797 5.11021C0.341797 3.75388 0.796311 2.6212 1.70534 1.71217C2.61437 0.803147 3.74705 0.348633 5.10337 0.348633C5.86811 0.348633 6.58956 0.510959 7.26773 0.835612C7.94589 1.16026 8.52305 1.60396 8.99921 2.16669C9.47537 1.60396 10.0525 1.16026 10.7307 0.835612C11.4089 0.510959 12.1303 0.348633 12.895 0.348633C14.2514 0.348633 15.3841 0.803147 16.2931 1.71217C17.2021 2.6212 17.6566 3.75388 17.6566 5.11021C17.6566 6.76954 17.0434 8.2918 15.8169 9.67699C14.5905 11.0622 13.1981 12.4546 11.6397 13.8542L10.168 15.1961C10.0092 15.3548 9.82527 15.4702 9.61605 15.5424C9.40683 15.6145 9.20122 15.6506 8.99921 15.6506ZM8.17676 3.89817C7.75831 3.30658 7.31102 2.85567 6.83486 2.54545C6.3587 2.23523 5.78154 2.08011 5.10337 2.08011C4.23763 2.08011 3.51618 2.36869 2.93902 2.94586C2.36186 3.52302 2.07328 4.24447 2.07328 5.11021C2.07328 5.86052 2.34022 6.65772 2.87409 7.50182C3.40796 8.34591 4.04645 9.16476 4.78954 9.95836C5.53264 10.752 6.29738 11.495 7.08376 12.1876C7.87014 12.8802 8.50862 13.4502 8.99921 13.8975C9.4898 13.4502 10.1283 12.8802 10.9147 12.1876C11.701 11.495 12.4658 10.752 13.2089 9.95836C13.952 9.16476 14.5905 8.34591 15.1243 7.50182C15.6582 6.65772 15.9251 5.86052 15.9251 5.11021C15.9251 4.24447 15.6366 3.52302 15.0594 2.94586C14.4822 2.36869 13.7608 2.08011 12.895 2.08011C12.2169 2.08011 11.6397 2.23523 11.1636 2.54545C10.6874 2.85567 10.2401 3.30658 9.82166 3.89817C9.72066 4.04246 9.59802 4.15068 9.45372 4.22282C9.30943 4.29497 9.15793 4.33104 8.99921 4.33104C8.84049 4.33104 8.68899 4.29497 8.5447 4.22282C8.40041 4.15068 8.27776 4.04246 8.17676 3.89817Z" fill="#1952BB"/>
// </svg>

//     )
// }

// export default favorite;

import React from 'react';

interface FavoriteProps {
    isFavorited: boolean;
}

const Favorite: React.FC<FavoriteProps> = ({ isFavorited }) => {
    return (
        <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="34" height="34" rx="17" stroke="#1952BB" strokeWidth="1.5"/>
        <path d="M17.9992 25.6506C17.7972 25.6506 17.5916 25.6145 17.3824 25.5424C17.1731 25.4702 16.9892 25.3548 16.8305 25.1961L15.3371 23.8325C13.8076 22.4329 12.426 21.0441 11.1923 19.6662C9.95864 18.2882 9.3418 16.7695 9.3418 15.1102C9.3418 13.7539 9.79631 12.6212 10.7053 11.7122C11.6144 10.8031 12.747 10.3486 14.1034 10.3486C14.8681 10.3486 15.5896 10.511 16.2677 10.8356C16.9459 11.1603 17.523 11.604 17.9992 12.1667C18.4754 11.604 19.0525 11.1603 19.7307 10.8356C20.4088 10.511 21.1303 10.3486 21.895 10.3486C23.2514 10.3486 24.384 10.8031 25.2931 11.7122C26.2021 12.6212 26.6566 13.7539 26.6566 15.1102C26.6566 16.7695 26.0434 18.2918 24.8169 19.677C23.5904 21.0622 22.198 22.4546 20.6397 23.8542L19.168 25.1961C19.0092 25.3548 18.8253 25.4702 18.616 25.5424C18.4068 25.6145 18.2012 25.6506 17.9992 25.6506Z" 
        fill={isFavorited ? "#1952BB" : "none"}
        stroke="#1952BB"
        strokeWidth="1.5"
        />
        <mask id="mask0_6560_13261" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="8" y="8" width="20" height="20">
        <rect x="8" y="8" width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_6560_13261)">
        </g>
        </svg>
    );
};

export default Favorite;



