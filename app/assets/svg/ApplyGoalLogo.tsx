import React from 'react'

const ApplyGoalLogo = () => {
    return (
        <svg width="208" height="49" viewBox="0 0 208 49" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3835_23749)">
            <path d="M33.0744 33.3709H29.7106C28.8494 33.3709 28.2606 32.9637 27.9441 32.1478L27.0939 30.0746H16.0149C15.5609 31.1848 15.2898 31.8534 15.199 32.0791C14.8358 32.9403 14.225 33.3709 13.3638 33.3709H10L20.1944 9.03906H22.9474L33.0744 33.3709ZM17.7813 25.7588H25.2917C25.2683 25.6914 24.7138 24.3541 23.627 21.7484C22.5401 19.1441 21.8481 17.467 21.5537 16.7186C21.3955 17.0818 21.0213 17.9761 20.4324 19.4027C19.8436 20.8294 19.3057 22.1336 18.8187 23.3113C18.3317 24.4889 17.9863 25.3048 17.7827 25.7574L17.7813 25.7588Z" fill="#1E62E0"/>
            <path d="M50.0322 21.3743C50.4395 22.4171 50.6445 23.5273 50.6445 24.705C50.6445 25.8827 50.4408 26.9819 50.0322 28.0013C49.625 29.0442 49.0582 29.9494 48.3332 30.7199C47.6081 31.5137 46.7579 32.1355 45.7839 32.5895C44.8098 33.0655 43.756 33.3035 42.6237 33.3035C41.8299 33.3035 41.1063 33.1907 40.4486 32.9637C39.8364 32.7821 39.293 32.5331 38.817 32.2167V40.1342H34.4668V16.3445H38.817V17.0241C39.1788 16.8205 39.5984 16.6389 40.0744 16.4807C40.7995 16.2316 41.6483 16.1064 42.6237 16.1064C43.756 16.1064 44.8098 16.3445 45.7839 16.8205C46.7579 17.2511 47.6068 17.8633 48.3332 18.6557C49.0582 19.4262 49.625 20.3328 50.0322 21.3743ZM45.9889 26.4041C46.1925 25.8606 46.2943 25.2938 46.2943 24.705C46.2943 24.1162 46.1925 23.5218 45.9889 22.9894C45.7852 22.457 45.5018 21.9989 45.1386 21.6136C44.7988 21.2284 44.3916 20.923 43.9156 20.696C43.485 20.4924 43.0199 20.3906 42.5219 20.3906C42.0239 20.3906 41.5589 20.4924 41.1283 20.696C40.6522 20.923 40.2326 21.2284 39.8708 21.6136C39.5076 21.9989 39.2256 22.4625 39.0206 23.0073C38.817 23.5287 38.7152 24.09 38.7152 24.6899C38.7152 25.2897 38.817 25.8565 39.0206 26.3889C39.2242 26.9214 39.5076 27.3809 39.8708 27.7647C40.2326 28.1719 40.6412 28.4787 41.0939 28.6823C41.5465 28.886 42.0225 28.9878 42.5205 28.9878C43.0186 28.9878 43.4836 28.886 43.9142 28.6823C44.3902 28.4567 44.7974 28.1609 45.1373 27.7991C45.4991 27.3919 45.7825 26.9269 45.9875 26.4054L45.9889 26.4041Z" fill="#1E62E0"/>
            <path d="M68.4854 21.3743C68.8926 22.4171 69.0976 23.5273 69.0976 24.705C69.0976 25.8827 68.894 26.9819 68.4854 28.0013C68.0781 29.0442 67.5113 29.9494 66.7863 30.7199C66.0613 31.5137 65.211 32.1355 64.237 32.5895C63.2629 33.0655 62.2091 33.3035 61.0769 33.3035C60.283 33.3035 59.5594 33.1907 58.9018 32.9637C58.2895 32.7821 57.7461 32.5331 57.2701 32.2167V40.1342H52.9199V16.3445H57.2701V17.0241C57.6319 16.8205 58.0515 16.6389 58.5276 16.4807C59.2526 16.2316 60.1014 16.1064 61.0769 16.1064C62.2091 16.1064 63.2629 16.3445 64.237 16.8205C65.211 17.2511 66.0599 17.8633 66.7863 18.6557C67.5113 19.4262 68.0781 20.3328 68.4854 21.3743ZM64.442 26.4041C64.6456 25.8606 64.7474 25.2938 64.7474 24.705C64.7474 24.1162 64.6456 23.5218 64.442 22.9894C64.2384 22.457 63.955 21.9989 63.5918 21.6136C63.2519 21.2284 62.8447 20.923 62.3687 20.696C61.9381 20.4924 61.4731 20.3906 60.975 20.3906C60.477 20.3906 60.012 20.4924 59.5814 20.696C59.1054 20.923 58.6858 21.2284 58.3239 21.6136C57.9607 21.9989 57.6787 22.4625 57.4737 23.0073C57.2701 23.5287 57.1683 24.09 57.1683 24.6899C57.1683 25.2897 57.2701 25.8565 57.4737 26.3889C57.6773 26.9214 57.9607 27.3809 58.3239 27.7647C58.6858 28.1719 59.0944 28.4787 59.547 28.6823C59.9996 28.886 60.4756 28.9878 60.9737 28.9878C61.4717 28.9878 61.9367 28.886 62.3673 28.6823C62.8433 28.4567 63.2506 28.1609 63.5904 27.7991C63.9522 27.3919 64.2356 26.9269 64.4406 26.4054L64.442 26.4041Z" fill="#1E62E0"/>
            <path d="M75.6888 33.2689H71.373V9.48047H75.6888V33.2689Z" fill="#1E62E0"/>
            <path d="M90.9475 16.3445H94.3112L90.8787 24.5001C90.4247 25.6103 89.8138 27.0824 89.0434 28.9177C86.9137 34.1277 85.5999 37.3113 85.1018 38.4669C84.5584 39.6446 83.572 40.2334 82.1453 40.2334H79.801L83.539 31.024L77.3535 16.3432H80.7173C81.5785 16.3432 82.1783 16.7504 82.5181 17.5662L85.8145 25.5182L88.1588 19.9105C88.7022 18.5747 89.02 17.8042 89.1108 17.5992C89.4727 16.7614 90.0849 16.3418 90.9461 16.3418L90.9475 16.3445Z" fill="#1E62E0"/>
            <path d="M111.303 32.9966C110.102 33.3818 108.823 33.5745 107.463 33.5745C105.764 33.5745 104.155 33.2456 102.637 32.5894C101.142 31.9552 99.8278 31.0719 98.6955 29.9383C97.5853 28.8281 96.713 27.5252 96.0788 26.0297C95.4212 24.5123 95.0938 22.8916 95.0938 21.1705C95.0938 20.0383 95.241 18.9445 95.5354 17.8907C95.8298 16.8369 96.2425 15.8518 96.7749 14.9342C97.3074 14.0165 97.9526 13.1787 98.712 12.4193C99.4714 11.6598 100.304 11.0091 101.21 10.4657C102.139 9.92225 103.135 9.51364 104.201 9.24262C105.243 8.92619 106.342 8.7666 107.498 8.7666C108.948 8.7666 110.318 9.01562 111.61 9.51364C112.878 9.96765 114.056 10.6005 115.144 11.4163L115.653 11.8236L115.381 12.4014C115.177 12.8774 114.747 13.3245 114.089 13.7441C113.431 14.1637 112.73 14.3729 111.981 14.3729C111.505 14.3729 111.053 14.26 110.622 14.033C109.466 13.4442 108.458 13.1498 107.598 13.1498C106.828 13.1498 106.103 13.2406 105.423 13.4222C104.72 13.6258 104.064 13.9037 103.452 14.2545C102.839 14.6054 102.285 15.0305 101.787 15.5285C101.289 16.0265 100.869 16.5823 100.529 17.1932C100.166 17.782 99.8952 18.4162 99.7136 19.0959C99.532 19.7755 99.4412 20.4785 99.4412 21.2022C99.4412 21.9258 99.5375 22.6632 99.7301 23.3429C99.9227 24.0225 100.194 24.6636 100.546 25.2634C100.897 25.8646 101.322 26.4026 101.82 26.8772C102.296 27.3752 102.834 27.8004 103.434 28.1512C104.033 28.502 104.675 28.7689 105.354 28.9491C106.01 29.1307 106.713 29.2215 107.461 29.2215C107.619 29.2215 107.846 29.2105 108.14 29.1871L108.48 29.1527C108.662 29.1307 108.876 29.0853 109.125 29.0165C109.238 28.9945 109.363 28.9601 109.499 28.9147C109.772 28.8473 109.975 28.7909 110.112 28.7455L110.724 28.4731C110.905 28.4057 111.029 28.3493 111.098 28.3039C111.324 28.1911 111.518 28.0893 111.676 27.9985V25.6872H106.986V24.4297C106.986 23.5919 107.291 22.8724 107.904 22.2711C108.516 21.6713 109.239 21.37 110.079 21.37H114.089C114.632 21.37 115.08 21.5351 115.432 21.8625C115.783 22.1913 115.97 22.6165 115.993 23.1365V29.3894C115.948 30.1598 115.63 30.7032 115.041 31.021C114.044 31.8148 112.9 32.4367 111.608 32.8907L111.303 32.9925V32.9966Z" fill="#1E62E0"/>
            <path d="M149.261 16.344C150.19 16.344 150.995 16.6783 151.674 17.3469C152.354 18.0155 152.694 18.8259 152.694 19.7765V33.2673H151.878C150.382 33.2673 149.363 32.9165 148.82 32.2134C148.705 32.3042 148.604 32.3717 148.514 32.4171C147.971 32.7803 147.358 33.0513 146.679 33.2329C145.999 33.4145 145.296 33.5053 144.573 33.5053C143.417 33.5053 142.33 33.2797 141.311 32.8257C140.337 32.373 139.475 31.7498 138.728 30.956C138.003 30.1856 137.436 29.2803 137.029 28.2375C136.622 27.196 136.417 26.0913 136.417 24.9246C136.417 23.758 136.621 22.6532 137.029 21.6118C137.436 20.5703 138.003 19.6637 138.728 18.8933C139.453 18.1008 140.313 17.4776 141.311 17.0236C142.33 16.5476 143.418 16.3096 144.573 16.3096C145.32 16.3096 146.034 16.4114 146.713 16.615C147.347 16.7966 147.925 17.0456 148.447 17.3634V16.344H149.263H149.261ZM148.071 26.6746C148.275 26.1532 148.377 25.5754 148.377 24.9411C148.377 24.3069 148.275 23.758 148.071 23.2256C147.868 22.6931 147.595 22.2226 147.255 21.8154C146.937 21.4536 146.541 21.1592 146.065 20.9322C145.611 20.7065 145.113 20.5923 144.57 20.5923C144.026 20.5923 143.494 20.7065 143.04 20.9322C142.586 21.1592 142.184 21.4591 141.833 21.8333C141.483 22.2075 141.205 22.6601 141.001 23.1925C140.797 23.725 140.696 24.3083 140.696 24.9425C140.696 25.5767 140.797 26.1202 141.001 26.6416C141.205 27.163 141.483 27.6156 141.833 28.0008C142.184 28.3861 142.586 28.6805 143.04 28.8841C143.493 29.1111 144.003 29.2239 144.57 29.2239C145.137 29.2239 145.611 29.1221 146.065 28.9185C146.541 28.6929 146.937 28.3971 147.255 28.0352C147.595 27.65 147.868 27.1974 148.071 26.676V26.6746Z" fill="#1E62E0"/>
            <path d="M160 33.2689H155.685V9.48047H160V33.2689Z" fill="#1E62E0"/>
            <path d="M129.052 25.6899C128.305 25.3721 127.582 25.064 126.745 24.7076C126.952 25.606 127.134 26.3957 127.316 27.1813C126.402 27.561 125.23 27.107 124.596 26.1481C123.937 25.1506 124.098 23.779 124.974 22.9398C125.864 22.0868 127.208 22.0139 128.241 22.721C129 23.2397 129.624 24.6306 129.052 25.6899Z" fill="#19191C"/>
            <path d="M134.608 21.4469C134.176 20.4288 133.565 19.5222 132.772 18.727C132.002 17.9593 131.095 17.3471 130.053 16.8945C129.012 16.4418 127.895 16.2148 126.707 16.2148C125.518 16.2148 124.401 16.4418 123.358 16.8945C122.318 17.3471 121.411 17.9593 120.641 18.727C119.87 19.5222 119.26 20.4385 118.806 21.4813C118.353 22.5241 118.126 23.6385 118.126 24.8272C118.126 26.0158 118.353 27.1329 118.806 28.1758C119.258 29.1938 119.87 30.1005 120.641 30.8929C121.411 31.6633 122.318 32.2742 123.358 32.7282C124.401 33.1822 125.51 33.4078 126.69 33.4078C127.495 33.4078 128.274 33.3033 129.028 33.0886C128.693 31.7046 128.364 30.3344 128.033 28.9558C127.616 29.092 127.18 29.1608 126.725 29.1608C126.134 29.1608 125.579 29.0494 125.06 28.8224C124.516 28.5954 124.051 28.2831 123.666 27.8869C123.281 27.492 122.965 27.0215 122.716 26.4781C122.486 25.9787 122.374 25.4297 122.374 24.8285C122.374 24.2273 122.486 23.6688 122.716 23.1473C122.94 22.6039 123.259 22.1403 123.666 21.7537C124.051 21.3465 124.516 21.03 125.06 20.803C125.579 20.576 126.134 20.4646 126.725 20.4646C127.315 20.4646 127.868 20.576 128.389 20.803C128.933 21.03 129.398 21.3423 129.783 21.7358C130.168 22.1334 130.485 22.6039 130.734 23.1473C130.961 23.6688 131.075 24.2246 131.075 24.812C131.075 25.3995 130.961 25.958 130.734 26.4794C130.699 26.5579 130.663 26.6349 130.623 26.7106C131.922 27.2705 133.211 27.8263 134.513 28.3904C134.547 28.3202 134.579 28.2487 134.608 28.1785C135.062 27.1137 135.287 25.9924 135.287 24.8134C135.287 23.6344 135.06 22.5145 134.608 21.4496V21.4469Z" fill="#1E62E0"/>
            <path d="M128.166 26.3643C130.988 27.5832 133.657 28.7347 136.499 29.9633C135.305 30.431 134.291 30.8685 133.247 31.2207C132.683 31.4106 132.333 31.7449 132.051 32.2429C131.516 33.1867 130.941 34.1085 130.287 35.1994C129.565 32.1906 128.883 29.3524 128.166 26.3643Z" fill="#19191C"/>
            </g>
            <defs>
            <clipPath id="clip0_3835_23749">
            <rect width="150" height="31.4666" fill="white" transform="translate(10 8.7666)"/>
            </clipPath>
            </defs>
        </svg>
    )
}

export default ApplyGoalLogo