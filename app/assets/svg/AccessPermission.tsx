import React from 'react'

const AccessPermission = () => {
    return (
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_10772_5779" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
            <rect x="0.199219" width="24" height="24" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_10772_5779)">
            <path d="M12.1992 2.14844C12.3326 2.14844 12.4534 2.15677 12.5617 2.17344C12.6701 2.1901 12.7826 2.22344 12.8992 2.27344L18.8992 4.52344C19.2826 4.67344 19.5951 4.9151 19.8367 5.24844C20.0784 5.58177 20.1992 5.95677 20.1992 6.37344V9.49844C20.1992 9.78177 20.1034 10.0193 19.9117 10.2109C19.7201 10.4026 19.4826 10.4984 19.1992 10.4984C18.9159 10.4984 18.6784 10.4026 18.4867 10.2109C18.2951 10.0193 18.1992 9.78177 18.1992 9.49844V6.39844L12.1992 4.14844L6.19922 6.39844V11.0984C6.19922 11.9318 6.32005 12.7651 6.56172 13.5984C6.80339 14.4318 7.13672 15.2234 7.56172 15.9734C7.98672 16.7234 8.50339 17.3984 9.11172 17.9984C9.72005 18.5984 10.3826 19.0901 11.0992 19.4734C11.3492 19.6068 11.5284 19.7984 11.6367 20.0484C11.7451 20.2984 11.7492 20.5484 11.6492 20.7984C11.5326 21.0651 11.3451 21.2484 11.0867 21.3484C10.8284 21.4484 10.5742 21.4318 10.3242 21.2984C8.44089 20.3651 6.94922 18.9568 5.84922 17.0734C4.74922 15.1901 4.19922 13.1984 4.19922 11.0984V6.37344C4.19922 5.95677 4.32005 5.58177 4.56172 5.24844C4.80339 4.9151 5.11589 4.67344 5.49922 4.52344L11.4992 2.27344C11.6159 2.22344 11.7326 2.1901 11.8492 2.17344C11.9659 2.15677 12.0826 2.14844 12.1992 2.14844ZM17.1992 21.9984C15.8159 21.9984 14.6367 21.5109 13.6617 20.5359C12.6867 19.5609 12.1992 18.3818 12.1992 16.9984C12.1992 15.6151 12.6867 14.4359 13.6617 13.4609C14.6367 12.4859 15.8159 11.9984 17.1992 11.9984C18.5826 11.9984 19.7617 12.4859 20.7367 13.4609C21.7117 14.4359 22.1992 15.6151 22.1992 16.9984C22.1992 18.3818 21.7117 19.5609 20.7367 20.5359C19.7617 21.5109 18.5826 21.9984 17.1992 21.9984ZM17.1992 16.9984C17.6159 16.9984 17.9701 16.8526 18.2617 16.5609C18.5534 16.2693 18.6992 15.9151 18.6992 15.4984C18.6992 15.0818 18.5534 14.7276 18.2617 14.4359C17.9701 14.1443 17.6159 13.9984 17.1992 13.9984C16.7826 13.9984 16.4284 14.1443 16.1367 14.4359C15.8451 14.7276 15.6992 15.0818 15.6992 15.4984C15.6992 15.9151 15.8451 16.2693 16.1367 16.5609C16.4284 16.8526 16.7826 16.9984 17.1992 16.9984ZM17.1992 19.9984C17.6159 19.9984 18.0076 19.9193 18.3742 19.7609C18.7409 19.6026 19.0659 19.3734 19.3492 19.0734C19.4326 18.9734 19.4659 18.8609 19.4492 18.7359C19.4326 18.6109 19.3576 18.5151 19.2242 18.4484C18.9076 18.2984 18.5784 18.1859 18.2367 18.1109C17.8951 18.0359 17.5492 17.9984 17.1992 17.9984C16.8492 17.9984 16.5034 18.0359 16.1617 18.1109C15.8201 18.1859 15.4909 18.2984 15.1742 18.4484C15.0409 18.5151 14.9659 18.6109 14.9492 18.7359C14.9326 18.8609 14.9659 18.9734 15.0492 19.0734C15.3326 19.3734 15.6576 19.6026 16.0242 19.7609C16.3909 19.9193 16.7826 19.9984 17.1992 19.9984Z" fill='currentColor' />
            </g>
        </svg>

    )
}

export default AccessPermission