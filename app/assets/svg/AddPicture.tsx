import React from 'react'

const AddPicture = () => {
    return (
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_9197_27856" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="30" height="30">
<rect width="30" height="30" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_9197_27856)">
<path d="M3.75195 26.2493C3.06445 26.2493 2.47591 26.0045 1.98633 25.5149C1.49674 25.0253 1.25195 24.4368 1.25195 23.7493V8.74927C1.25195 8.06177 1.49674 7.47323 1.98633 6.98364C2.47591 6.49406 3.06445 6.24927 3.75195 6.24927H7.68945L9.25195 4.56177C9.48112 4.31177 9.75716 4.11385 10.0801 3.96802C10.403 3.82218 10.7415 3.74927 11.0957 3.74927H16.252C16.6061 3.74927 16.903 3.86906 17.1426 4.10864C17.3822 4.34823 17.502 4.6451 17.502 4.99927C17.502 5.35343 17.3822 5.65031 17.1426 5.88989C16.903 6.12948 16.6061 6.24927 16.252 6.24927H11.0957L8.81445 8.74927H3.75195V23.7493H23.752V13.7493C23.752 13.3951 23.8717 13.0982 24.1113 12.8586C24.3509 12.6191 24.6478 12.4993 25.002 12.4993C25.3561 12.4993 25.653 12.6191 25.8926 12.8586C26.1322 13.0982 26.252 13.3951 26.252 13.7493V23.7493C26.252 24.4368 26.0072 25.0253 25.5176 25.5149C25.028 26.0045 24.4395 26.2493 23.752 26.2493H3.75195ZM23.752 6.24927H22.502C22.1478 6.24927 21.8509 6.12948 21.6113 5.88989C21.3717 5.65031 21.252 5.35343 21.252 4.99927C21.252 4.6451 21.3717 4.34823 21.6113 4.10864C21.8509 3.86906 22.1478 3.74927 22.502 3.74927H23.752V2.49927C23.752 2.1451 23.8717 1.84823 24.1113 1.60864C24.3509 1.36906 24.6478 1.24927 25.002 1.24927C25.3561 1.24927 25.653 1.36906 25.8926 1.60864C26.1322 1.84823 26.252 2.1451 26.252 2.49927V3.74927H27.502C27.8561 3.74927 28.153 3.86906 28.3926 4.10864C28.6322 4.34823 28.752 4.6451 28.752 4.99927C28.752 5.35343 28.6322 5.65031 28.3926 5.88989C28.153 6.12948 27.8561 6.24927 27.502 6.24927H26.252V7.49927C26.252 7.85344 26.1322 8.15031 25.8926 8.38989C25.653 8.62948 25.3561 8.74927 25.002 8.74927C24.6478 8.74927 24.3509 8.62948 24.1113 8.38989C23.8717 8.15031 23.752 7.85344 23.752 7.49927V6.24927ZM13.752 21.8743C15.3145 21.8743 16.6426 21.3274 17.7363 20.2336C18.8301 19.1399 19.377 17.8118 19.377 16.2493C19.377 14.6868 18.8301 13.3586 17.7363 12.2649C16.6426 11.1711 15.3145 10.6243 13.752 10.6243C12.1895 10.6243 10.8613 11.1711 9.76758 12.2649C8.67383 13.3586 8.12695 14.6868 8.12695 16.2493C8.12695 17.8118 8.67383 19.1399 9.76758 20.2336C10.8613 21.3274 12.1895 21.8743 13.752 21.8743ZM13.752 19.3743C12.877 19.3743 12.1374 19.0722 11.5332 18.468C10.929 17.8639 10.627 17.1243 10.627 16.2493C10.627 15.3743 10.929 14.6347 11.5332 14.0305C12.1374 13.4264 12.877 13.1243 13.752 13.1243C14.627 13.1243 15.3665 13.4264 15.9707 14.0305C16.5749 14.6347 16.877 15.3743 16.877 16.2493C16.877 17.1243 16.5749 17.8639 15.9707 18.468C15.3665 19.0722 14.627 19.3743 13.752 19.3743Z" fill="#1E62E0"/>
</g>
</svg>

    )
}

export default AddPicture
