import React from 'react'

type StudentProps= {
    className:  string;
}
const Student: React.FC<StudentProps> = ({ className }) => {
  return (
        <svg className={className} width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* <rect x="0.5" y="0.5" width="55" height="55" rx="27.5" stroke="currentColor"/> */}
            <path d="M29.2191 23.9264C28.8526 24.0852 28.3834 24.1726 27.8982 24.1726C27.4127 24.1726 26.9438 24.0852 26.5759 23.9264L23.6526 22.6599L21.5283 21.7402C21.444 22.1574 21.3994 22.5889 21.3994 23.0305C21.3998 26.6135 24.3146 30.4167 27.8975 30.4167C31.4814 30.4167 34.3959 26.6135 34.3959 23.0305C34.3959 22.5889 34.351 22.1574 34.2667 21.7402L32.1438 22.6599L29.2191 23.9264Z" fill="currentColor"/>
            <path d="M32.7768 30.4836C31.516 31.4438 29.9429 32.0157 28.2398 32.0157H27.555C25.852 32.0157 24.2795 31.4438 23.0188 30.4836C18.9031 31.1487 15.7598 34.7163 15.7598 39.0193C15.7601 41.383 21.1943 43.2999 27.8974 43.2999C34.6005 43.2999 40.0351 41.383 40.0351 39.0193C40.0347 34.7163 36.8918 31.1487 32.7768 30.4836Z" fill="currentColor"/>
            <path d="M19.0756 17.1098L19.671 17.3671L19.6206 17.7666C19.3928 17.8057 19.2188 18.0043 19.2188 18.2426C19.2188 18.4575 19.3592 18.6408 19.5533 18.7023L19.1926 23.7636C19.1776 23.9761 19.2198 24.312 19.2864 24.5129L19.5802 25.3922C19.6135 25.4931 19.6577 25.5431 19.7016 25.5431C19.7451 25.5431 19.7893 25.4931 19.8229 25.3922L20.1167 24.5129C20.1833 24.312 20.2262 23.9757 20.2105 23.7636L19.8498 18.7027C20.0426 18.6404 20.1844 18.4579 20.1844 18.243C20.1844 18.0046 20.0106 17.8057 19.7815 17.767L19.7356 17.395L21.1095 17.9904L21.7834 18.2817V20.8787L24.0066 21.8412L26.9303 23.1077C27.1962 23.2226 27.5467 23.2804 27.8976 23.2804C28.2481 23.2804 28.5983 23.2229 28.8642 23.1077L31.7882 21.8412L34.0118 20.8783V18.2817L34.685 17.9904L36.7186 17.1101C37.25 16.8792 37.25 16.5029 36.7186 16.2723L28.8646 12.8723C28.5987 12.7574 28.2481 12.7 27.8976 12.7C27.5471 12.7 27.1962 12.7574 26.9303 12.8723L19.0753 16.272C18.5439 16.5025 18.5439 16.8789 19.0756 17.1098Z" fill="currentColor"/>
        </svg>
  )
}

export default Student;