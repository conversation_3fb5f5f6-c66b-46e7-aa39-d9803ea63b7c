import React from 'react'

const ClockHours = () => {
    return (
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.800781" width="24" height="24" rx="12" fill="#F4F7FE" />
            <path d="M12.8012 16.7234C12.1475 16.7234 11.5333 16.5994 10.9584 16.3513C10.3835 16.1033 9.88348 15.7666 9.45823 15.3414C9.03298 14.9161 8.69633 14.4161 8.44827 13.8412C8.2002 13.2663 8.07617 12.6521 8.07617 11.9984C8.07617 11.3448 8.2002 10.7306 8.44827 10.1557C8.69633 9.58081 9.03298 9.08075 9.45823 8.6555C9.88348 8.23025 10.3835 7.89359 10.9584 7.64553C11.5333 7.39747 12.1475 7.27344 12.8012 7.27344C13.4627 7.27344 14.0789 7.39747 14.6498 7.64553C15.2208 7.89359 15.7189 8.23025 16.1441 8.6555C16.5694 9.08075 16.906 9.57884 17.1541 10.1498C17.4021 10.7207 17.5262 11.3369 17.5262 11.9984C17.5262 12.0772 17.5242 12.154 17.5203 12.2288C17.5163 12.3036 17.5104 12.3764 17.5025 12.4473C17.4868 12.5812 17.4297 12.6836 17.3313 12.7544C17.2328 12.8253 17.1167 12.8489 16.9828 12.8253C16.8489 12.8017 16.7426 12.7347 16.6639 12.6245C16.5851 12.5142 16.5536 12.3922 16.5694 12.2583C16.5694 12.2189 16.5713 12.1756 16.5753 12.1284C16.5792 12.0811 16.5812 12.0378 16.5812 11.9984C16.5812 11.4708 16.4847 10.9786 16.2918 10.5219C16.0988 10.0651 15.8291 9.6635 15.4826 9.317C15.1361 8.9705 14.7345 8.70078 14.2777 8.50784C13.821 8.31491 13.3288 8.21844 12.8012 8.21844C11.7459 8.21844 10.8521 8.58463 10.1197 9.317C9.38736 10.0494 9.02117 10.9432 9.02117 11.9984C9.02117 13.0537 9.38736 13.9475 10.1197 14.6799C10.8521 15.4123 11.7459 15.7784 12.8012 15.7784C13.1555 15.7784 13.4961 15.7332 13.823 15.6426C14.1498 15.552 14.4589 15.4162 14.7502 15.2351C14.8605 15.1721 14.9806 15.1484 15.1105 15.1642C15.2405 15.1799 15.3409 15.2429 15.4117 15.3532C15.4905 15.4634 15.5161 15.5835 15.4885 15.7135C15.461 15.8434 15.392 15.9438 15.2818 16.0147C14.9117 16.2431 14.5199 16.4183 14.1065 16.5403C13.693 16.6624 13.2579 16.7234 12.8012 16.7234ZM16.2268 14.8334C16.0614 14.8334 15.9216 14.7763 15.8075 14.6622C15.6933 14.548 15.6362 14.4082 15.6362 14.2428C15.6362 14.0774 15.6933 13.9377 15.8075 13.8235C15.9216 13.7093 16.0614 13.6522 16.2268 13.6522C16.3922 13.6522 16.532 13.7093 16.6461 13.8235C16.7603 13.9377 16.8174 14.0774 16.8174 14.2428C16.8174 14.4082 16.7603 14.548 16.6461 14.6622C16.532 14.7763 16.3922 14.8334 16.2268 14.8334ZM13.2737 11.8094L14.6912 13.2269C14.7778 13.3136 14.8211 13.4238 14.8211 13.5577C14.8211 13.6916 14.7778 13.8018 14.6912 13.8884C14.6045 13.9751 14.4943 14.0184 14.3604 14.0184C14.2265 14.0184 14.1163 13.9751 14.0297 13.8884L12.4704 12.3292C12.4232 12.2819 12.3877 12.2288 12.3641 12.1697C12.3405 12.1107 12.3287 12.0496 12.3287 11.9866V10.1084C12.3287 9.97456 12.374 9.86234 12.4645 9.77178C12.5551 9.68122 12.6673 9.63594 12.8012 9.63594C12.935 9.63594 13.0473 9.68122 13.1378 9.77178C13.2284 9.86234 13.2737 9.97456 13.2737 10.1084V11.8094Z" fill="#36373B" />
        </svg>
    )
}

export default ClockHours