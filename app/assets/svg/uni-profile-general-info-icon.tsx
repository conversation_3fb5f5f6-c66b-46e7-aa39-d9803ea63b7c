import React from 'react';

const UniProfileGeneralInfoIcon = () => {
    return (
        <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_26959"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="16"
                height="16"
            >
                <rect width="16" height="16" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_26959)">
                <path
                    d="M11.3333 13.9997C10.6 13.9997 9.97222 13.7386 9.45 13.2163C8.92778 12.6941 8.66667 12.0663 8.66667 11.333C8.66667 10.5997 8.92778 9.9719 9.45 9.44967C9.97222 8.92745 10.6 8.66634 11.3333 8.66634C12.0667 8.66634 12.6944 8.92745 13.2167 9.44967C13.7389 9.9719 14 10.5997 14 11.333C14 12.0663 13.7389 12.6941 13.2167 13.2163C12.6944 13.7386 12.0667 13.9997 11.3333 13.9997ZM11.3333 12.6663C11.7 12.6663 12.0139 12.5358 12.275 12.2747C12.5361 12.0136 12.6667 11.6997 12.6667 11.333C12.6667 10.9663 12.5361 10.6525 12.275 10.3913C12.0139 10.1302 11.7 9.99967 11.3333 9.99967C10.9667 9.99967 10.6528 10.1302 10.3917 10.3913C10.1306 10.6525 10 10.9663 10 11.333C10 11.6997 10.1306 12.0136 10.3917 12.2747C10.6528 12.5358 10.9667 12.6663 11.3333 12.6663ZM4.66667 11.9997C3.93333 11.9997 3.30556 11.7386 2.78333 11.2163C2.26111 10.6941 2 10.0663 2 9.33301C2 8.59967 2.26111 7.9719 2.78333 7.44967C3.30556 6.92745 3.93333 6.66634 4.66667 6.66634C5.4 6.66634 6.02778 6.92745 6.55 7.44967C7.07222 7.9719 7.33333 8.59967 7.33333 9.33301C7.33333 10.0663 7.07222 10.6941 6.55 11.2163C6.02778 11.7386 5.4 11.9997 4.66667 11.9997ZM4.66667 10.6663C5.03333 10.6663 5.34722 10.5358 5.60833 10.2747C5.86944 10.0136 6 9.69967 6 9.33301C6 8.96634 5.86944 8.65245 5.60833 8.39134C5.34722 8.13023 5.03333 7.99967 4.66667 7.99967C4.3 7.99967 3.98611 8.13023 3.725 8.39134C3.46389 8.65245 3.33333 8.96634 3.33333 9.33301C3.33333 9.69967 3.46389 10.0136 3.725 10.2747C3.98611 10.5358 4.3 10.6663 4.66667 10.6663ZM7.33333 6.66634C6.6 6.66634 5.97222 6.40523 5.45 5.88301C4.92778 5.36079 4.66667 4.73301 4.66667 3.99967C4.66667 3.26634 4.92778 2.63856 5.45 2.11634C5.97222 1.59412 6.6 1.33301 7.33333 1.33301C8.06667 1.33301 8.69444 1.59412 9.21667 2.11634C9.73889 2.63856 10 3.26634 10 3.99967C10 4.73301 9.73889 5.36079 9.21667 5.88301C8.69444 6.40523 8.06667 6.66634 7.33333 6.66634ZM7.33333 5.33301C7.7 5.33301 8.01389 5.20245 8.275 4.94134C8.53611 4.68023 8.66667 4.36634 8.66667 3.99967C8.66667 3.63301 8.53611 3.31912 8.275 3.05801C8.01389 2.7969 7.7 2.66634 7.33333 2.66634C6.96667 2.66634 6.65278 2.7969 6.39167 3.05801C6.13056 3.31912 6 3.63301 6 3.99967C6 4.36634 6.13056 4.68023 6.39167 4.94134C6.65278 5.20245 6.96667 5.33301 7.33333 5.33301Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default UniProfileGeneralInfoIcon;
