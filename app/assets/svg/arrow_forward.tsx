import { ArrowForwardProps } from '@/types'
import React from 'react';

const ArrowForward:React.FC<ArrowForwardProps> = ({ className }) => {
    return (
            <svg
                className={className}
                viewBox="0 0 15 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
            <mask
                id="mask0_1480_3555"
                style={{ maskType: 'alpha' }}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="15"
                height="15"
            >
                <rect width="15" height="15" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1480_3555)">
                <path
                    d="M5.01562 13.75L3.90625 12.6406L9.04687 7.5L3.90625 2.35937L5.01562 1.25L11.2656 7.5L5.01562 13.75Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    )
}

export default ArrowForward;



