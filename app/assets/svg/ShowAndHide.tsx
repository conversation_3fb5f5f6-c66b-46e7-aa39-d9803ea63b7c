import React from 'react'

const ShowAndHide = () => {
    return (
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_10777_10357" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="18">
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_10777_10357)">
                <path d="M9.00078 12C9.93828 12 10.7352 11.6719 11.3914 11.0156C12.0477 10.3594 12.3758 9.5625 12.3758 8.625C12.3758 7.6875 12.0477 6.89062 11.3914 6.23438C10.7352 5.57812 9.93828 5.25 9.00078 5.25C8.06328 5.25 7.26641 5.57812 6.61016 6.23438C5.95391 6.89062 5.62578 7.6875 5.62578 8.625C5.62578 9.5625 5.95391 10.3594 6.61016 11.0156C7.26641 11.6719 8.06328 12 9.00078 12ZM9.00078 10.65C8.43828 10.65 7.96016 10.4531 7.56641 10.0594C7.17266 9.66563 6.97578 9.1875 6.97578 8.625C6.97578 8.0625 7.17266 7.58437 7.56641 7.19063C7.96016 6.79688 8.43828 6.6 9.00078 6.6C9.56328 6.6 10.0414 6.79688 10.4352 7.19063C10.8289 7.58437 11.0258 8.0625 11.0258 8.625C11.0258 9.1875 10.8289 9.66563 10.4352 10.0594C10.0414 10.4531 9.56328 10.65 9.00078 10.65ZM9.00078 14.25C7.32578 14.25 5.79766 13.8 4.41641 12.9C3.03516 12 1.94453 10.8125 1.14453 9.3375C1.08203 9.225 1.03516 9.10938 1.00391 8.99063C0.972656 8.87188 0.957031 8.75 0.957031 8.625C0.957031 8.5 0.972656 8.37813 1.00391 8.25938C1.03516 8.14062 1.08203 8.025 1.14453 7.9125C1.94453 6.4375 3.03516 5.25 4.41641 4.35C5.79766 3.45 7.32578 3 9.00078 3C10.6758 3 12.2039 3.45 13.5852 4.35C14.9664 5.25 16.057 6.4375 16.857 7.9125C16.9195 8.025 16.9664 8.14062 16.9977 8.25938C17.0289 8.37813 17.0445 8.5 17.0445 8.625C17.0445 8.75 17.0289 8.87188 16.9977 8.99063C16.9664 9.10938 16.9195 9.225 16.857 9.3375C16.057 10.8125 14.9664 12 13.5852 12.9C12.2039 13.8 10.6758 14.25 9.00078 14.25Z" fill="#1E62E0" />
            </g>
        </svg>
    )
}

export default ShowAndHide