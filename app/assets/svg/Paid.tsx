import React from 'react'

const Paid = () => {
    return (
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_8510_25695" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
                <rect y="0.5" width="14" height="14" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_8510_25695)">
                <path d="M7.00033 13.3332C6.19338 13.3332 5.43505 13.18 4.72533 12.8738C4.0156 12.5675 3.39824 12.1519 2.87324 11.6269C2.34824 11.1019 1.93262 10.4846 1.62637 9.77484C1.32012 9.06512 1.16699 8.30678 1.16699 7.49984C1.16699 6.69289 1.32012 5.93456 1.62637 5.22484C1.93262 4.51512 2.34824 3.89775 2.87324 3.37275C3.39824 2.84775 4.0156 2.43213 4.72533 2.12588C5.43505 1.81963 6.19338 1.6665 7.00033 1.6665C7.80727 1.6665 8.5656 1.81963 9.27533 2.12588C9.98505 2.43213 10.6024 2.84775 11.1274 3.37275C11.6524 3.89775 12.068 4.51512 12.3743 5.22484C12.6805 5.93456 12.8337 6.69289 12.8337 7.49984C12.8337 8.30678 12.6805 9.06512 12.3743 9.77484C12.068 10.4846 11.6524 11.1019 11.1274 11.6269C10.6024 12.1519 9.98505 12.5675 9.27533 12.8738C8.5656 13.18 7.80727 13.3332 7.00033 13.3332ZM7.00033 12.1665C8.3031 12.1665 9.40658 11.7144 10.3107 10.8103C11.2149 9.90609 11.667 8.80262 11.667 7.49984C11.667 6.19706 11.2149 5.09359 10.3107 4.18942C9.40658 3.28525 8.3031 2.83317 7.00033 2.83317C5.69755 2.83317 4.59408 3.28525 3.68991 4.18942C2.78574 5.09359 2.33366 6.19706 2.33366 7.49984C2.33366 8.80262 2.78574 9.90609 3.68991 10.8103C4.59408 11.7144 5.69755 12.1665 7.00033 12.1665ZM6.98574 11.5832C7.12185 11.5832 7.24095 11.5321 7.34303 11.43C7.44512 11.328 7.49616 11.2089 7.49616 11.0728V10.854C7.98227 10.7665 8.40033 10.5769 8.75033 10.2853C9.10033 9.99359 9.27533 9.56095 9.27533 8.98734C9.27533 8.579 9.15866 8.2047 8.92533 7.86442C8.69199 7.52414 8.22533 7.22762 7.52533 6.97484C6.94199 6.78039 6.53852 6.61025 6.31491 6.46442C6.0913 6.31859 5.97949 6.11928 5.97949 5.8665C5.97949 5.61373 6.06942 5.41442 6.24928 5.26859C6.42915 5.12275 6.68921 5.04984 7.02949 5.04984C7.22394 5.04984 7.39408 5.08387 7.53991 5.15192C7.68574 5.21998 7.80727 5.31234 7.90449 5.429C8.00172 5.54567 8.11109 5.62588 8.23262 5.66963C8.35415 5.71338 8.46838 5.71095 8.57533 5.66234C8.72116 5.604 8.82081 5.50435 8.87428 5.36338C8.92776 5.22241 8.9156 5.09359 8.83783 4.97692C8.68227 4.75331 8.49026 4.56373 8.26178 4.40817C8.03331 4.25262 7.78783 4.16512 7.52533 4.14567V3.92692C7.52533 3.79081 7.47428 3.67171 7.3722 3.56963C7.27012 3.46755 7.15102 3.4165 7.01491 3.4165C6.8788 3.4165 6.7597 3.46755 6.65762 3.56963C6.55553 3.67171 6.50449 3.79081 6.50449 3.92692V4.14567C6.01838 4.25262 5.63921 4.4665 5.36699 4.78734C5.09477 5.10817 4.95866 5.46789 4.95866 5.8665C4.95866 6.32345 5.09234 6.69289 5.3597 6.97484C5.62706 7.25678 6.04755 7.49984 6.62116 7.704C7.23366 7.92762 7.65901 8.12692 7.8972 8.30192C8.1354 8.47692 8.25449 8.70539 8.25449 8.98734C8.25449 9.30817 8.14026 9.54393 7.91178 9.69463C7.68331 9.84532 7.40866 9.92067 7.08783 9.92067C6.83505 9.92067 6.60658 9.85991 6.40241 9.73838C6.19824 9.61685 6.0281 9.43456 5.89199 9.1915C5.81421 9.05539 5.71213 8.96303 5.58574 8.91442C5.45935 8.86581 5.33296 8.86581 5.20658 8.91442C5.07046 8.96303 4.97081 9.05539 4.90762 9.1915C4.84442 9.32762 4.84199 9.45887 4.90033 9.58525C5.05588 9.91581 5.26491 10.1856 5.52741 10.3946C5.78991 10.6037 6.10588 10.7471 6.47533 10.8248V11.0728C6.47533 11.2089 6.52637 11.328 6.62845 11.43C6.73053 11.5321 6.84963 11.5832 6.98574 11.5832Z" fill="#57585E" />
            </g>
        </svg>
    )
}

export default Paid