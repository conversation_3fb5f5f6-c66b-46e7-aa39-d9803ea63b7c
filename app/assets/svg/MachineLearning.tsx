import React from 'react'

const MachineLearning = () => {
    return (
        <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg" xlinkHref="http://www.w3.org/1999/xlink">
            <rect width="42" height="42" fill="url(#pattern0_7112_30689)"/>
            <defs>
            <pattern id="pattern0_7112_30689" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlinkHref="#image0_7112_30689" transform="translate(-0.00223214) scale(0.00446429)"/>
            </pattern>
            <image id="image0_7112_30689" width="225" height="224" preserveAspectRatio="none" xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADgCAIAAAB6jZaIAAAba0lEQVR4Ae2d70vb3vfAP//S9x/owz6yKshgTJDBGAXRIYwh7Ur6Q2e1Lq5aGDJQJj4RRkFBZIhsPvDBUFq7tiq4vXXzgb4dU5y8saBSvtycNos3aZsmOemNuaO4JE3b5NxXzj333HPO/d/sjn86/4i/HC2BqVznVK4zud2W3G6bzj/6sC+s/nj7+Wg2e7qyd7ZxeJH7+afw73///L46Oi8dy6/fV0f//vfPzz+Fw4vc3tlG9nRl82Rx9cfb9Lfo7I5f/kL48pbIZyrX+fXX6v/eFXqS221wHfb9zXdMSa9U0dfsCz5I/koN486/ye02MetNZDzJ7bYP+8Lno9m9s43z0nHZ0n9XN5c//xSypyvLB+K7Qk8i40lkPGLWaxswYtabPV0hjNrUzPmOVKEdiJzc6ppY7x5ffjK60DsyM/Bq+vlQcjAafxkZDoXv/osMh6Lxl7HxwFDqxcjMwOhCb2Lp6cR69+RWVwXuQnuFWhcgK2a9gGb6WzR7umI5lPUJv74tHV7kPh/Nzu747YHVFkarXE5udb3++Dg+3z+UHBRCESEQE4JR8gpF5FdYiPx9AamKI/JplQ8GYkIoEhsPjMwMjKX9E+vdlQcAkL1fvMpac/lAPLzI1SfJnnevbi6//lr9sC+gworJKKBZaBfXeuLz/dH4ywqUEpF3FaW5PUFCPBiF7x9KDo4u9L7ZeAi83gN7ALTm3G7f3tmGPfA1+yvXt6Xs6QpYApbbACiMAhwT690j75+FBUlfhiTtaA5FnZ8mulbiNRwOj8wMiGs95HqcqVmBzuUD0eYOvVlG5fN//imAWrWQVIsZBRTG0v7IcIhoNRvR1CAY9GsgFg6H4/P9YLw6Ra0CnZ+OZq5uLmUCnLJxXjpOf4vCYM78OMcyRgmduc74fD9YisSmZOdfFdaRmYHklwepos+84PC+Ibndlsh4lg9EJ9KpfIp+Xx3N7fYlMh6TsrKCUcl/NLrQC6MfdshUXwkxAwKxkZkBIjUm/VaJjGd2x//76kjZ2I7e3jvbAEeEYVLNMpoq+l5/fBwOh4VgVM0Em0fAOQB2qmHBWf5BUJ/Z0xVHE6l58de3JTBSjQnNBKOSHhpKvRACMbZ6dj1PhjSSE9d6GNGmYtY7u+N3eueuCah88OuvVWMWqkFGU0WfuNYDCkkPEiyeIxmpxp5saz+VyHg+H83KbXmPN85LxzBV25QAjTCaKvrG0n5Hqs+7z4oQjCaWnrZQlUL/zqzXE+Npub4tze74xaxXP6ZNM5oq+kbePyOAOv+fEIq8mn7eqmE+aBSnOD6t5XVut08/ps0xmir6Xk0/d9DwqMFzJEQiw6GWMAqhSffbAK2PtX5Mm2CUaNCZgfsDqMSvEIiBZ1d/12P+TAD0+rZUvxXv/bs6MdXLaKroIx7Qe9HFK5WrEIhNbnWZx07/N3BAlc+enqBQXYymCu2vPz6+f4ASt24g9mbjoW3DJrBB3dzFKwEtl8tXN5cNDVMdjOY7Jre67lkXL6tSIRi100uayHjcOUii0FTu/vvfP/XnSxszmir6YuMBEh1yH/8Jwejrj4/t0aOJjIeRuE8lIixsb54s1sG0AaOpQjtxhTpnnrPZ50gIRseXn9jAqJj1bp4ssgAEm9cwt9tXK5yvAaP3uJcHmu1hNLndNrfbZz8cVzeXhxe5zZPF5QNxbrdvKtcp55lA5DzYgrM7/vS36Kejma+/Vv/97x/7rxMM01qqtB6jFXf9Pe3l7WRUzHrt9DT9/FNY/fF2KtcpgwhjtVqKCt5V4ju327d5smiz6bx5sqg5fqrNKAyVWuJskhKYIBhA+beS59Rsd173fBv0aCLjsWe28/q29PloFuZXTeZtyrlTc7t9dtrQ0/lH6gepJqOpQjsJWLZLiRIWISEpEIsMhyAF9NX0c3gNpV4MJQdj4wHwFlmYF4U9ZoKsYht6z89Hs6A19Xtq9ZwJxE/nH9nzmB1e5NQ9fk1Gp/IddgAqSGgGo0OpF2NpP7gq6+faT+U7kl8evP74eHShl0QGAtxGk1KwfU+JjAfbG3p4kQO1p4c5Y+cAqXO7fdj3Ui6X1YOnGozmO8aXn2AP5+EZGEuTYhhk0hxKQuhJOIbKEVKe/lS+Q1zrGXn/zFgGlRCMTqx3I43rbRjLLx+IxoIyDcAKpH79tYraLajdpdqMpoo+UFF1bTlTb1ZyNqT8ZgPyuvORav7+xHp3fL6/Yg/os1KEQCz55QEGo2BX4TXn1c3lu0KP5iDjjnD0PPPNnJPIeFZ/vMW7L7Uq1WZ0KteJqkSFQGx8+Yn1AUdVWMW1HhKfBYmpdR8lIRDDABS8PHg23Hnp2OSoyAzHYtb7YV/Aw5SySrUYzXeQ2Xk0v70QiL3++Nh6QJXKIN+RKvomt7pg2EfuRTNPFS02DwJHkFrxvHRsW/9eC2VsTJUDfA1GUUf0QjA68v4ZLqAyrFVDYiztr2QF3iVVCEVGZgYwYvPErBfJboMgDLWDphZMeMfFrBev01f6SrUYlSbotRVP3X6z8ZsSIkh9a53GkCtTgBNAvjUhEEMaMCW325CUKBRbrHOzdr6F5/q9urmUnVAajE7lO+RWbIxdM2cIocjoQi+G3tLTMDKpsgdgLO3H0Oh4w/kP+4L9g6T6ssVzrn3YF6C7UDGKOb1kc7CmpnArhcokbxfS04LUbJD7q3lTLTyIN0kBxSNg9Hm3/mi+Y2K9G2vAJEQw9FYLW0j908nttvS3qOUdvbLvU/9oa4/gxRxCd6+hR7EG9UJkKDl47xlFarD0tygL4yTNhwHPiQHdvQajiaWnGHpUCEWGUi/uPaNi1mu5ElVPvWiy0sKDSIOnr79WIRqL7uuRgprdwCiSO0Y9hd1CHDV/Ornd9q7QY/nDCRYOrUcRA++FSDT+8n7r0UTG8/NPwdqmYl+JArUY914ul6fzj2xkVKqtp/kU3puDGB09y5aosuGS223LB6K1z2e5XF798TaR8djU18NMjz3JQ0rZ2bad3G5b+h63tpGub0uyK9u2GzH8Q4mMx9rbL5fLe2cbGowijZmIs18ga4Dc1+5ezHotDyLJnq6w5rSvQzCGBM5Lx8Nb/0frUSzfkzQjJQSjSLM7dWRnz1sYrvvZHT+zLie1VJG6ew09iujDB0wDMVJDme2i9OoGqH8Ew0fIst++ljQwAhXmdvtoPZr88gC7bI4d4Xly9JMtGxgqBLyDtWhg8zhGIZZPRzObJ4t312LMd2D48KngEyEQq3T6TK6d0CwBGMF4S9/jDuroQWIYcvj6a5VmNFX0ReMvkUKflKQKUqodWTZJWjenWSyYOj+R8VhePYGpG9R5MRizGD//FDQYHZkZsIFRMtCXsjqJQoWl6Gzpl3WKu6nTLHe7ONEYhdjW2R2/tR6oq5tLmtGpfAei+0mpSKvbQjAaGQ5BaXon6lSMAZMcmdbUo8LCyRgTGRqM2jBsqvJZ/V9KtA+Hw6MLvX9TmR2iVjG898pkCRbI038NGD64r79W746ZpPhfsoj83eyfKk2Y/1dLQozMDJCFvos+CJ7XL6CWnIkReO+UKVC1wMWs1/KghZ9/CipGC+2ksri+FHUMZomdGohF4y/H0n7216LFmF/RU4RbzQcLRzCG9r+vjmhGoV4Ntpe0MdygVgOxoeQgmeiXy5kwZgNgDOodNE1PPRhi1mv5kmhXN5cqRiUasEuVNGa0eoZc1IlNG8ByC8yhg3qAFWM64/q2pMHoFCQ2taS2YxVNjf+ldT4jw6HRhV52bADLHU9OiRmlNKjMKEZBYC1GQZUmB1tolWowCodkGyD1Aqb+W+uxstzbcniRc1C4E0UqUky+NqNT+Y43Gw9bb5XWRFWaApDqld4pvme7tWp5moQTZ+qVpFr+0JbL5RqMSqqUrG3XugF+bT4V74BaDUXi8/32GwAYCebOdY4CqZYbP/UYhZ8kONjvK1VAqHMTPFYjMwN2korhwP90NOPcvh7q8Fte/7+mHiWMsjl4qo0tTSpy148RRbF8IDqdUcsrPtdlVOrxHbdYKLir4vP9pAQaZvgfhjvQiVF5SnsUI4q0AaPgPHfiquBQebRSjxeHVAxG5XJcyoZ30HZrGAVMh5KDNoQ/1+7GDb0juVSHkoPESEUIVMWYrL8HjP6+OrI2Qq+xHoWHOFVoJ5NPaCWeDTGo60Ok6w9FMOpHc0bVCj6R8bSM0b+dPmvzT7pAJSuBx+f7rU3344xqMmp5VoJePVrRpkVfZQjlBIcURS8kqFgY8scZ1WTU8vC85hgFbUrS8KUOlIKA/V0hREpRWDXe54xqMtpiPVrRpoX2ya0uMooKxBzh4Vc+PH8xNe095Yyyyyi5MmmJGbIinpQ6p4SA/W3A1LxtisEo94+qfQJN9/XKRwccOmQlJB1rdjHFrhCMvpp+bhJTDP/o8oHouMx6JRIt848qL0K9DWt2wepyDur6hUAssfTUjN8Ug9HVH2/5XCilSk3p0b+8Sl1/8ssDnesgMqJThWDUzJKhYtb76WiGEqjJ3c9Hs05n1KQE1B+3iFEYf1RJJUF90Puz7aIyaZhipEY4PTbP8vjRGrkiJge81RU7Rxd6w1J8J8sGAKyxayz0BGOxG+cWgIBSJZYHfWvn3P3twc3ACit25jvGl5/ExgPsDqokTW+YUcvTd6iFh61pCzPt2MxnMYK+z0vHlvb1mvcjqdU3Gw+JARCMQjgSI/YoXEaleG/zQScY6TuOzrkTs17LC+Nr1IDAenAlUqGe1F+1yoi1Kl2GsQG+5eaXo3OXMRzGe2cb+HqUUq5VtRqf7yd5KGw4VoVg1NiSEhjpO46uAWH5ugCqmmQUT3i7krWaKrSLaz0Vd1Uw2sKhFVnlzNBqkZbXgIB1iRzqxrc8ME+rtiMelLW+GWyAXOdY2k8W7m4dqUIgNrnV1ezgCaOWjnPDnC3vVTRq5GIZo7UAVRyHhbtff3wM1qr9OlUIRqEMalNCELPew4uc2u1s5ohD3fgYI0iNWuNNNQ/KyZJaFdd67CdVCEUMzODDYoFmiFR/1qFlIDBmNFZ/vG2dParQoBqsS6S+/vi40vvb46mC0X2T6/JgpC871P2E8biq1r6pz43970qkQlCVPV2/AZM0ud1muRu/XC47cWhv+YAJ5HB3fSb7KdTxi6miT1zrISV98J2pQjAqrvU0O2zCWDvLWYvcQU9ouatYay1GHcRo9Mv4n0oV2pNfHpAOHxlTY8MmDPeT44ZNGGWFtNa0xafNOOL5DrKYBHKBNCEUGV3obXbCCWP1dsdVeMSoub58IKrWC2WZ0VwnOPxRy00KocjIzECzjGJMADprYXCkUmRTuU4Yh9k+F2riSUgVfWRSCk2bGnM/YXRz5XLZQZ58jBjF89JxIuNxHqPYlXvJjGjqRbNJTjBvqXZzmjzioEBSDGsne7oiZr0OZFSq44e3fJQxRqGnOy8dm4RS/XFHeKAwVvqTuxGnMorX3Rvr68Fssjzep1wuOyL/LpHxfP21qn7AzByRzXFnMlpoJ159HJPUMKMY04DlchlsMuPOEBOmv84fxbZznMoo3jJ8xsb10JyWe7BBDzG+LmMi48HoQOTxolMZHXn/DE+Pktp6zSeNgElqeZ0jxlUp0jywMhOhNqPVAORmZwV1dhBmTkN1PxlObAKT1PJlCEGVMlshH6MkSblcVmZvazOaKvom1rtHF3pHF3ph/WOmSE0VfYjj+mD09cfHxu4XyTIrl8vXtyUGq0IkMp7Nk0UzA6Nan4UcaNmComNKUoX2sbQfcoxIDqe0/nGlAjJOSfnmdCrMiKIV6RUCsTcbD40xitfdl8tlmLluTlaYAyYx6/2wL9SCzMxxKnVbW4/S1cSrJeXfbDwkzu2WkppCXrdcCEbNcIARSyq3Nzt+KCSHKNzp3G6fMpdLxahUr4FmVIovJgUcA7FX08+TXx60llTE0CchEo2/bHaSiWIaaXQvt1/LO/3kdltyu83yFZjgBtXB3TSjqUJ7gyGzpFNbRWqq6ENdG9KM40kmVcx6LXdoy6q0XC7P7vhbiCkqoPLckixMjZiSVNFHSjM3dI9D7596YeeIKlX0JZae4gY9GU2xV8oUtR9srTYVs953hR7L11mUn0C1EtVmVP+QGXr/2HgASicgGgBSxgiM5FDzmgwkiijplLcTGY/lmaJyQ8LG6o+3Nk/lJzKepe9x6jKs3aUsUZCnqq8v+ppWVNVlj0fePwO1ShzgVo2rJDpJ4f3Ui6YvrFmcBbKWg0ljFMSKkcKrpgGCoG3o9yH4CGMySXlTtRwXNKNTuU7DKIBajQyH4vP94AGowGqM1yqdZGo+GG1sfjRLpOp88N5b9XTZoEqhgUGhIpGa3G5LZDzLByJe/y5jCpau3BfJGzSjk1tdhhmtNLpAVpSDLxmZGRhffkL8AIX2VNFH/oKKhXVmVX8rpxV9k1td48tPQHfaQCdcuRCIyXIxv2GDVSq37tXNpUyq0mtj+C6S221i1gudO0bAoXzl8kYdt9pdRvMdFjAq6yeAVZoFCIfDQ8nBkZmB0YXexNLT1x8fi2s9E+vd8BLXel5/fJxYehqf7381/Zwk1Adi9ujOvxcLKSJNZtbXhwAjgURuVPXG9W3p66/Vud0+iF0Xs95amknzsoFLQHN2x795sojkXVJfueZQSb5ImlGS1IYxhQO8SmuOQf1R7b+wKBly5qfMpXLD5PSSLFBqAyNfVN3G1JHr29LhRe7z0eyHfQGmvhIZjwwuGJfwF47DW3O7fas/3u6dbdiGpnzZyplPSoCqcT3yNKOSCba2BSMldNTSVB/BKGosN63+jauby3//++fwInd4kds724DX4UXu55/CeenYfiiVV57+Fq1vTNN61Mq+ni0M612NEIiZWVpEjabyCEaMurKNHb2dPV1p6EG7y2iu04WMCsGo5YsxKxnFS3VyNJ3lcvnnn0JDQFV9vRQmg2KP1tNiLX1PiESGQ1b5myg05V07x/hOAVd/DgytR1MGfPgtZczkjwuB2MR6NzajoAyQItmcAqXyOq9uLvX7HDQY1T8XapKPln9cCEZJ2RxL/U2y7lRvoIbtKQlgfPv6tlR/IE+JToNRXTElLefL9AUYzqOnJNjUbiLjQUomYZxL+fKubi6bAlTDHiWxeTMDtk3tmCbN4BdYuH59U4zC+Mm1mJ6XjvV38bJgaT0KiydpxjgbxIHBj0mBzEQExgIJTCdgJDKe1R9vZdXikg0YxRuYqtVg9H67n4QQibRvIaCgHvCSgdgkfvNkUY+bSdadyg0Vo1I1JRLM0TDMmUEF2eiShGB0KDlI1GeLNCgl+neFntbO8dgD9Id9wTCgGvYoEWK+483Gw/vmJZUSB0beP7NtFK/EsdY2RL5hx2XaA6Lmr/z8U4BQlVoS0HNcQ49OSarUhqD3RlrPsvdJrGAoUkm/Nm1N6hFrU+ckMp70t6gNAZqaGCEdvL4tQQlmAwYoJT1tRgHT8eUnLVxyzhpCq3lXk1tdxsrjUPJC2oUoJNRMPSQWNb8WFpeqHymiX5I1GSWYFtrlJA3nmadSBktkOMRQ9YpGKhwy2rAToTSpsurg3tnGdP6RGetTzW49RsnZUsLGxHo3ceyzsTpyY/0q0RkWImNpP2IaYCPg1LLWcwQs1Nkdv+NI3TvbeFfoSWQ85jt3SlCNGIWWqJJKKtMGo8waAMTuDEbD4fBY2k+eLkO17ygBtWQXSJ3OP8qerjBup17fljZPFmFuwnI6Qfj6GFWQOrnVNZb2R+MvK2q1FTHztCqFxNRAbCg5SHp2yJrC0XN2IqtMKmJQrR5e5NLfohDDj0Rn84xWWx0y495sPBxd6K3ACnmb9vIKWlMIxGLjgdGFXjIqanUtKiSClbC2JJdDtlavbi73zjaWvsdtQFMWZjN6tMpo5cNQoLToS355kFh6+mr6OVmDrporR8ZYliOrTOKTsjsSS0+h+FQl3ZS6wnu3K8M6nX8EuUe/r45kgJA2zkvHe2cbn45mZnf8dqJpBaMyAdK0DUlNlngdX34Sn+8fSg7+RVbSsuCkJODWZ1c6AU6uaEopsxSiQEZmBhJLT0nyvpQMzciMkSxN2zYAVsjhTGQ8szv+pe/xzZPFvbMNSFEyZsVe3Vz+vjo6vMhlT1dWf7yFqiHAJWSZ2naDyh8yoUdlRpUbwGs1m35yq2tivXt8+cnoQu/IzMBQ6kVsPEDiU8NhMvYKxO68pOFOZDgUGw8MpV7Iic7iWg8kG0GGvmu5VDYbtQ3BROpUz6lc57tCz9xu34d9If0tuvQ9vvQ9vnwgwkb6W/TDvjC32zedfyRraJnIFkJJ3Z3VjCp5hW2ZWgBXUregdCt/6xxXFoxQfzM/okMCgG/DvxQWTO3iM6pDjkxJhF8MaxLgjHay1iT8eigJcEY5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEuAM8p6C1FKxYW7nFHOKOsS4Iyy3kIuVJzULXNGOaOsS4AzynoLUUrFhbucUc4o6xLgjLLeQi5UnNQtc0Y5o6xLgDPKegtRSsWFu5xRzijrEuCMst5CLlSc1C1zRjmjrEugwuh0/lFyu42/uAQYlECF0Q/7wtxuH39xCTAogdkd/97Zxv8DHKfgcTdLs0gAAAAASUVORK5CYII="/>
            </defs>
        </svg>
    )
}

export default MachineLearning