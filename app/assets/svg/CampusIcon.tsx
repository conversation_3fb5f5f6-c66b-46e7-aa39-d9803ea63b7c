import React from 'react'

const CampusIcon = () => {
  return (
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_9608_1995" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<rect width="20" height="20" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_9608_1995)">
<path d="M4.16663 18.3334C3.47218 18.3334 2.8819 18.0904 2.39579 17.6042C1.90968 17.1181 1.66663 16.5279 1.66663 15.8334C1.66663 15.2917 1.82288 14.8056 2.13538 14.3751C2.44788 13.9445 2.84718 13.6459 3.33329 13.4792V11.6667C3.33329 10.9723 3.57635 10.382 4.06246 9.89591C4.54857 9.4098 5.13885 9.16675 5.83329 9.16675H9.16663V6.52092C8.68051 6.35425 8.28121 6.05564 7.96871 5.62508C7.65621 5.19453 7.49996 4.70841 7.49996 4.16675C7.49996 3.4723 7.74301 2.88203 8.22913 2.39591C8.71524 1.9098 9.30551 1.66675 9.99996 1.66675C10.6944 1.66675 11.2847 1.9098 11.7708 2.39591C12.2569 2.88203 12.5 3.4723 12.5 4.16675C12.5 4.70841 12.3437 5.19453 12.0312 5.62508C11.7187 6.05564 11.3194 6.35425 10.8333 6.52092V9.16675H14.1666C14.8611 9.16675 15.4513 9.4098 15.9375 9.89591C16.4236 10.382 16.6666 10.9723 16.6666 11.6667V13.4792C17.1527 13.6459 17.552 13.9445 17.8645 14.3751C18.177 14.8056 18.3333 15.2917 18.3333 15.8334C18.3333 16.5279 18.0902 17.1181 17.6041 17.6042C17.118 18.0904 16.5277 18.3334 15.8333 18.3334C15.1388 18.3334 14.5486 18.0904 14.0625 17.6042C13.5763 17.1181 13.3333 16.5279 13.3333 15.8334C13.3333 15.2917 13.4895 14.8056 13.802 14.3751C14.1145 13.9445 14.5138 13.6459 15 13.4792V11.6667C15 11.4306 14.9201 11.2327 14.7604 11.073C14.6007 10.9133 14.4027 10.8334 14.1666 10.8334H10.8333V13.4792C11.3194 13.6459 11.7187 13.9445 12.0312 14.3751C12.3437 14.8056 12.5 15.2917 12.5 15.8334C12.5 16.5279 12.2569 17.1181 11.7708 17.6042C11.2847 18.0904 10.6944 18.3334 9.99996 18.3334C9.30551 18.3334 8.71524 18.0904 8.22913 17.6042C7.74301 17.1181 7.49996 16.5279 7.49996 15.8334C7.49996 15.2917 7.65621 14.8056 7.96871 14.3751C8.28121 13.9445 8.68051 13.6459 9.16663 13.4792V10.8334H5.83329C5.59718 10.8334 5.39926 10.9133 5.23954 11.073C5.07982 11.2327 4.99996 11.4306 4.99996 11.6667V13.4792C5.48607 13.6459 5.88538 13.9445 6.19788 14.3751C6.51038 14.8056 6.66663 15.2917 6.66663 15.8334C6.66663 16.5279 6.42357 17.1181 5.93746 17.6042C5.45135 18.0904 4.86107 18.3334 4.16663 18.3334ZM4.16663 16.6667C4.40274 16.6667 4.60065 16.5869 4.76038 16.4272C4.9201 16.2674 4.99996 16.0695 4.99996 15.8334C4.99996 15.5973 4.9201 15.3994 4.76038 15.2397C4.60065 15.0799 4.40274 15.0001 4.16663 15.0001C3.93051 15.0001 3.7326 15.0799 3.57288 15.2397C3.41315 15.3994 3.33329 15.5973 3.33329 15.8334C3.33329 16.0695 3.41315 16.2674 3.57288 16.4272C3.7326 16.5869 3.93051 16.6667 4.16663 16.6667ZM9.99996 16.6667C10.2361 16.6667 10.434 16.5869 10.5937 16.4272C10.7534 16.2674 10.8333 16.0695 10.8333 15.8334C10.8333 15.5973 10.7534 15.3994 10.5937 15.2397C10.434 15.0799 10.2361 15.0001 9.99996 15.0001C9.76385 15.0001 9.56593 15.0799 9.40621 15.2397C9.24649 15.3994 9.16663 15.5973 9.16663 15.8334C9.16663 16.0695 9.24649 16.2674 9.40621 16.4272C9.56593 16.5869 9.76385 16.6667 9.99996 16.6667ZM15.8333 16.6667C16.0694 16.6667 16.2673 16.5869 16.427 16.4272C16.5868 16.2674 16.6666 16.0695 16.6666 15.8334C16.6666 15.5973 16.5868 15.3994 16.427 15.2397C16.2673 15.0799 16.0694 15.0001 15.8333 15.0001C15.5972 15.0001 15.3993 15.0799 15.2395 15.2397C15.0798 15.3994 15 15.5973 15 15.8334C15 16.0695 15.0798 16.2674 15.2395 16.4272C15.3993 16.5869 15.5972 16.6667 15.8333 16.6667ZM9.99996 5.00008C10.2361 5.00008 10.434 4.92022 10.5937 4.7605C10.7534 4.60078 10.8333 4.40286 10.8333 4.16675C10.8333 3.93064 10.7534 3.73272 10.5937 3.573C10.434 3.41328 10.2361 3.33341 9.99996 3.33341C9.76385 3.33341 9.56593 3.41328 9.40621 3.573C9.24649 3.73272 9.16663 3.93064 9.16663 4.16675C9.16663 4.40286 9.24649 4.60078 9.40621 4.7605C9.56593 4.92022 9.76385 5.00008 9.99996 5.00008Z" fill="#1E62E0"/>
</g>
</svg>

  )
}

export default CampusIcon;

