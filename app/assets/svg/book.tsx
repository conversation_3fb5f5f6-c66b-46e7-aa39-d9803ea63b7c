import React from 'react';

const Book = () => {
    return (
        <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4174_28955"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="16"
                height="17"
            >
                <rect y="0.5" width="16" height="16" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4174_28955)">
                <path
                    d="M4.49935 15.1666C3.99935 15.1666 3.56879 14.9972 3.20768 14.6583C2.84657 14.3194 2.66602 13.9 2.66602 13.4V4.09998C2.66602 3.67776 2.79657 3.29998 3.05768 2.96665C3.31879 2.63332 3.66046 2.42221 4.08268 2.33332L9.08268 1.34998C9.49379 1.26109 9.86046 1.34998 10.1827 1.61665C10.5049 1.88332 10.666 2.22776 10.666 2.64998V10.6C10.666 10.9222 10.566 11.2083 10.366 11.4583C10.166 11.7083 9.91046 11.8611 9.59935 11.9166L4.34935 12.9667C4.24935 12.9889 4.16602 13.0416 4.09935 13.125C4.03268 13.2083 3.99935 13.3 3.99935 13.4C3.99935 13.5222 4.04935 13.625 4.14935 13.7083C4.24935 13.7916 4.36602 13.8333 4.49935 13.8333H11.9993V3.83332C11.9993 3.64443 12.0632 3.48609 12.191 3.35832C12.3188 3.23054 12.4771 3.16665 12.666 3.16665C12.8549 3.16665 13.0132 3.23054 13.141 3.35832C13.2688 3.48609 13.3327 3.64443 13.3327 3.83332V13.8333C13.3327 14.2 13.2021 14.5139 12.941 14.775C12.6799 15.0361 12.366 15.1666 11.9993 15.1666H4.49935ZM5.99935 11.2833L9.33268 10.6333V2.66665L5.99935 3.31665V11.2833ZM4.66602 11.55V3.58332L4.41602 3.63332C4.29379 3.65554 4.19379 3.70832 4.11602 3.79165C4.03824 3.87498 3.99935 3.97776 3.99935 4.09998V11.7167C4.0549 11.6944 4.11324 11.675 4.17435 11.6583C4.23546 11.6416 4.29379 11.6278 4.34935 11.6166L4.66602 11.55Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Book;
