import React from 'react'

const help = () => {
  return (
    <div>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_3840_9549" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
        <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_3840_9549)">
        <path d="M11.95 18.5C12.3 18.5 12.5958 18.3792 12.8375 18.1375C13.0792 17.8958 13.2 17.6 13.2 17.25C13.2 16.9 13.0792 16.6042 12.8375 16.3625C12.5958 16.1208 12.3 16 11.95 16C11.6 16 11.3042 16.1208 11.0625 16.3625C10.8208 16.6042 10.7 16.9 10.7 17.25C10.7 17.6 10.8208 17.8958 11.0625 18.1375C11.3042 18.3792 11.6 18.5 11.95 18.5ZM12 22.5C10.6167 22.5 9.31667 22.2375 8.1 21.7125C6.88333 21.1875 5.825 20.475 4.925 19.575C4.025 18.675 3.3125 17.6167 2.7875 16.4C2.2625 15.1833 2 13.8833 2 12.5C2 11.1167 2.2625 9.81667 2.7875 8.6C3.3125 7.38333 4.025 6.325 4.925 5.425C5.825 4.525 6.88333 3.8125 8.1 3.2875C9.31667 2.7625 10.6167 2.5 12 2.5C13.3833 2.5 14.6833 2.7625 15.9 3.2875C17.1167 3.8125 18.175 4.525 19.075 5.425C19.975 6.325 20.6875 7.38333 21.2125 8.6C21.7375 9.81667 22 11.1167 22 12.5C22 13.8833 21.7375 15.1833 21.2125 16.4C20.6875 17.6167 19.975 18.675 19.075 19.575C18.175 20.475 17.1167 21.1875 15.9 21.7125C14.6833 22.2375 13.3833 22.5 12 22.5ZM12 20.5C14.2333 20.5 16.125 19.725 17.675 18.175C19.225 16.625 20 14.7333 20 12.5C20 10.2667 19.225 8.375 17.675 6.825C16.125 5.275 14.2333 4.5 12 4.5C9.76667 4.5 7.875 5.275 6.325 6.825C4.775 8.375 4 10.2667 4 12.5C4 14.7333 4.775 16.625 6.325 18.175C7.875 19.725 9.76667 20.5 12 20.5ZM12.1 8.2C12.5167 8.2 12.8792 8.33333 13.1875 8.6C13.4958 8.86667 13.65 9.2 13.65 9.6C13.65 9.96667 13.5375 10.2917 13.3125 10.575C13.0875 10.8583 12.8333 11.125 12.55 11.375C12.1667 11.7083 11.8292 12.075 11.5375 12.475C11.2458 12.875 11.1 13.325 11.1 13.825C11.1 14.0583 11.1875 14.2542 11.3625 14.4125C11.5375 14.5708 11.7417 14.65 11.975 14.65C12.225 14.65 12.4375 14.5667 12.6125 14.4C12.7875 14.2333 12.9 14.025 12.95 13.775C13.0167 13.425 13.1667 13.1125 13.4 12.8375C13.6333 12.5625 13.8833 12.3 14.15 12.05C14.5333 11.6833 14.8625 11.2833 15.1375 10.85C15.4125 10.4167 15.55 9.93333 15.55 9.4C15.55 8.55 15.2042 7.85417 14.5125 7.3125C13.8208 6.77083 13.0167 6.5 12.1 6.5C11.4667 6.5 10.8625 6.63333 10.2875 6.9C9.7125 7.16667 9.275 7.575 8.975 8.125C8.85833 8.325 8.82083 8.5375 8.8625 8.7625C8.90417 8.9875 9.01667 9.15833 9.2 9.275C9.43333 9.40833 9.675 9.45 9.925 9.4C10.175 9.35 10.3833 9.20833 10.55 8.975C10.7333 8.725 10.9625 8.53333 11.2375 8.4C11.5125 8.26667 11.8 8.2 12.1 8.2Z" fill="currentColor"/>
        </g>
        </svg>
    </div>
  )
}

export default help