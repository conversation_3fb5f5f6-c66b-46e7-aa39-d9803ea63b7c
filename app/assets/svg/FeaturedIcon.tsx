import React from 'react'

const FeaturedIcon = () => {
    return (
        <svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="4" y="4" width="48" height="48" rx="24" fill="#DCE8FF" />
            <rect x="4" y="4" width="48" height="48" rx="24" stroke="#F4F7FE" strokeWidth="8" />
            <mask id="mask0_11362_37845" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="16" y="16" width="24" height="24">
                <rect x="16" y="16" width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_11362_37845)">
                <path d="M28 33C28.2833 33 28.5208 32.9042 28.7125 32.7125C28.9042 32.5208 29 32.2833 29 32C29 31.7167 28.9042 31.4792 28.7125 31.2875C28.5208 31.0958 28.2833 31 28 31C27.7167 31 27.4792 31.0958 27.2875 31.2875C27.0958 31.4792 27 31.7167 27 32C27 32.2833 27.0958 32.5208 27.2875 32.7125C27.4792 32.9042 27.7167 33 28 33ZM28 29C28.2833 29 28.5208 28.9042 28.7125 28.7125C28.9042 28.5208 29 28.2833 29 28V24C29 23.7167 28.9042 23.4792 28.7125 23.2875C28.5208 23.0958 28.2833 23 28 23C27.7167 23 27.4792 23.0958 27.2875 23.2875C27.0958 23.4792 27 23.7167 27 24V28C27 28.2833 27.0958 28.5208 27.2875 28.7125C27.4792 28.9042 27.7167 29 28 29ZM28 38C26.6167 38 25.3167 37.7375 24.1 37.2125C22.8833 36.6875 21.825 35.975 20.925 35.075C20.025 34.175 19.3125 33.1167 18.7875 31.9C18.2625 30.6833 18 29.3833 18 28C18 26.6167 18.2625 25.3167 18.7875 24.1C19.3125 22.8833 20.025 21.825 20.925 20.925C21.825 20.025 22.8833 19.3125 24.1 18.7875C25.3167 18.2625 26.6167 18 28 18C29.3833 18 30.6833 18.2625 31.9 18.7875C33.1167 19.3125 34.175 20.025 35.075 20.925C35.975 21.825 36.6875 22.8833 37.2125 24.1C37.7375 25.3167 38 26.6167 38 28C38 29.3833 37.7375 30.6833 37.2125 31.9C36.6875 33.1167 35.975 34.175 35.075 35.075C34.175 35.975 33.1167 36.6875 31.9 37.2125C30.6833 37.7375 29.3833 38 28 38ZM28 36C30.2333 36 32.125 35.225 33.675 33.675C35.225 32.125 36 30.2333 36 28C36 25.7667 35.225 23.875 33.675 22.325C32.125 20.775 30.2333 20 28 20C25.7667 20 23.875 20.775 22.325 22.325C20.775 23.875 20 25.7667 20 28C20 30.2333 20.775 32.125 22.325 33.675C23.875 35.225 25.7667 36 28 36Z" fill="#1E62E0" />
            </g>
        </svg>
    )
}

export default FeaturedIcon