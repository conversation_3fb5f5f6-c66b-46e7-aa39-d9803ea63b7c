import { IconProps } from '@/types'
import React from 'react'

const Files:React.FC<IconProps> = ({
    className
}) => {
  return (
<svg className={className} width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_9056_35837" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="17">
<rect y="0.5" width="16" height="16" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_9056_35837)">
<path d="M6.00001 9.16683H10C10.1889 9.16683 10.3472 9.10294 10.475 8.97516C10.6028 8.84738 10.6667 8.68905 10.6667 8.50016C10.6667 8.31127 10.6028 8.15294 10.475 8.02516C10.3472 7.89738 10.1889 7.8335 10 7.8335H6.00001C5.81112 7.8335 5.65278 7.89738 5.52501 8.02516C5.39723 8.15294 5.33334 8.31127 5.33334 8.50016C5.33334 8.68905 5.39723 8.84738 5.52501 8.97516C5.65278 9.10294 5.81112 9.16683 6.00001 9.16683ZM6.00001 11.1668H10C10.1889 11.1668 10.3472 11.1029 10.475 10.9752C10.6028 10.8474 10.6667 10.6891 10.6667 10.5002C10.6667 10.3113 10.6028 10.1529 10.475 10.0252C10.3472 9.89738 10.1889 9.8335 10 9.8335H6.00001C5.81112 9.8335 5.65278 9.89738 5.52501 10.0252C5.39723 10.1529 5.33334 10.3113 5.33334 10.5002C5.33334 10.6891 5.39723 10.8474 5.52501 10.9752C5.65278 11.1029 5.81112 11.1668 6.00001 11.1668ZM6.00001 13.1668H8.00001C8.18889 13.1668 8.34723 13.1029 8.47501 12.9752C8.60278 12.8474 8.66667 12.6891 8.66667 12.5002C8.66667 12.3113 8.60278 12.1529 8.47501 12.0252C8.34723 11.8974 8.18889 11.8335 8.00001 11.8335H6.00001C5.81112 11.8335 5.65278 11.8974 5.52501 12.0252C5.39723 12.1529 5.33334 12.3113 5.33334 12.5002C5.33334 12.6891 5.39723 12.8474 5.52501 12.9752C5.65278 13.1029 5.81112 13.1668 6.00001 13.1668ZM4.00001 15.1668C3.63334 15.1668 3.31945 15.0363 3.05834 14.7752C2.79723 14.5141 2.66667 14.2002 2.66667 13.8335V3.16683C2.66667 2.80016 2.79723 2.48627 3.05834 2.22516C3.31945 1.96405 3.63334 1.8335 4.00001 1.8335H8.78334C8.96112 1.8335 9.13056 1.86683 9.29167 1.9335C9.45278 2.00016 9.59445 2.09461 9.71667 2.21683L12.95 5.45016C13.0722 5.57238 13.1667 5.71405 13.2333 5.87516C13.3 6.03627 13.3333 6.20572 13.3333 6.3835V13.8335C13.3333 14.2002 13.2028 14.5141 12.9417 14.7752C12.6806 15.0363 12.3667 15.1668 12 15.1668H4.00001ZM12 6.50016H9.66667C9.38889 6.50016 9.15278 6.40294 8.95834 6.2085C8.76389 6.01405 8.66667 5.77794 8.66667 5.50016V3.16683H4.00001V13.8335H12V6.50016Z" fill="currentColor"/>
</g>
</svg>

  )
}

export default Files
