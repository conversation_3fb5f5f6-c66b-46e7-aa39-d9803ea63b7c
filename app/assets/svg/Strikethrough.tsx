import React from 'react';
import { xIconProps } from '@/types';

const Strikethrough: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4501_10738"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4501_10738)">
                <path
                    d="M2.25 10.5C2.0375 10.5 1.85937 10.4281 1.71562 10.2844C1.57187 10.1406 1.5 9.9625 1.5 9.75C1.5 9.5375 1.57187 9.35938 1.71562 9.21563C1.85937 9.07188 2.0375 9 2.25 9H15.75C15.9625 9 16.1406 9.07188 16.2844 9.21563C16.4281 9.35938 16.5 9.5375 16.5 9.75C16.5 9.9625 16.4281 10.1406 16.2844 10.2844C16.1406 10.4281 15.9625 10.5 15.75 10.5H2.25ZM7.875 7.5V5.25H4.875C4.5625 5.25 4.29688 5.14063 4.07812 4.92188C3.85937 4.70312 3.75 4.4375 3.75 4.125C3.75 3.8125 3.85937 3.54688 4.07812 3.32812C4.29688 3.10937 4.5625 3 4.875 3H13.125C13.4375 3 13.7031 3.10937 13.9219 3.32812C14.1406 3.54688 14.25 3.8125 14.25 4.125C14.25 4.4375 14.1406 4.70312 13.9219 4.92188C13.7031 5.14063 13.4375 5.25 13.125 5.25H10.125V7.5H7.875ZM7.875 12H10.125V13.875C10.125 14.1875 10.0156 14.4531 9.79688 14.6719C9.57812 14.8906 9.3125 15 9 15C8.6875 15 8.42188 14.8906 8.20312 14.6719C7.98437 14.4531 7.875 14.1875 7.875 13.875V12Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Strikethrough;
