import React from 'react'

const InPerson = () => {
  return (
<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="28" rx="14" fill="#E7EFFF"/>
<mask id="mask0_7392_20663" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="5" y="5" width="18" height="18">
<rect x="5" y="5" width="18" height="18" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_7392_20663)">
<path d="M14 14C13.175 14 12.4688 13.7063 11.8813 13.1188C11.2938 12.5312 11 11.825 11 11C11 10.175 11.2938 9.46875 11.8813 8.88125C12.4688 8.29375 13.175 8 14 8C14.825 8 15.5312 8.29375 16.1187 8.88125C16.7063 9.46875 17 10.175 17 11C17 11.825 16.7063 12.5312 16.1187 13.1188C15.5312 13.7063 14.825 14 14 14ZM8 20V17.9C8 17.475 8.10937 17.0844 8.32812 16.7281C8.54688 16.3719 8.8375 16.1 9.2 15.9125C9.975 15.525 10.7625 15.2344 11.5625 15.0406C12.3625 14.8469 13.175 14.75 14 14.75C14.825 14.75 15.6375 14.8469 16.4375 15.0406C17.2375 15.2344 18.025 15.525 18.8 15.9125C19.1625 16.1 19.4531 16.3719 19.6719 16.7281C19.8906 17.0844 20 17.475 20 17.9V20H8Z" fill="#1E62E0"/>
</g>
</svg>

  )
}

export default InPerson;

