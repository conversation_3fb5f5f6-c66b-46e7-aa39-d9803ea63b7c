import React from 'react'

const Unselect = () => {
    return (
        <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_10777_11843" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="19" height="18">
                <rect x="0.800781" width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_10777_11843)">
                <path d="M15.1074 16.425L11.4324 12.75H7.55117C7.13867 12.75 6.78555 12.6031 6.4918 12.3094C6.19805 12.0156 6.05117 11.6625 6.05117 11.25V7.36875L2.37617 3.69375C2.23867 3.55625 2.16992 3.38438 2.16992 3.17813C2.16992 2.97188 2.23867 2.79375 2.37617 2.64375C2.52617 2.49375 2.7043 2.41875 2.91055 2.41875C3.1168 2.41875 3.29492 2.49375 3.44492 2.64375L16.1762 15.375C16.3262 15.525 16.398 15.7 16.3918 15.9C16.3855 16.1 16.3074 16.275 16.1574 16.425C16.0074 16.5625 15.8324 16.6344 15.6324 16.6406C15.4324 16.6469 15.2574 16.575 15.1074 16.425ZM9.93242 11.25L7.55117 8.86875V11.25H9.93242ZM13.5512 10.6313L12.0512 9.13125V6.75H9.66992L8.16992 5.25H12.0512C12.4637 5.25 12.8168 5.39688 13.1105 5.69063C13.4043 5.98438 13.5512 6.3375 13.5512 6.75V10.6313ZM6.80117 3.75C6.58867 3.75 6.41055 3.67813 6.2668 3.53438C6.12305 3.39063 6.05117 3.2125 6.05117 3C6.05117 2.7875 6.12305 2.60938 6.2668 2.46563C6.41055 2.32188 6.58867 2.25 6.80117 2.25C7.01367 2.25 7.1918 2.32188 7.33555 2.46563C7.4793 2.60938 7.55117 2.7875 7.55117 3C7.55117 3.2125 7.4793 3.39063 7.33555 3.53438C7.1918 3.67813 7.01367 3.75 6.80117 3.75ZM9.80117 3.75C9.58867 3.75 9.41055 3.67813 9.2668 3.53438C9.12305 3.39063 9.05117 3.2125 9.05117 3C9.05117 2.7875 9.12305 2.60938 9.2668 2.46563C9.41055 2.32188 9.58867 2.25 9.80117 2.25C10.0137 2.25 10.1918 2.32188 10.3355 2.46563C10.4793 2.60938 10.5512 2.7875 10.5512 3C10.5512 3.2125 10.4793 3.39063 10.3355 3.53438C10.1918 3.67813 10.0137 3.75 9.80117 3.75ZM12.8012 3.75C12.5887 3.75 12.4105 3.67813 12.2668 3.53438C12.123 3.39063 12.0512 3.2125 12.0512 3C12.0512 2.7875 12.123 2.60938 12.2668 2.46563C12.4105 2.32188 12.5887 2.25 12.8012 2.25C13.0137 2.25 13.1918 2.32188 13.3355 2.46563C13.4793 2.60938 13.5512 2.7875 13.5512 3C13.5512 3.2125 13.4793 3.39063 13.3355 3.53438C13.1918 3.67813 13.0137 3.75 12.8012 3.75ZM15.8012 3.75C15.5887 3.75 15.4105 3.67813 15.2668 3.53438C15.123 3.39063 15.0512 3.2125 15.0512 3C15.0512 2.7875 15.123 2.60938 15.2668 2.46563C15.4105 2.32188 15.5887 2.25 15.8012 2.25C16.0137 2.25 16.1918 2.32188 16.3355 2.46563C16.4793 2.60938 16.5512 2.7875 16.5512 3C16.5512 3.2125 16.4793 3.39063 16.3355 3.53438C16.1918 3.67813 16.0137 3.75 15.8012 3.75ZM3.80117 6.75C3.58867 6.75 3.41055 6.67813 3.2668 6.53438C3.12305 6.39063 3.05117 6.2125 3.05117 6C3.05117 5.7875 3.12305 5.60938 3.2668 5.46563C3.41055 5.32188 3.58867 5.25 3.80117 5.25C4.01367 5.25 4.1918 5.32188 4.33555 5.46563C4.4793 5.60938 4.55117 5.7875 4.55117 6C4.55117 6.2125 4.4793 6.39063 4.33555 6.53438C4.1918 6.67813 4.01367 6.75 3.80117 6.75ZM15.8012 6.75C15.5887 6.75 15.4105 6.67813 15.2668 6.53438C15.123 6.39063 15.0512 6.2125 15.0512 6C15.0512 5.7875 15.123 5.60938 15.2668 5.46563C15.4105 5.32188 15.5887 5.25 15.8012 5.25C16.0137 5.25 16.1918 5.32188 16.3355 5.46563C16.4793 5.60938 16.5512 5.7875 16.5512 6C16.5512 6.2125 16.4793 6.39063 16.3355 6.53438C16.1918 6.67813 16.0137 6.75 15.8012 6.75ZM3.80117 9.75C3.58867 9.75 3.41055 9.67813 3.2668 9.53438C3.12305 9.39063 3.05117 9.2125 3.05117 9C3.05117 8.7875 3.12305 8.60938 3.2668 8.46563C3.41055 8.32188 3.58867 8.25 3.80117 8.25C4.01367 8.25 4.1918 8.32188 4.33555 8.46563C4.4793 8.60938 4.55117 8.7875 4.55117 9C4.55117 9.2125 4.4793 9.39063 4.33555 9.53438C4.1918 9.67813 4.01367 9.75 3.80117 9.75ZM15.8012 9.75C15.5887 9.75 15.4105 9.67813 15.2668 9.53438C15.123 9.39063 15.0512 9.2125 15.0512 9C15.0512 8.7875 15.123 8.60938 15.2668 8.46563C15.4105 8.32188 15.5887 8.25 15.8012 8.25C16.0137 8.25 16.1918 8.32188 16.3355 8.46563C16.4793 8.60938 16.5512 8.7875 16.5512 9C16.5512 9.2125 16.4793 9.39063 16.3355 9.53438C16.1918 9.67813 16.0137 9.75 15.8012 9.75ZM3.80117 12.75C3.58867 12.75 3.41055 12.6781 3.2668 12.5344C3.12305 12.3906 3.05117 12.2125 3.05117 12C3.05117 11.7875 3.12305 11.6094 3.2668 11.4656C3.41055 11.3219 3.58867 11.25 3.80117 11.25C4.01367 11.25 4.1918 11.3219 4.33555 11.4656C4.4793 11.6094 4.55117 11.7875 4.55117 12C4.55117 12.2125 4.4793 12.3906 4.33555 12.5344C4.1918 12.6781 4.01367 12.75 3.80117 12.75ZM15.8012 12.75C15.5887 12.75 15.4105 12.6781 15.2668 12.5344C15.123 12.3906 15.0512 12.2125 15.0512 12C15.0512 11.7875 15.123 11.6094 15.2668 11.4656C15.4105 11.3219 15.5887 11.25 15.8012 11.25C16.0137 11.25 16.1918 11.3219 16.3355 11.4656C16.4793 11.6094 16.5512 11.7875 16.5512 12C16.5512 12.2125 16.4793 12.3906 16.3355 12.5344C16.1918 12.6781 16.0137 12.75 15.8012 12.75ZM3.80117 15.75C3.58867 15.75 3.41055 15.6781 3.2668 15.5344C3.12305 15.3906 3.05117 15.2125 3.05117 15C3.05117 14.7875 3.12305 14.6094 3.2668 14.4656C3.41055 14.3219 3.58867 14.25 3.80117 14.25C4.01367 14.25 4.1918 14.3219 4.33555 14.4656C4.4793 14.6094 4.55117 14.7875 4.55117 15C4.55117 15.2125 4.4793 15.3906 4.33555 15.5344C4.1918 15.6781 4.01367 15.75 3.80117 15.75ZM6.80117 15.75C6.58867 15.75 6.41055 15.6781 6.2668 15.5344C6.12305 15.3906 6.05117 15.2125 6.05117 15C6.05117 14.7875 6.12305 14.6094 6.2668 14.4656C6.41055 14.3219 6.58867 14.25 6.80117 14.25C7.01367 14.25 7.1918 14.3219 7.33555 14.4656C7.4793 14.6094 7.55117 14.7875 7.55117 15C7.55117 15.2125 7.4793 15.3906 7.33555 15.5344C7.1918 15.6781 7.01367 15.75 6.80117 15.75ZM9.80117 15.75C9.58867 15.75 9.41055 15.6781 9.2668 15.5344C9.12305 15.3906 9.05117 15.2125 9.05117 15C9.05117 14.7875 9.12305 14.6094 9.2668 14.4656C9.41055 14.3219 9.58867 14.25 9.80117 14.25C10.0137 14.25 10.1918 14.3219 10.3355 14.4656C10.4793 14.6094 10.5512 14.7875 10.5512 15C10.5512 15.2125 10.4793 15.3906 10.3355 15.5344C10.1918 15.6781 10.0137 15.75 9.80117 15.75ZM12.8012 15.75C12.5887 15.75 12.4105 15.6781 12.2668 15.5344C12.123 15.3906 12.0512 15.2125 12.0512 15C12.0512 14.7875 12.123 14.6094 12.2668 14.4656C12.4105 14.3219 12.5887 14.25 12.8012 14.25C13.0137 14.25 13.1918 14.3219 13.3355 14.4656C13.4793 14.6094 13.5512 14.7875 13.5512 15C13.5512 15.2125 13.4793 15.3906 13.3355 15.5344C13.1918 15.6781 13.0137 15.75 12.8012 15.75Z" fill="#1E62E0" />
            </g>
        </svg>
    )
}

export default Unselect