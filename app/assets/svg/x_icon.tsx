import React from 'react';
import { xIconProps } from '@/types';

const XIcon: React.FC<xIconProps> = ({ className }) => {
  return (
    <svg className={className} width="13" height="12" viewBox="0 0 13 12" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.25293 3L3.25293 9M3.25293 3L9.25293 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}

export default XIcon