import React from 'react'

const BronzeStar = () => {
  return (
    <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_1684_5460)">
<path d="M21.9844 63.1094C21.5469 63.9844 20.4531 64.3125 19.5781 63.7656C-1.09377 50.9687 -0.875025 19.9062 20.125 7.43749C20.6719 7.10937 21.4375 7.32812 21.7656 7.87499C22.0937 8.42187 21.875 9.07812 21.3281 9.40624C1.74998 21.1094 1.96873 49.1094 21.3281 60.9219C22.0937 61.3594 22.3125 62.3437 21.9844 63.1094Z" fill="#E6814A"/>
<path d="M21.2187 14.6563C19.6875 15.8594 18.0469 16.5156 16.4062 16.8438C14.7656 17.1719 13.2344 16.9531 12.0312 16.0781C14.7656 12.4688 19.4687 12.1406 21.2187 14.6563Z" fill="#F9B48F"/>
<path d="M14.5469 5.90625C12.3594 7.65625 10.9375 9.84375 10.6094 11.7031C10.1719 13.6719 10.7187 15.2031 11.9219 16.1875C14.5469 12.6875 16.2969 8.42187 14.5469 5.90625Z" fill="#F9B48F"/>
<path d="M15.9688 19.9062C14.875 21.3281 13.5625 22.5312 12.1406 23.1875C10.7188 23.8437 9.07813 24.1719 7.76562 23.625C9.29687 19.3594 13.5625 18.0469 15.9688 19.9062Z" fill="#F9B48F"/>
<path d="M7.10938 13.3438C5.57813 15.4219 4.92187 17.8281 5.14062 19.6875C5.35937 21.5469 6.23438 22.9688 7.65625 23.5156C9.1875 19.6875 9.51563 15.3125 7.10938 13.3438Z" fill="#F9B48F"/>
<path d="M12.5781 26.1406C11.9219 27.7813 10.9375 29.2031 9.73437 30.1875C8.53125 31.1719 7.10937 31.9375 5.57812 31.7188C6.125 27.3438 9.73438 24.9375 12.5781 26.1406Z" fill="#F9B48F"/>
<path d="M2.29689 22.2031C1.42189 24.6094 1.42189 27.0156 2.07814 28.7656C2.73439 30.5156 4.04689 31.6094 5.57813 31.7188C6.01563 27.5625 5.14063 23.4063 2.29689 22.2031Z" fill="#F9B48F"/>
<path d="M10.9375 32.9219C10.7187 34.6719 10.1719 36.3125 9.29687 37.625C8.42187 38.9375 7.21875 39.9219 5.6875 40.1406C5.03125 35.7656 7.98437 32.4844 10.9375 32.9219Z" fill="#F9B48F"/>
<path d="M-1.82762e-05 31.8281C-0.218768 34.3438 0.437482 36.6406 1.53123 38.1719C2.62498 39.7031 4.15623 40.3594 5.68748 40.1406C5.03123 35.9844 3.06248 32.2656 -1.82762e-05 31.8281Z" fill="#F9B48F"/>
<path d="M11.1562 39.9219C11.4843 41.6719 11.2656 43.4219 10.8281 44.8438C10.2812 46.375 9.40621 47.6875 7.98434 48.3438C6.23434 44.1875 8.20309 40.25 11.1562 39.9219Z" fill="#F9B48F"/>
<path d="M0.4375 41.6719C0.875 44.1875 2.07812 46.2656 3.60937 47.3594C5.14062 48.5625 6.78125 48.7813 8.09375 48.2344C6.34375 44.4063 3.39063 41.3438 0.4375 41.6719Z" fill="#F9B48F"/>
<path d="M13.2343 46.5938C14 48.2344 14.3281 49.875 14.2187 51.5156C14.1093 53.1562 13.6718 54.6875 12.4687 55.6719C9.62497 52.0625 10.5 47.6875 13.2343 46.5938Z" fill="#F9B48F"/>
<path d="M3.28125 51.1875C4.375 53.4844 6.125 55.2344 7.875 56C9.625 56.7656 11.375 56.6563 12.4687 55.6719C9.73437 52.2813 6.125 50.0938 3.28125 51.1875Z" fill="#F9B48F"/>
<path d="M17.1718 52.5C18.375 53.9219 19.25 55.4531 19.6875 57.0938C20.125 58.7344 20.125 60.375 19.3593 61.5781C15.2031 58.9531 14.7656 54.3594 17.1718 52.5Z" fill="#F9B48F"/>
<path d="M8.85938 59.7188C10.6094 61.6875 12.9063 62.8906 14.875 63.2188C16.8437 63.5469 18.4844 62.8906 19.3594 61.6875C15.4219 59.0625 11.2656 57.8594 8.85938 59.7188Z" fill="#F9B48F"/>
<path d="M48.0156 63.1094C48.4531 63.9844 49.5469 64.3125 50.4219 63.7656C71.0937 50.9687 70.875 19.9062 49.875 7.43749C49.3281 7.10937 48.5625 7.32812 48.2344 7.87499C47.9062 8.42187 48.125 9.07812 48.6719 9.40624C68.25 21.1094 67.9219 49 48.5625 60.9219C47.9062 61.3594 47.6875 62.3437 48.0156 63.1094Z" fill="#E6814A"/>
<path d="M48.7812 14.6563C50.3125 15.8594 51.9531 16.5156 53.5938 16.8438C55.2344 17.1719 56.7656 16.9531 57.9688 16.0781C55.2344 12.4688 50.5313 12.1406 48.7812 14.6563Z" fill="#F9B48F"/>
<path d="M55.4531 5.90625C57.6406 7.65625 59.0625 9.84375 59.3906 11.7031C59.8281 13.6719 59.2812 15.2031 58.0781 16.1875C55.4531 12.6875 53.7031 8.42187 55.4531 5.90625Z" fill="#F9B48F"/>
<path d="M54.0312 19.9062C55.125 21.3281 56.4375 22.5312 57.8594 23.1875C59.2812 23.8437 60.9219 24.1719 62.2344 23.625C60.7031 19.3594 56.4375 18.0469 54.0312 19.9062Z" fill="#F9B48F"/>
<path d="M62.8906 13.3438C64.4219 15.4219 65.0781 17.8281 64.8594 19.6875C64.6406 21.5469 63.7656 22.9688 62.3437 23.5156C60.8125 19.6875 60.4844 15.3125 62.8906 13.3438Z" fill="#F9B48F"/>
<path d="M57.4219 26.1406C58.0781 27.7813 59.0625 29.2031 60.2656 30.1875C61.4687 31.1719 62.8906 31.9375 64.4219 31.7188C63.875 27.3438 60.2656 24.9375 57.4219 26.1406Z" fill="#F9B48F"/>
<path d="M67.7031 22.2031C68.5781 24.6094 68.5781 27.0156 67.9219 28.7656C67.2656 30.5156 65.9531 31.6094 64.4219 31.7188C63.9844 27.5625 64.8594 23.4063 67.7031 22.2031Z" fill="#F9B48F"/>
<path d="M59.0625 32.9219C59.2813 34.6719 59.8281 36.3125 60.7031 37.625C61.5781 38.9375 62.7812 39.9219 64.3125 40.1406C64.9687 35.7656 62.0156 32.4844 59.0625 32.9219Z" fill="#F9B48F"/>
<path d="M70 31.8281C70.2188 34.3438 69.5625 36.6406 68.4687 38.1719C67.375 39.7031 65.8437 40.3594 64.3125 40.1406C64.9688 35.9844 66.9375 32.2656 70 31.8281Z" fill="#F9B48F"/>
<path d="M58.8437 39.9219C58.5156 41.6719 58.7343 43.4219 59.1718 44.8438C59.7187 46.375 60.5937 47.6875 62.0156 48.3438C63.7656 44.1875 61.7968 40.25 58.8437 39.9219Z" fill="#F9B48F"/>
<path d="M69.5625 41.6719C69.125 44.1875 67.9219 46.2656 66.3906 47.3594C64.8594 48.5625 63.2188 48.7813 61.9062 48.2344C63.6563 44.4063 66.6094 41.3438 69.5625 41.6719Z" fill="#F9B48F"/>
<path d="M56.7656 46.5938C56 48.2344 55.6719 49.875 55.7812 51.5156C55.8906 53.1562 56.3281 54.6875 57.5312 55.6719C60.375 52.0625 59.5 47.6875 56.7656 46.5938Z" fill="#F9B48F"/>
<path d="M66.7188 51.1875C65.625 53.4844 63.875 55.2344 62.125 56C60.375 56.7656 58.625 56.6563 57.5312 55.6719C60.2656 52.2813 63.875 50.0938 66.7188 51.1875Z" fill="#F9B48F"/>
<path d="M52.8281 52.5C51.625 53.9219 50.75 55.4531 50.3125 57.0938C49.875 58.7344 49.875 60.375 50.6406 61.5781C54.7968 58.9531 55.2343 54.3594 52.8281 52.5Z" fill="#F9B48F"/>
<path d="M61.1406 59.7188C59.3906 61.6875 57.0938 62.8906 55.125 63.2188C53.1563 63.5469 51.5156 62.8906 50.6406 61.6875C54.5781 59.0625 58.7344 57.8594 61.1406 59.7188Z" fill="#F9B48F"/>
<path d="M45.0625 40.25L47.3594 54.0313L35 47.5781L22.6406 54.0313L24.9375 40.25L14.9844 30.5156L28.7656 28.4375L35 15.9688L41.2344 28.4375L55.0156 30.5156L45.0625 40.25Z" fill="#F9B48F"/>
<path d="M35 15.9688V35.3281L41.2344 28.4375L35 15.9688Z" fill="#E6814A"/>
<path d="M55.0156 30.5156L45.0625 40.25L35 35.3281L55.0156 30.5156Z" fill="#E6814A"/>
<path d="M14.9844 30.5156L24.9375 40.25L35 35.3281L14.9844 30.5156Z" fill="#E6814A"/>
<path d="M35 35.3281V47.5781L47.3594 54.0313L35 35.3281Z" fill="#E6814A"/>
<path d="M35 35.3281V47.5781L22.6406 54.0313L35 35.3281Z" fill="#E6814A"/>
</g>
<defs>
<clipPath id="clip0_1684_5460">
<rect width="70" height="70" fill="white"/>
</clipPath>
</defs>
</svg>

  )
}

export default BronzeStar
