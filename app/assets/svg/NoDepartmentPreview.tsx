import React from 'react'

const NoDepartmentPreview = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_11201_79521)">
                <path d="M8.5293 46.0137V104.708H111.278V53.8061C111.278 50.2199 108.37 47.3119 104.784 47.3119H82.5806C81.0243 47.3119 79.5294 46.7045 78.4138 45.6203L73.8803 41.2111C72.7646 40.1254 71.2697 39.5195 69.7134 39.5195H15.0235C11.4373 39.5195 8.5293 42.4275 8.5293 46.0137Z" fill="#5B80C3" />
                <path d="M100.934 100.966H19.3008V6.3683C19.3008 2.85134 22.1521 0 25.6691 0H78.7508L100.932 21.4826V100.966H100.934Z" fill="white" />
                <path d="M78.752 0V16.8012C78.752 19.2497 80.7221 21.2418 83.169 21.2717L100.932 21.4826" fill="#E9F0FF" />
                <path d="M119.373 61.3531L112.312 114.844C111.924 117.794 109.408 119.999 106.433 119.999H13.9445C11.0286 119.999 8.54551 117.879 8.0876 115.001L0.809766 69.2997C0.24957 65.7859 2.96558 62.6026 6.52503 62.6026H37.2398C39.0148 62.6026 40.7206 61.9102 41.9936 60.6718L46.0377 56.7394C47.3108 55.501 49.0165 54.8086 50.7915 54.8086H113.637C117.134 54.8086 119.831 57.8865 119.375 61.3516L119.373 61.3531Z" fill="#E3E7FC" />
                <path d="M62.0709 40.6243L71.4259 31.2694C71.9656 30.7296 71.9656 29.8547 71.4259 29.315C70.8861 28.7752 70.0112 28.7752 69.4715 29.315L60.1165 38.6699L50.7615 29.315C50.2218 28.7752 49.3469 28.7752 48.8071 29.315C48.2674 29.8547 48.2674 30.7296 48.8071 31.2694L58.1621 40.6243L48.8071 49.9793C48.2674 50.519 48.2674 51.3939 48.8071 51.9337C49.3469 52.4734 50.2218 52.4734 50.7615 51.9337L60.1165 42.5787L69.4715 51.9337C70.0112 52.4734 70.8861 52.4734 71.4259 51.9337C71.9656 51.3939 71.9656 50.519 71.4259 49.9793L62.0709 40.6243Z" fill="#1E62E0" />
                <path d="M45.4268 57.2278L42.7715 54.2891L36.2985 60.1378L38.9539 63.0766L45.4268 57.2278Z" fill="#144296" />
                <path d="M41.3333 64.6915L14.5383 88.9058L14.0945 89.3071L12.6389 90.6195L12.1968 91.0207L8.13534 94.6887C6.28166 96.3646 3.42246 96.2198 1.74816 94.3662C0.072297 92.5125 0.217067 89.6533 2.07075 87.9774L6.1306 84.3094L6.57435 83.9097L8.02677 82.5942L8.47052 82.1929L35.2655 57.9786C36.2301 57.1068 37.7203 57.1823 38.5921 58.147L41.5001 61.3649C42.3718 62.3295 42.2963 63.8197 41.3317 64.6915H41.3333Z" fill="#1E62E0" />
                <path d="M1.17773 93.5808C1.33509 93.8578 1.5255 94.1222 1.74895 94.3692C3.42324 96.2245 6.28244 96.3677 8.13613 94.6918L12.1975 91.0238L12.6382 90.6225L14.0937 89.3101L14.5375 88.9089L41.334 64.6945C42.2971 63.8228 42.3726 62.3342 41.5024 61.368L40.8982 60.6992C41.3891 61.6276 41.2082 62.8047 40.393 63.5411L13.5965 87.7554L13.1527 88.1567L11.6971 89.4691L11.2565 89.8703L7.19512 93.5384C5.47205 95.0978 2.87878 95.082 1.17773 93.5793V93.5808Z" fill="#1952BB" />
                <path opacity="0.5" d="M75.2106 55.8632C83.5509 47.5229 83.5509 34.0006 75.2106 25.6603C66.8703 17.3199 53.348 17.32 45.0077 25.6603C36.6674 34.0006 36.6674 47.5229 45.0077 55.8632C53.348 64.2035 66.8703 64.2035 75.2106 55.8632Z" fill="white" />
                <path d="M61.3019 17.1986C48.2883 16.5408 37.204 26.5567 36.5463 39.5703C35.8885 52.5838 45.9044 63.665 58.9179 64.3243C71.9314 64.9821 83.0126 54.9693 83.672 41.9558C84.3297 28.9423 74.317 17.8579 61.3035 17.2002L61.3019 17.1986ZM59.0296 62.0914C47.2498 61.495 38.1844 51.4634 38.7792 39.6835C39.3756 27.9037 49.4072 18.8383 61.187 19.4331C72.9669 20.0295 82.0323 30.0611 81.4375 41.8409C80.8411 53.6208 70.8095 62.6862 59.0296 62.0914Z" fill="#1E62E0" />
                <path d="M12.6392 90.6199L12.197 91.0212L6.13086 84.3099L6.57461 83.9102L12.6392 90.6199Z" fill="#435BDB" />
                <path d="M8.46906 82.1968L8.02539 82.5977L14.0904 89.31L14.5341 88.9091L8.46906 82.1968Z" fill="#435BDB" />
            </g>
            <defs>
                <clipPath id="clip0_11201_79521">
                    <rect width="118.845" height="120" fill="white" transform="translate(0.578125)" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default NoDepartmentPreview