import React from 'react'

const StandardizedIcon = () => {
  return (
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_9173_4208" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<rect width="20" height="20" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_9173_4208)">
<path d="M7.29191 13.0343L8.72942 11.5968C8.81041 11.5158 8.90489 11.477 9.01288 11.4804C9.12086 11.4838 9.21534 11.5259 9.29633 11.6069C9.37057 11.6879 9.40768 11.7824 9.40768 11.8904C9.40768 11.9984 9.37057 12.0928 9.29633 12.1738L7.58549 13.8948C7.5045 13.9758 7.41002 14.0163 7.30204 14.0163C7.19405 14.0163 7.09957 13.9758 7.01858 13.8948L6.14798 13.0242C6.07374 12.95 6.03662 12.8555 6.03662 12.7407C6.03662 12.626 6.07374 12.5315 6.14798 12.4573C6.22222 12.383 6.3167 12.3459 6.43143 12.3459C6.54616 12.3459 6.64065 12.383 6.71488 12.4573L7.29191 13.0343ZM7.29191 9.79485L8.72942 8.35734C8.81041 8.27636 8.90489 8.23755 9.01288 8.24093C9.12086 8.2443 9.21534 8.28648 9.29633 8.36747C9.37057 8.44845 9.40768 8.54294 9.40768 8.65092C9.40768 8.7589 9.37057 8.85339 9.29633 8.93437L7.58549 10.6553C7.5045 10.7363 7.41002 10.7768 7.30204 10.7768C7.19405 10.7768 7.09957 10.7363 7.01858 10.6553L6.14798 9.78473C6.07374 9.71049 6.03662 9.61601 6.03662 9.50128C6.03662 9.38655 6.07374 9.29206 6.14798 9.21782C6.22222 9.14359 6.3167 9.10647 6.43143 9.10647C6.54616 9.10647 6.64065 9.14359 6.71488 9.21782L7.29191 9.79485ZM10.7237 13.3684C10.609 13.3684 10.5128 13.3296 10.4352 13.252C10.3576 13.1744 10.3188 13.0782 10.3188 12.9635C10.3188 12.8487 10.3576 12.7525 10.4352 12.6749C10.5128 12.5973 10.609 12.5585 10.7237 12.5585H13.5582C13.673 12.5585 13.7691 12.5973 13.8468 12.6749C13.9244 12.7525 13.9632 12.8487 13.9632 12.9635C13.9632 13.0782 13.9244 13.1744 13.8468 13.252C13.7691 13.3296 13.673 13.3684 13.5582 13.3684H10.7237ZM10.7237 10.1289C10.609 10.1289 10.5128 10.0901 10.4352 10.0125C10.3576 9.93489 10.3188 9.83872 10.3188 9.72399C10.3188 9.60926 10.3576 9.51309 10.4352 9.43548C10.5128 9.35786 10.609 9.31906 10.7237 9.31906H13.5582C13.673 9.31906 13.7691 9.35786 13.8468 9.43548C13.9244 9.51309 13.9632 9.60926 13.9632 9.72399C13.9632 9.83872 13.9244 9.93489 13.8468 10.0125C13.7691 10.0901 13.673 10.1289 13.5582 10.1289H10.7237Z" fill="#1E62E0"/>
<path d="M4.16667 17.5002C3.70833 17.5002 3.31597 17.337 2.98958 17.0106C2.66319 16.6842 2.5 16.2918 2.5 15.8335V4.16683C2.5 3.7085 2.66319 3.31613 2.98958 2.98975C3.31597 2.66336 3.70833 2.50016 4.16667 2.50016H7.64583C7.79861 2.01405 8.09722 1.61475 8.54167 1.30225C8.98611 0.989746 9.47222 0.833496 10 0.833496C10.5556 0.833496 11.0521 0.989746 11.4896 1.30225C11.9271 1.61475 12.2222 2.01405 12.375 2.50016H15.8333C16.2917 2.50016 16.684 2.66336 17.0104 2.98975C17.3368 3.31613 17.5 3.7085 17.5 4.16683V15.8335C17.5 16.2918 17.3368 16.6842 17.0104 17.0106C16.684 17.337 16.2917 17.5002 15.8333 17.5002H4.16667ZM4.16667 15.8335H15.8333V4.16683H14.1667V5.8335C14.1667 6.06961 14.0868 6.26752 13.9271 6.42725C13.7674 6.58697 13.5694 6.66683 13.3333 6.66683H6.66667C6.43056 6.66683 6.23264 6.58697 6.07292 6.42725C5.91319 6.26752 5.83333 6.06961 5.83333 5.8335V4.16683H4.16667V15.8335ZM10 4.16683C10.2361 4.16683 10.434 4.08697 10.5938 3.92725C10.7535 3.76752 10.8333 3.56961 10.8333 3.3335C10.8333 3.09738 10.7535 2.89947 10.5938 2.73975C10.434 2.58002 10.2361 2.50016 10 2.50016C9.76389 2.50016 9.56597 2.58002 9.40625 2.73975C9.24653 2.89947 9.16667 3.09738 9.16667 3.3335C9.16667 3.56961 9.24653 3.76752 9.40625 3.92725C9.56597 4.08697 9.76389 4.16683 10 4.16683Z" fill="#1E62E0"/>
</g>
</svg>
  )
}

export default StandardizedIcon;

