import React from 'react';

const Payment = () => {
    return (
        <div>
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_2875_5182" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
            <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_2875_5182)">
            <path d="M4 20.5C3.45 20.5 2.97917 20.3042 2.5875 19.9125C2.19583 19.5208 2 19.05 2 18.5V6.5C2 5.95 2.19583 5.47917 2.5875 5.0875C2.97917 4.69583 3.45 4.5 4 4.5H20C20.55 4.5 21.0208 4.69583 21.4125 5.0875C21.8042 5.47917 22 5.95 22 6.5V11.5C22 11.7833 21.9042 12.0208 21.7125 12.2125C21.5208 12.4042 21.2833 12.5 21 12.5H4V18.5H13C13.2833 18.5 13.5208 18.5958 13.7125 18.7875C13.9042 18.9792 14 19.2167 14 19.5C14 19.7833 13.9042 20.0208 13.7125 20.2125C13.5208 20.4042 13.2833 20.5 13 20.5H4ZM4 8.5H20V6.5H4V8.5ZM19 19.5H17C16.7167 19.5 16.4792 19.4042 16.2875 19.2125C16.0958 19.0208 16 18.7833 16 18.5C16 18.2167 16.0958 17.9792 16.2875 17.7875C16.4792 17.5958 16.7167 17.5 17 17.5H19V15.5C19 15.2167 19.0958 14.9792 19.2875 14.7875C19.4792 14.5958 19.7167 14.5 20 14.5C20.2833 14.5 20.5208 14.5958 20.7125 14.7875C20.9042 14.9792 21 15.2167 21 15.5V17.5H23C23.2833 17.5 23.5208 17.5958 23.7125 17.7875C23.9042 17.9792 24 18.2167 24 18.5C24 18.7833 23.9042 19.0208 23.7125 19.2125C23.5208 19.4042 23.2833 19.5 23 19.5H21V21.5C21 21.7833 20.9042 22.0208 20.7125 22.2125C20.5208 22.4042 20.2833 22.5 20 22.5C19.7167 22.5 19.4792 22.4042 19.2875 22.2125C19.0958 22.0208 19 21.7833 19 21.5V19.5Z" fill="currentColor"/>
            </g>
            </svg>
        </div>
    );
};

export default Payment;