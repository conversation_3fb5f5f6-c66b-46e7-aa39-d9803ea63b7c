import React from 'react';

const ApplicationFeeIcon = () => {
    return (
        <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27019"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="19"
                height="18"
            >
                <rect x="0.333984" width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27019)">
                <path
                    d="M8.58203 13.5V13.875C8.58203 13.975 8.61953 14.0625 8.69453 14.1375C8.76953 14.2125 8.85703 14.25 8.95703 14.25H9.70703C9.80703 14.25 9.89453 14.2125 9.96953 14.1375C10.0445 14.0625 10.082 13.975 10.082 13.875V13.5H10.832C11.0445 13.5 11.2227 13.4281 11.3664 13.2844C11.5102 13.1406 11.582 12.9625 11.582 12.75V10.5C11.582 10.2875 11.5102 10.1094 11.3664 9.96563C11.2227 9.82188 11.0445 9.75 10.832 9.75H8.58203V9H10.832C11.0445 9 11.2227 8.92813 11.3664 8.78438C11.5102 8.64063 11.582 8.4625 11.582 8.25C11.582 8.0375 11.5102 7.85938 11.3664 7.71563C11.2227 7.57188 11.0445 7.5 10.832 7.5H10.082V7.125C10.082 7.025 10.0445 6.9375 9.96953 6.8625C9.89453 6.7875 9.80703 6.75 9.70703 6.75H8.95703C8.85703 6.75 8.76953 6.7875 8.69453 6.8625C8.61953 6.9375 8.58203 7.025 8.58203 7.125V7.5H7.83203C7.61953 7.5 7.44141 7.57188 7.29766 7.71563C7.15391 7.85938 7.08203 8.0375 7.08203 8.25V10.5C7.08203 10.7125 7.15391 10.8906 7.29766 11.0344C7.44141 11.1781 7.61953 11.25 7.83203 11.25H10.082V12H7.83203C7.61953 12 7.44141 12.0719 7.29766 12.2156C7.15391 12.3594 7.08203 12.5375 7.08203 12.75C7.08203 12.9625 7.15391 13.1406 7.29766 13.2844C7.44141 13.4281 7.61953 13.5 7.83203 13.5H8.58203ZM4.83203 16.5C4.41953 16.5 4.06641 16.3531 3.77266 16.0594C3.47891 15.7656 3.33203 15.4125 3.33203 15V3C3.33203 2.5875 3.47891 2.23438 3.77266 1.94063C4.06641 1.64688 4.41953 1.5 4.83203 1.5H11.582L15.332 5.25V15C15.332 15.4125 15.1852 15.7656 14.8914 16.0594C14.5977 16.3531 14.2445 16.5 13.832 16.5H4.83203ZM4.83203 15H13.832V6H11.582C11.3695 6 11.1914 5.92812 11.0477 5.78438C10.9039 5.64062 10.832 5.4625 10.832 5.25V3H4.83203V15Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default ApplicationFeeIcon;
