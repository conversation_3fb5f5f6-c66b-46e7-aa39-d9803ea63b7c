import React from 'react'

interface KeyboardArrowDownProps {
    className?: string
}

const KeyboardArrowDown:React.FC<KeyboardArrowDownProps> = ({
    className
}) => {
    return (
        <svg className={className} width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_11829_17010" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="19" height="18">
                <rect x="0.800781" y="18" width="18" height="18" transform="rotate(-90 0.800781 18)" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_11829_17010)">
                <path d="M12.0336 8.99844C12.0336 9.09844 12.018 9.19219 11.9867 9.27969C11.9555 9.36719 11.9023 9.44844 11.8273 9.52344L8.37734 12.9734C8.23984 13.1109 8.06484 13.1797 7.85234 13.1797C7.63984 13.1797 7.46484 13.1109 7.32734 12.9734C7.18984 12.8359 7.12109 12.6609 7.12109 12.4484C7.12109 12.2359 7.18984 12.0609 7.32734 11.9234L10.2523 8.99844L7.32734 6.07344C7.18984 5.93594 7.12109 5.76094 7.12109 5.54844C7.12109 5.33594 7.18984 5.16094 7.32734 5.02344C7.46484 4.88594 7.63984 4.81719 7.85234 4.81719C8.06484 4.81719 8.23984 4.88594 8.37734 5.02344L11.8273 8.47344C11.9023 8.54844 11.9555 8.62969 11.9867 8.71719C12.018 8.80469 12.0336 8.89844 12.0336 8.99844Z" fill="#7A7B82" />
            </g>
        </svg>

    )
}

export default KeyboardArrowDown