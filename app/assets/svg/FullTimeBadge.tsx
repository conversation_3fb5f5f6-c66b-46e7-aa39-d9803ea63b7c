import React from 'react'

const FullTimeBadge = () => {
    return (
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.300781" width="24" height="24" rx="12" fill="#F4F7FE" />
            <mask id="mask0_10322_37194" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="5" y="5" width="15" height="14">
                <rect x="5.30078" y="5" width="14" height="14" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_10322_37194)">
                <path d="M8.80013 15.5013H12.3001V15.2388C12.3001 15.0735 12.254 14.9204 12.1616 14.7794C12.0692 14.6385 11.9404 14.5291 11.7751 14.4513C11.5807 14.3638 11.3838 14.2982 11.1845 14.2544C10.9852 14.2107 10.7737 14.1888 10.5501 14.1888C10.3265 14.1888 10.1151 14.2107 9.91576 14.2544C9.71645 14.2982 9.51957 14.3638 9.32513 14.4513C9.15985 14.5291 9.03103 14.6385 8.93867 14.7794C8.84631 14.9204 8.80013 15.0735 8.80013 15.2388V15.5013ZM13.9043 14.6263H15.3626C15.489 14.6263 15.5935 14.585 15.6762 14.5023C15.7588 14.4197 15.8001 14.3152 15.8001 14.1888C15.8001 14.0624 15.7588 13.9579 15.6762 13.8753C15.5935 13.7926 15.489 13.7513 15.3626 13.7513H13.9043C13.7779 13.7513 13.6734 13.7926 13.5908 13.8753C13.5081 13.9579 13.4668 14.0624 13.4668 14.1888C13.4668 14.3152 13.5081 14.4197 13.5908 14.5023C13.6734 14.585 13.7779 14.6263 13.9043 14.6263ZM10.5501 13.7513C10.7932 13.7513 10.9998 13.6662 11.1699 13.4961C11.3401 13.326 11.4251 13.1194 11.4251 12.8763C11.4251 12.6332 11.3401 12.4266 11.1699 12.2565C10.9998 12.0864 10.7932 12.0013 10.5501 12.0013C10.3071 12.0013 10.1005 12.0864 9.93034 12.2565C9.7602 12.4266 9.67513 12.6332 9.67513 12.8763C9.67513 13.1194 9.7602 13.326 9.93034 13.4961C10.1005 13.6662 10.3071 13.7513 10.5501 13.7513ZM13.9043 12.8763H15.3626C15.489 12.8763 15.5935 12.835 15.6762 12.7523C15.7588 12.6697 15.8001 12.5652 15.8001 12.4388C15.8001 12.3124 15.7588 12.2079 15.6762 12.1253C15.5935 12.0426 15.489 12.0013 15.3626 12.0013H13.9043C13.7779 12.0013 13.6734 12.0426 13.5908 12.1253C13.5081 12.2079 13.4668 12.3124 13.4668 12.4388C13.4668 12.5652 13.5081 12.6697 13.5908 12.7523C13.6734 12.835 13.7779 12.8763 13.9043 12.8763ZM7.63346 17.8346C7.31263 17.8346 7.03798 17.7204 6.80951 17.4919C6.58103 17.2635 6.4668 16.9888 6.4668 16.668V10.2513C6.4668 9.93047 6.58103 9.65582 6.80951 9.42734C7.03798 9.19887 7.31263 9.08464 7.63346 9.08464H10.5501V7.33464C10.5501 7.0138 10.6644 6.73915 10.8928 6.51068C11.1213 6.2822 11.396 6.16797 11.7168 6.16797H12.8835C13.2043 6.16797 13.479 6.2822 13.7074 6.51068C13.9359 6.73915 14.0501 7.0138 14.0501 7.33464V9.08464H16.9668C17.2876 9.08464 17.5623 9.19887 17.7908 9.42734C18.0192 9.65582 18.1335 9.93047 18.1335 10.2513V16.668C18.1335 16.9888 18.0192 17.2635 17.7908 17.4919C17.5623 17.7204 17.2876 17.8346 16.9668 17.8346H7.63346ZM7.63346 16.668H16.9668V10.2513H14.0501C14.0501 10.5721 13.9359 10.8468 13.7074 11.0753C13.479 11.3037 13.2043 11.418 12.8835 11.418H11.7168C11.396 11.418 11.1213 11.3037 10.8928 11.0753C10.6644 10.8468 10.5501 10.5721 10.5501 10.2513H7.63346V16.668ZM11.7168 10.2513H12.8835V7.33464H11.7168V10.2513Z" fill="#36373B" />
            </g>
        </svg>
    )
}

export default FullTimeBadge