import React from 'react';

const AvgGradProgramIcon = () => {
    return (
        <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27037"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="19"
                height="18"
            >
                <rect x="0.333984" width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27037)">
                <path
                    d="M4.08398 6H14.584V4.5H4.08398V6ZM4.08398 16.5C3.67148 16.5 3.31836 16.3531 3.02461 16.0594C2.73086 15.7656 2.58398 15.4125 2.58398 15V4.5C2.58398 4.0875 2.73086 3.73438 3.02461 3.44063C3.31836 3.14688 3.67148 3 4.08398 3H4.83398V2.25C4.83398 2.0375 4.90586 1.85938 5.04961 1.71562C5.19336 1.57187 5.37148 1.5 5.58398 1.5C5.79648 1.5 5.97461 1.57187 6.11836 1.71562C6.26211 1.85938 6.33398 2.0375 6.33398 2.25V3H12.334V2.25C12.334 2.0375 12.4059 1.85938 12.5496 1.71562C12.6934 1.57187 12.8715 1.5 13.084 1.5C13.2965 1.5 13.4746 1.57187 13.6184 1.71562C13.7621 1.85938 13.834 2.0375 13.834 2.25V3H14.584C14.9965 3 15.3496 3.14688 15.6434 3.44063C15.9371 3.73438 16.084 4.0875 16.084 4.5V8.00625C16.084 8.21875 16.0121 8.39687 15.8684 8.54062C15.7246 8.68437 15.5465 8.75625 15.334 8.75625C15.1215 8.75625 14.9434 8.68437 14.7996 8.54062C14.6559 8.39687 14.584 8.21875 14.584 8.00625V7.5H4.08398V15H8.43398C8.64648 15 8.82461 15.0719 8.96836 15.2156C9.11211 15.3594 9.18398 15.5375 9.18398 15.75C9.18398 15.9625 9.11211 16.1406 8.96836 16.2844C8.82461 16.4281 8.64648 16.5 8.43398 16.5H4.08398ZM13.834 17.25C12.7965 17.25 11.9121 16.8844 11.1809 16.1531C10.4496 15.4219 10.084 14.5375 10.084 13.5C10.084 12.4625 10.4496 11.5781 11.1809 10.8469C11.9121 10.1156 12.7965 9.75 13.834 9.75C14.8715 9.75 15.7559 10.1156 16.4871 10.8469C17.2184 11.5781 17.584 12.4625 17.584 13.5C17.584 14.5375 17.2184 15.4219 16.4871 16.1531C15.7559 16.8844 14.8715 17.25 13.834 17.25ZM14.209 13.35V11.625C14.209 11.525 14.1715 11.4375 14.0965 11.3625C14.0215 11.2875 13.934 11.25 13.834 11.25C13.734 11.25 13.6465 11.2875 13.5715 11.3625C13.4965 11.4375 13.459 11.525 13.459 11.625V13.3313C13.459 13.4313 13.4777 13.5281 13.5152 13.6219C13.5527 13.7156 13.609 13.8 13.684 13.875L14.8277 15.0187C14.9027 15.0938 14.9902 15.1313 15.0902 15.1313C15.1902 15.1313 15.2777 15.0938 15.3527 15.0187C15.4277 14.9437 15.4652 14.8563 15.4652 14.7563C15.4652 14.6563 15.4277 14.5688 15.3527 14.4938L14.209 13.35Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default AvgGradProgramIcon;
