import React from 'react'

const DepartmentProfile = () => {
    return (
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.724609" y="0.625" width="23.75" height="23.75" rx="11.875" fill="#EFF0F0" />
            <rect x="0.724609" y="0.625" width="23.75" height="23.75" rx="11.875" stroke="#DEDEE0" strokeWidth="0.25" />
            <mask id="mask0_10906_29248" style={{ maskType: 'alpha' }} maskUnits="userSpaceOnUse" x="5" y="5" width="15" height="15">
                <rect x="5.59961" y="5.5" width="14" height="14" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_10906_29248)">
                <path d="M6.18262 15.532C6.18262 15.2015 6.26769 14.8977 6.43783 14.6206C6.60796 14.3435 6.83401 14.132 7.11595 13.9862C7.71873 13.6848 8.33123 13.4588 8.95345 13.3081C9.57567 13.1574 10.2076 13.082 10.8493 13.082C11.491 13.082 12.1229 13.1574 12.7451 13.3081C13.3673 13.4588 13.9798 13.6848 14.5826 13.9862C14.8646 14.132 15.0906 14.3435 15.2607 14.6206C15.4309 14.8977 15.516 15.2015 15.516 15.532V15.9987C15.516 16.3195 15.4017 16.5942 15.1732 16.8227C14.9448 17.0511 14.6701 17.1654 14.3493 17.1654H7.34928C7.02845 17.1654 6.7538 17.0511 6.52533 16.8227C6.29685 16.5942 6.18262 16.3195 6.18262 15.9987V15.532ZM17.8493 17.1654H16.3618C16.4687 16.9904 16.5489 16.8032 16.6024 16.6039C16.6559 16.4046 16.6826 16.2029 16.6826 15.9987V15.4154C16.6826 14.9876 16.5635 14.5768 16.3253 14.1831C16.0871 13.7893 15.7493 13.4515 15.3118 13.1695C15.8076 13.2279 16.2743 13.3275 16.7118 13.4685C17.1493 13.6095 17.5576 13.782 17.9368 13.9862C18.2868 14.1806 18.5541 14.397 18.7389 14.6352C18.9236 14.8734 19.016 15.1334 19.016 15.4154V15.9987C19.016 16.3195 18.9017 16.5942 18.6732 16.8227C18.4448 17.0511 18.1701 17.1654 17.8493 17.1654ZM10.8493 12.4987C10.2076 12.4987 9.65831 12.2702 9.20137 11.8133C8.74442 11.3563 8.51595 10.807 8.51595 10.1654C8.51595 9.5237 8.74442 8.97439 9.20137 8.51745C9.65831 8.0605 10.2076 7.83203 10.8493 7.83203C11.491 7.83203 12.0403 8.0605 12.4972 8.51745C12.9541 8.97439 13.1826 9.5237 13.1826 10.1654C13.1826 10.807 12.9541 11.3563 12.4972 11.8133C12.0403 12.2702 11.491 12.4987 10.8493 12.4987ZM16.6826 10.1654C16.6826 10.807 16.4541 11.3563 15.9972 11.8133C15.5403 12.2702 14.991 12.4987 14.3493 12.4987C14.2423 12.4987 14.1062 12.4865 13.941 12.4622C13.7757 12.4379 13.6396 12.4112 13.5326 12.382C13.7951 12.0709 13.9969 11.7258 14.1378 11.3466C14.2788 10.9674 14.3493 10.5737 14.3493 10.1654C14.3493 9.75703 14.2788 9.36328 14.1378 8.98411C13.9969 8.60495 13.7951 8.25981 13.5326 7.9487C13.6687 7.90009 13.8048 7.86849 13.941 7.85391C14.0771 7.83932 14.2132 7.83203 14.3493 7.83203C14.991 7.83203 15.5403 8.0605 15.9972 8.51745C16.4541 8.97439 16.6826 9.5237 16.6826 10.1654ZM7.34928 15.9987H14.3493V15.532C14.3493 15.4251 14.3225 15.3279 14.2691 15.2404C14.2156 15.1529 14.1451 15.0848 14.0576 15.0362C13.5326 14.7737 13.0028 14.5768 12.468 14.4456C11.9333 14.3143 11.3937 14.2487 10.8493 14.2487C10.3048 14.2487 9.76526 14.3143 9.23053 14.4456C8.69581 14.5768 8.16595 14.7737 7.64095 15.0362C7.55345 15.0848 7.48296 15.1529 7.42949 15.2404C7.37602 15.3279 7.34928 15.4251 7.34928 15.532V15.9987ZM10.8493 11.332C11.1701 11.332 11.4448 11.2178 11.6732 10.9893C11.9017 10.7609 12.016 10.4862 12.016 10.1654C12.016 9.84453 11.9017 9.56988 11.6732 9.34141C11.4448 9.11293 11.1701 8.9987 10.8493 8.9987C10.5285 8.9987 10.2538 9.11293 10.0253 9.34141C9.79685 9.56988 9.68262 9.84453 9.68262 10.1654C9.68262 10.4862 9.79685 10.7609 10.0253 10.9893C10.2538 11.2178 10.5285 11.332 10.8493 11.332Z" fill="#57585E" />
            </g>
        </svg>
    )
}

export default DepartmentProfile