import { IconProps } from '@/types'
import React from 'react'

const EditAction:React.FC<IconProps> = ({className}) => {
    return (
        <svg className={className} width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1453_14653" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="12" height="12">
                <rect width="12" height="12" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1453_14653)">
                <path d="M2.5 9.5H3.2125L8.1 4.6125L7.3875 3.9L2.5 8.7875V9.5ZM2 10.5C1.85833 10.5 1.73958 10.4521 1.64375 10.3562C1.54792 10.2604 1.5 10.1417 1.5 10V8.7875C1.5 8.65417 1.525 8.52708 1.575 8.40625C1.625 8.28542 1.69583 8.17917 1.7875 8.0875L8.1 1.7875C8.2 1.69583 8.31042 1.625 8.43125 1.575C8.55208 1.525 8.67917 1.5 8.8125 1.5C8.94583 1.5 9.075 1.525 9.2 1.575C9.325 1.625 9.43333 1.7 9.525 1.8L10.2125 2.5C10.3125 2.59167 10.3854 2.7 10.4313 2.825C10.4771 2.95 10.5 3.075 10.5 3.2C10.5 3.33333 10.4771 3.46042 10.4313 3.58125C10.3854 3.70208 10.3125 3.8125 10.2125 3.9125L3.9125 10.2125C3.82083 10.3042 3.71458 10.375 3.59375 10.425C3.47292 10.475 3.34583 10.5 3.2125 10.5H2ZM7.7375 4.2625L7.3875 3.9L8.1 4.6125L7.7375 4.2625Z" fill="currentColor" />
            </g>
        </svg>
    )
}

export default EditAction