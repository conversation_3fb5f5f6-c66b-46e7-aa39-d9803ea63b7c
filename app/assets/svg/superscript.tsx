import React from 'react';
import { xIconProps } from '@/types';

const Superscript: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4501_6250"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4501_6250)">
                <path
                    d="M15.0012 6.75C14.7887 6.75 14.6106 6.67813 14.4668 6.53438C14.3231 6.39063 14.2512 6.2125 14.2512 6V5.25C14.2512 5.0375 14.3231 4.85938 14.4668 4.71563C14.6106 4.57188 14.7887 4.5 15.0012 4.5H16.5012V3.75H14.6262C14.5262 3.75 14.4387 3.7125 14.3637 3.6375C14.2887 3.5625 14.2512 3.475 14.2512 3.375C14.2512 3.275 14.2887 3.1875 14.3637 3.1125C14.4387 3.0375 14.5262 3 14.6262 3H16.5012C16.7137 3 16.8918 3.07187 17.0356 3.21562C17.1793 3.35937 17.2512 3.5375 17.2512 3.75V4.5C17.2512 4.7125 17.1793 4.89063 17.0356 5.03438C16.8918 5.17813 16.7137 5.25 16.5012 5.25H15.0012V6H16.8762C16.9762 6 17.0637 6.0375 17.1387 6.1125C17.2137 6.1875 17.2512 6.275 17.2512 6.375C17.2512 6.475 17.2137 6.5625 17.1387 6.6375C17.0637 6.7125 16.9762 6.75 16.8762 6.75H15.0012ZM5.94493 15C5.60743 15 5.36055 14.8563 5.2043 14.5688C5.04805 14.2813 5.05743 13.9938 5.23243 13.7063L7.87618 9.54375L5.47618 5.775C5.30118 5.5 5.2918 5.21875 5.44805 4.93125C5.6043 4.64375 5.84493 4.5 6.16993 4.5C6.31993 4.5 6.45743 4.53438 6.58243 4.60313C6.70743 4.67188 6.80743 4.76875 6.88243 4.89375L8.96368 8.25H9.03868L11.1012 4.89375C11.1762 4.76875 11.2793 4.67188 11.4106 4.60313C11.5418 4.53438 11.6762 4.5 11.8137 4.5C12.1512 4.5 12.3949 4.64375 12.5449 4.93125C12.6949 5.21875 12.6824 5.50625 12.5074 5.79375L10.1074 9.54375L12.7699 13.7063C12.9449 13.9938 12.9512 14.2813 12.7887 14.5688C12.6262 14.8563 12.3824 15 12.0574 15C11.9074 15 11.7699 14.9656 11.6449 14.8969C11.5199 14.8281 11.4199 14.7313 11.3449 14.6063L9.03868 10.9312H8.96368L6.65743 14.6063C6.58243 14.7313 6.4793 14.8281 6.34805 14.8969C6.2168 14.9656 6.08243 15 5.94493 15Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Superscript;
