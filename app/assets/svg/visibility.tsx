import React from 'react';

type visibilityProps = {
    className:  string;
}

const visibility: React.FC<visibilityProps>  = ({className}) => {
    return (
        <svg className={className} width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_2578_3546" style={{ maskType: 'alpha' }} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
        <rect width="20" height="20" fill="currentColor"/>
        </mask>
        <g mask="url(#mask0_2578_3546)">
        <path d="M10 13.333C11.0417 13.333 11.9271 12.9684 12.6562 12.2393C13.3854 11.5101 13.75 10.6247 13.75 9.58301C13.75 8.54134 13.3854 7.65592 12.6562 6.92676C11.9271 6.19759 11.0417 5.83301 10 5.83301C8.95833 5.83301 8.07292 6.19759 7.34375 6.92676C6.61458 7.65592 6.25 8.54134 6.25 9.58301C6.25 10.6247 6.61458 11.5101 7.34375 12.2393C8.07292 12.9684 8.95833 13.333 10 13.333ZM10 11.833C9.375 11.833 8.84375 11.6143 8.40625 11.1768C7.96875 10.7393 7.75 10.208 7.75 9.58301C7.75 8.95801 7.96875 8.42676 8.40625 7.98926C8.84375 7.55176 9.375 7.33301 10 7.33301C10.625 7.33301 11.1562 7.55176 11.5938 7.98926C12.0312 8.42676 12.25 8.95801 12.25 9.58301C12.25 10.208 12.0312 10.7393 11.5938 11.1768C11.1562 11.6143 10.625 11.833 10 11.833ZM10 15.833C8.13889 15.833 6.44097 15.333 4.90625 14.333C3.37153 13.333 2.15972 12.0136 1.27083 10.3747C1.20139 10.2497 1.14931 10.1212 1.11458 9.98926C1.07986 9.85731 1.0625 9.7219 1.0625 9.58301C1.0625 9.44412 1.07986 9.3087 1.11458 9.17676C1.14931 9.04481 1.20139 8.91634 1.27083 8.79134C2.15972 7.15245 3.37153 5.83301 4.90625 4.83301C6.44097 3.83301 8.13889 3.33301 10 3.33301C11.8611 3.33301 13.559 3.83301 15.0937 4.83301C16.6285 5.83301 17.8403 7.15245 18.7292 8.79134C18.7986 8.91634 18.8507 9.04481 18.8854 9.17676C18.9201 9.3087 18.9375 9.44412 18.9375 9.58301C18.9375 9.7219 18.9201 9.85731 18.8854 9.98926C18.8507 10.1212 18.7986 10.2497 18.7292 10.3747C17.8403 12.0136 16.6285 13.333 15.0937 14.333C13.559 15.333 11.8611 15.833 10 15.833ZM10 14.1663C11.5694 14.1663 13.0104 13.7531 14.3229 12.9268C15.6354 12.1004 16.6389 10.9858 17.3333 9.58301C16.6389 8.18023 15.6354 7.06565 14.3229 6.23926C13.0104 5.41287 11.5694 4.99967 10 4.99967C8.43056 4.99967 6.98958 5.41287 5.67708 6.23926C4.36458 7.06565 3.36111 8.18023 2.66667 9.58301C3.36111 10.9858 4.36458 12.1004 5.67708 12.9268C6.98958 13.7531 8.43056 14.1663 10 14.1663Z" fill="currentColor"/>
        </g>
        </svg>
            )
}

export default visibility;