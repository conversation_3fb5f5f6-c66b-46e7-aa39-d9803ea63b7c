'use client';

import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import RegularStudent from './components/dashboard/RegularStudent';
import SuperAdmin from './components/dashboard/SuperAdmin';
import AgencyStudent from './components/dashboard/AgencyStudent';
import AccessRestricted from './components/AccessRestricted';
import University from './components/dashboard/University';
import Councelor from './components/dashboard/Counselor';
import AgentAdimn from './components/dashboard/AgentAdmin';
// 'use client'
// import UniversityList from './components/UniversityList';

// import Link from 'next/link';
// import { auth } from '../lib/auth';
// import { redirect } from 'next/navigation';
// import Image from 'next/image';
// // import React, {useState} from 'react';
// import Modal from './components/Modal';
// import { Label } from '@/components/ui/label';
// import { Button } from '@/components/ui/button';
// import AddIcon from '@/app/assets/svg/add-icon.svg';
// import UpcomingEvent from './components/UpcomingEvent';
// import { webinar, onGoingApplications } from '@/common';
// import StatCardLayout from './components/StatCardLayout';
// import DemoFileUpload from './components/DemoFileUpload';
// import ArrowRight from '@/app/assets/svg/arrow-right.svg';
// import StatInfoCardBig from './components/StatInfoCardBig';
// import Applications from '@/app/assets/svg/Applications.svg';
// import StatInfoCardSmall from './components/StatInfoCardSmall';
// import EnrollWebinarCard from './components/EnrollWebinarCard';
// import ApplicationStatus from './components/ApplicationStatus';
// import GlobalStudent from '@/app/assets/svg/global-student.svg';
// import DashboardLayout from './components/layout/DashboardLayout';
// // import SelectStudentTypeModal from './components/StudentTypeModal';
// import DialogIcon from '@/app/assets/svg/student-select-modal.svg';
// import { MultipleLineChart } from './components/MultipleLineChart';
// import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
// import FavoriteUniversity from '@/app/assets/svg/favorite-university.svg';
// import GlobalUniversities from '@/app/assets/svg/global-universities.svg';
// import ProfileCompleteProgress from './components/ProfileCompleteProgress';
// import StudentIndividualBanner from './components/StudentIndividualBanner';
// import InputFieldWithValidation from './components/InputFieldWithValidation';
// import SuggestedUniversities from '@/app/assets/svg/suggested-universities.svg';
// import StudentIndividualBannerIcon from '@/app/assets/svg/StudentIndividualBannerIcon';
// import { usePermissions } from '@/lib/utils';

// const page = async () => {
    // const session = await auth();
    // console.log(session)
    // const userRole = session?.user?.roles?.[0];
    // const userActions = session?.user?.actions;
    // console.log(userActions);
    // const findActions = session?.user?.actions?.map((action) => action.name) || [];
    // console.log(findActions)
    // if(findActions === 'Create') {
// const page = async () => {
//     // const session = await auth();
//     // console.log(session)
//     // const userRole = session?.user?.roles?.[0];
//     // const userActions = session?.user?.actions;
//     // console.log(userActions);
//     // const findActions = session?.user?.actions?.map((action) => action.name) || [];
//     // console.log(findActions)
//     // if(findActions === 'Create') {

//     // }
    
//     // console.log(session?.accessToken)
//     // if (!session?.user) redirect("/login");
//     // const [studentType, setStudentType] = useState('');
//     const permission = (await usePermissions())
//     console.log(permission)
//     const hasApplications = onGoingApplications.length > 0 ;
//     const totalApplications = 0
//     // interface FormData {
//     //     name: string;
//     //     email: string;
//     //     password: string;
//     //     age: string;
//     // }
//     // const [formData, setFormData] = useState<FormData>({
//     //     name: '',
//     //     email: '',
//     //     password: '',
//     //     age: '',
//     //   });
    
//     //   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     //     setFormData({ ...formData, [e.target.name]: e.target.value });
//     //   };
    
//     //   const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
//     //     e.preventDefault();
//     //     alert(`Form submitted with: ${JSON.stringify(formData, null, 2)}`);
//     //   };


//         const handleConfirm = () => {
//             // console.log(`Selected Student Type: ${studentType}`);
//             // You can do anything here like updating form state, saving to db, etc.
//         };

//     return (
//         <>
//             <DashboardLayout>
//                 {/* <DemoFileUpload /> */}
//                 {/* {session ? (
//                     <h1>Dashboard</h1>
//                 ): (
//                     <p>log in first</p>
//                 )} */}
//                 {/* <UniversityList /> */}
//                 {/* <SelectStudentTypeModal/> */}
//                 {/* <Modal openOnLoad onConfirm={handleConfirm}>
//                     {(close) => (
//                         <>
//                             <div className='flex flex-col gap-6 items-center'>
//                                 <Image src={DialogIcon} alt='dialog icon' />
//                                 <div>
//                                     <div className='text-center text-lg leading-[21px] font-semibold text-graySix mb-[14px]'>
//                                         Select Student Type
//                                     </div>
//                                     <div className='text-center font-normal text-base leading-5 text-graySix'>
//                                         Choose the category that best describes you.
//                                     </div>
//                                 </div>
//                             </div>

//                             <RadioGroup
//                                 value={studentType}
//                                 onValueChange={setStudentType}
//                                 className='flex pt-8 justify-center gap-8'
//                             >
//                                 <div className='flex items-center gap-1.5'>
//                                     <RadioGroupItem value='new-enrollment' id='new-enrollment' />
//                                     <Label htmlFor='new-enrollment' className='text-base font-normal text-graySix cursor-pointer'>
//                                         New Enrollment
//                                     </Label>
//                                 </div>
//                                 <div className='flex items-center gap-1.5'>
//                                     <RadioGroupItem value='transfer' id='transfer' />
//                                     <Label htmlFor='transfer' className='text-base font-normal text-graySix cursor-pointer'>
//                                         Transfer
//                                     </Label>
//                                 </div>
//                             </RadioGroup>

//                             <p className='text-xs font-normal leading-[18px] text-center py-[36px] text-grayFour'>
//                                 A New Enrollment Student is one starting their academic journey at an institution for the first time, without transferring credits.
//                             </p>

//                             <div className='flex w-full justify-center space-x-3'>
//                                 <Button
//                                     variant='outline'
//                                     onClick={close}
//                                     className='w-1/2 py-2.5 text-base font-semibold bg-white text-grayFive rounded-[8px]'
//                                 >
//                                     Cancel
//                                 </Button>
//                                 <Button
//                                     onClick={() => {
//                                         handleConfirm();
//                                         close();
//                                     }}
//                                     className='w-1/2 py-2.5 text-base font-semibold bg-primaryColor text-white rounded-[8px] hover:bg-tertiary'
//                                     disabled={!studentType}
//                                 >
//                                     Confirm
//                                 </Button>
//                             </div>
//                         </>
//                     )}
//                 </Modal> */}
                
//                 <div className='flex flex-col'>
//                     <div className='pt-5 pb-10'>
//                         {permission.hasPermission('Create') && (
//                             <StudentIndividualBanner
//                                 date='September 4, 2023'
//                                 description={`'Fast-Track Your Student Visa: Join Our Free Webinar!'`}
//                                 heading='Lee'
//                                 BannerImage={<StudentIndividualBannerIcon />}
//                             />
//                         )}
//                         {/* {userRole === 'Student' && (
//                             <StudentIndividualBanner
//                                 date='September 4, 2023'
//                                 description={`'Fast-Track Your Student Visa: Join Our Free Webinar!'`}
//                                 heading='Lee'
//                                 BannerImage={<StudentIndividualBannerIcon />}
//                             />
//                         )} */}
//                     </div>
//                     <div className='grid md:grid-cols-4 grid-cols-1 gap-6'>
//                         <div className='flex flex-col gap-6 md:col-span-3 col-span-1'>
//                             <div className={`grid md:grid-flow-col md:auto-cols-fr gap-6`}>
//                                 {totalApplications === 0 && (
//                                     <StatCardLayout>
//                                         <Link href={'/profile?tab=application'} className='flex py-[25px] gap-1.5 justify-center items-center'>
//                                             <Image src={AddIcon} alt='add icon' height={24} width={24} />
//                                             <p className='text-tertiary text-base leading-5 font-semibold p-2.5'>
//                                                 Create Your Application
//                                             </p>
//                                         </Link>
//                                     </StatCardLayout>
//                                 )}
//                                 {totalApplications > 0 && (
//                                     <StatCardLayout>
//                                         <StatInfoCardSmall
//                                             imageSrc={Applications}
//                                             description='Applications'
//                                             number={totalApplications}
//                                         />
//                                     </StatCardLayout>
//                                 )}
//                                 <StatCardLayout>
//                                     <StatInfoCardSmall
//                                         imageSrc={FavoriteUniversity}
//                                         description='Favorite Universities'
//                                         number={4}
//                                         link='favourite-universities'
//                                     />
//                                 </StatCardLayout>
//                                 <StatCardLayout>
//                                     <StatInfoCardSmall
//                                         imageSrc={SuggestedUniversities}
//                                         description='Suggested Courses'
//                                         number={20}
//                                         link='suggested-courses'
//                                     />
//                                 </StatCardLayout>
//                             </div>
//                             <div className='h-full'>
//                                 {hasApplications ? (
//                                     <StatCardLayout className='h-full'>
//                                         <div className='py-4 px-6 space-y-[22px]'>
//                                             <div className='flex justify-between items-center'>
//                                                 <h2 className='md:text-2xl text-base md:leading-[29px] leading-5 font-bold text-primaryColor'>Your Ongoing Applications</h2>
//                                                 <Link href={'/all-applications'} className='font-semibold md:text-sm text-[10px] md:leading-[17px] leading-[14px] text-primaryColor inline-flex items-center gap-2'>
//                                                     <span>View all</span>
//                                                     <Image
//                                                         src={ArrowRight}
//                                                         alt='arrow right'
//                                                     />
//                                                 </Link>
//                                             </div>
//                                             <div>
//                                                 <ul className='space-y-1.5'>
//                                                     {onGoingApplications.map((app, index) => (
//                                                     <li
//                                                         key={index}
//                                                         className=' py-2.5 flex space-x-4'
//                                                     >
//                                                         <Image
//                                                             src={app.logo}
//                                                             alt={`${app.university} logo`}
//                                                             className='w-12 h-12 rounded-full'
//                                                         />
//                                                         <div className='grid md:grid-cols-4 grid-cols-1 gap-3 items-center w-full'>
//                                                             <div className='md:col-span-2 space-y-1.5'>
                                                                
//                                                                 <h3 className='text-base leading-[18px] font-semibold text-graySix'>
//                                                                     {app.title}
//                                                                 </h3>
//                                                                 <p className='text-sm leading-[18px] font-normal text-graySix'>{app.university}</p>
//                                                                 <p className='text-xs leading-[15px] font-normal text-grayFour'>{app.location}</p>
//                                                             </div>
//                                                             <div className='flex md:justify-center justify-start'>
//                                                                 <p className='text-[13px] leading-4 font-medium text-grayFour text-start'>Created on: <br />{app.createdOn}</p>
//                                                             </div>
//                                                             <span className={`text-[13px] md:pl-10 flex justify-start leading-4 font-semibold text-primaryColor md:text-end text-start `}>
//                                                                 <ApplicationStatus status={app.status} />
//                                                             </span>
//                                                         </div>
//                                                     </li>
//                                                     ))}
//                                                 </ul>
//                                             </div>
//                                         </div>
//                                     </StatCardLayout>
//                                 ) : (
//                                 <StatCardLayout className='h-full'>
//                                     <MultipleLineChart />
//                                 </StatCardLayout>
//                                 )}
//                             </div>
//                         </div>
//                         <div className='grid grid-cols-1 gap-6'>
//                             <StatCardLayout>
//                                 <EnrollWebinarCard
//                                     totalEnrollee = {webinar[0].totalEnrollee}
//                                     title={webinar[0].title}
//                                     imageSrc={webinar[0].bannerImage}
//                                     date={webinar[0].date}
//                                     startTime={webinar[0].startTime}
//                                     endTime={webinar[0].endTime}
//                                     enrolled={webinar[0].enrollee}
//                                 />
//                             </StatCardLayout> 
//                             {/* <StatCardLayout>
//                                 <UpcomingEvent />
//                             </StatCardLayout> */}
//                             <StatCardLayout>
//                                 <ProfileCompleteProgress progressValue={90} />
//                             </StatCardLayout>  
//                         </div>
//                     </div>
//                     <div className='flex md:flex-row flex-col gap-6 pt-6 pb-20'>
//                         <div className='md:w-2/5 w-full'>
//                             <StatCardLayout>
//                                 <UpcomingEvent />
//                             </StatCardLayout>
//                         </div>
//                         <div className='md:w-3/5 w-full grid md:grid-cols-2 grid-cols-1 gap-6'>
//                             <StatCardLayout className='h-full'>
//                                 <StatInfoCardBig
//                                     imageSrc={GlobalStudent}
//                                     description='Global Students'
//                                     number='3345'
//                                 />
//                             </StatCardLayout>
//                             <StatCardLayout className='h-full'>
//                                 <StatInfoCardBig
//                                     imageSrc={GlobalUniversities}
//                                     description='Global Universities'
//                                     number='27'
//                                 />
//                             </StatCardLayout>
//                         </div>
//                     </div>
//                 </div>
//                  {/* <form onSubmit={handleSubmit} className='p-6 bg-white shadow-lg rounded-lg w-96'>
//                     <InputFieldWithValidation label='Name' type='text' name='name' value={formData.name} onChange={handleChange} />
//                     <InputFieldWithValidation label='Email' type='email' name='email' value={formData.email} onChange={handleChange} />
//                     <InputFieldWithValidation label='Password' type='password' name='password' value={formData.password} onChange={handleChange} />
//                     <InputFieldWithValidation label='Age' type='number' name='age' value={formData.age} onChange={handleChange} />
//                     <button type='submit' className='w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 transition'>
//                         Submit
//                     </button>
//                 </form> */}
//             </DashboardLayout>
//         </>
//     )

const page = () => {
    const user = useSelector((state: RootState) => state.auth.user);
    switch (user?.roles.name) {
    case 'SuperAdmin':
      return <SuperAdmin />;
    case 'Student':
      return <RegularStudent />;
    case 'AgencyStudent':
      return <AgencyStudent />;
    case 'University':
      return <University />;
    case 'AgentAdmin':
      return <AgentAdimn />;
    case 'Councilor':
      return <Councelor />;
    default:
      return <AccessRestricted />;
  }    
}

export default page;