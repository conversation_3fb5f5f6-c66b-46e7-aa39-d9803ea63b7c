'use client'

import { courses } from '@/common';
import React, {useState} from 'react';
import { StatusColor } from '@/common';
import Heading from '@/app/components/Heading';
import Pagination from '@/app/components/Pagination';
import FilterLists from '@/app/components/FilterLists';
import { DatePicker } from '@/app/components/DatePicker';
import DropDownButton from '@/app/components/DropDownButton';
import HarvardUniversity from '@/app/assets/svg/HarvardUniversity';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import SelectAndSearchCombobox from '@/app/components/SelectAndSearchCombobox';

const page = () => {
    const tableHeadData = [
        'UID',
        'University Name',
        'Total Applications',
        'Comission Type',
        'Commission (%) / Fixed',
        'Total Commission',
        'Total Students Payment',
        'Commission Paid',
        'Commission Due',
        'Last Payment Date',
        'Next Payment Due Date',
        'Commission Status',
        'Remarks',
    ];
    const comissionLists = [
        {
            uid: 262,
            logo: HarvardUniversity,
            universityName: 'Harvard University',
            totalApplications: 150,
            type: 'Fixed Amount',
            comission: '$1000',
            totalComission: '$37,500',
            totalStudentsPayment: '$150,000',
            paid: '$37,500',
            due: '$37,500',
            lastPaymentDate: '22 Oct 2023',
            nextPaymentDueDate: '22 Oct 2023',
            status: 'Pending',
            remarks: 'calculation under review.'
        }
    ];

    const [selected, setSelected] = React.useState("")
    const universities = [
        { value: 'harvard-university', label: 'Harvard University' },
        { value: 'national-university-of-singapore', label: 'National University of Singapore' },
        { value: 'university-of-toronto', label: 'University of Toronto' },
        { value: 'oxford-university', label: 'Oxford University' },
        { value: 'university-of-sydney', label: 'University of Sydney' },
        { value: 'university-of-melbourne', label: 'University of Melbourne' },
        { value: 'yale-university', label: 'Yale University' },
    ];

    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    const filterLists =['2 year Undergraduate Diploma', 'Dec 2024', 'Pending', 'USA'];

    return (
        <DashboardLayout>
            <Heading level='h1'>
                University Commission
            </Heading>
            
            <div className='grid grid-cols-6 gap-3'>
                <SelectAndSearchCombobox
                    options={universities}
                    label='University'
                    type='select'
                    selectedValue={selected}
                    onChange={setSelected}
                    placeholder="Choose a university"
                />
                <DatePicker />
                <DatePicker />
            </div>
            <div className='pt-5'>
                <FilterLists lists={filterLists} />
            </div>
            <div className='flex justify-between items-center pt-5'>
                <Heading level='h2'>
                    Comissions list
                </Heading>
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            {tableHeadData.map((thData, index) => (
                                <th 
                                    key={index} 
                                    className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                >
                                    {thData}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {comissionLists.map((list, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.uid}</td>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal flex gap-2.5 items-center'>
                                    <HarvardUniversity />
                                    {list.universityName}
                                </td>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.totalApplications}</td>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.type}</td>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.comission}</td>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.totalComission}</td>
                                <td className='text-xs leading-5 text-graySix p-4'>{list.totalStudentsPayment}</td>
                                <td className='text-xs leading-5 text-graySix p-4'>{list.paid}</td>
                                <td className='text-xs leading-5 text-graySix p-4'>{list.due}</td>
                                <td className='text-xs leading-5 text-graySix p-4'>{list.lastPaymentDate}</td>
                                <td className='text-xs leading-5 text-graySix p-4'>{list.nextPaymentDueDate}</td>
                                <td className={`text-xs leading-5 text-graySix p-4`}>
                                    <span className={`rounded-[16px] py-0.5 px-1.5 ${StatusColor(list.status)}`}>
                                        {list.status}
                                    </span>
                                </td>
                                <td className='text-xs leading-5 text-graySix p-4'>{list.remarks}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className='py-12'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default page