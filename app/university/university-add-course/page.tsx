'use client'

import Image from 'next/image';
import React, { useState } from 'react';

import Error from '@/app/assets/svg/error.svg';
import Heading from '@/app/components/Heading';
import Switcher from '@/app/components/Switcher';
import { Checkbox } from '@/components/ui/checkbox';
import FileUpload from '@/app/components/FileUpload';
import InputField from '@/app/components/InputField';
import { useForm, Controller } from 'react-hook-form';
import MultiSelect from '@/app/components/MultiSelect';
import PlusPrimary from '@/app/assets/svg/PlusPrimary';
import SelectField from '@/app/components/SelectField';
import Text<PERSON>reaField from '@/app/components/TextAreaField';
import AddNewFieldButton from '@/app/components/AddNewFieldButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import InputFieldWithCurrency from '@/app/components/InputFieldWithCurrency';
import SelectAndSearchCombobox from '@/app/components/SelectAndSearchCombobox';
import { 
    country, 
    province,
    frameworks,
    levelOfStudy,
} 
from '@/common';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';

const page = () => {
    const { control } = useForm<FormData>();
    const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
    const [proficiencyFields, setProficiencyFields] = useState<number[]>([0]);
    const [fee, setFee] = useState<string>('');
    const [selectedCurrency, setSelectedCurrency] = useState({
        code: 'USD',
        symbol: '$',
        label: 'USD ($)'
    });

    return (
        <DashboardLayout>
            <div className='py-5'>
                <Heading level='h1'>
                    Course Info
                </Heading>
            </div>
            <div className='pb-32'>
                <div className='rounded-[20px] p-[30px] bg-white'>
                    <h2 className='pb-3.5 font-medium text-base leading-none text-primaryColor'>Course Info</h2>
                    <hr />
                    <div className='space-y-[30px]'>
                        <div className='flex gap-6 mt-[30px]'>
                            <div className='w-[67%]'>
                                <InputField
                                    id='course-title'
                                    label='Course Title'
                                    type='text'
                                    placeholder='Course title'

                                />
                            </div>
                            <div className='w-[33%]'>
                                <MultiSelect
                                    options={country}
                                    selectedValues={selectedOptions}
                                    onChange={(values: any) => setSelectedOptions(values)}
                                    label='Country'
                                />
                            </div>
                        </div>
                        <div className='grid grid-cols-3 gap-6'>
                            <SelectField
                                label='Program'
                                placeholder='select program'
                                options={levelOfStudy}
                                value={'field.value'}
                                // onChange={field.onChange}
                                className=' '
                            />
                            <SelectField
                                label='Duration'
                                placeholder='select'
                                options={levelOfStudy}
                                value={'field.value'}
                                // onChange={field.onChange}
                                className=' '
                            />
                            <SelectField
                                label='Format'
                                placeholder='select'
                                options={levelOfStudy}
                                value={'field.value'}
                                // onChange={field.onChange}
                                className=' '
                            />
                        </div>
                        <div className='grid grid-cols-4 gap-6'>
                            <SelectField
                                label='Campuses'
                                placeholder='select campuses'
                                options={levelOfStudy}
                                value={'field.value'}
                                // onChange={field.onChange}
                                className=' '
                            />
                            <InputFieldWithCurrency
                                value={fee}
                                onChange={(value) => setFee(String(value))}
                                required
                                onCurrencyChange={setSelectedCurrency}
                                selectedCurrency={selectedCurrency}
                                label='Application Fees'
                            />
                            <InputFieldWithCurrency
                                value={fee}
                                onChange={(value) => setFee(String(value))}
                                required
                                onCurrencyChange={setSelectedCurrency}
                                selectedCurrency={selectedCurrency}
                                label='Tuition Fees'
                            />
                        </div>
                        <div className='grid grid-cols-3 gap-6'>
                            <InputField
                                id='last-academic'
                                label='Last Academic'
                                type='text'
                                // placeholder='Course title'
                            />
                            <InputField
                                id='Last Academic'
                                label='Minimum GPA'
                                type='text'
                                // placeholder='Course title'
                            />
                            <SelectAndSearchCombobox 
                                options={province} 
                                label='Lecture Language'
                                type='select'
                            />
                        </div>
                        <div className='grid grid-cols-2 gap-6'>
                            <InputField
                                id='course-rank'
                                label='Enter global rank'
                                type='text'
                                placeholder='Enter global rank'
                            />
                            <InputField
                                id='acceptance-rate'
                                label='Acceptance rate'
                                type='text'
                                placeholder='Enter acceptance rate'
                            />
                        </div>
                        <div>
                            <TextAreaField 
                                label='Entry Requirements'
                                placeholder='Enter entry requirements'
                            />
                            <div className='flex items-center space-x-2 py-1 mt-1.5'>
                                <Checkbox className='border-tertiary border-opacity-20 shadow-none' />
                                <label
                                    
                                    className='text-sm font-medium text-graySix'
                                >
                                    These requirements can vary slightly by program
                                </label>
                            </div>
                        </div>
                    </div>
                    <div className='mt-[30px]'>
                        <h2 className='pb-3.5 font-medium text-base leading-none text-primaryColor'>Standardized Test Requirements</h2>
                        <hr />
                        <div>
                            {proficiencyFields.map((index) => (
                                <div key={index} >
                                    <div  className='flex gap-6 pt-[30px]'>
                                        <div className='w-[30%]'>
                                            <SelectAndSearchCombobox
                                                options={frameworks}
                                                label='English Proficiency (01)'
                                                type='select'
                                                // field={field}
                                            />
                                            {/* <Controller
                                                // name='permanent_country'
                                                // control={control}
                                                render={({ field }) => (
                                                    <SelectAndSearchCombobox
                                                        options={frameworks}
                                                        label='English Proficiency (01)'
                                                        type='select'
                                                        // field={field}
                                                    />
                                                )}
                                            /> */}
                                        </div>
                                        <div className='w-[70%]'>
                                            <div>
                                                <InputField 
                                                    id='score'
                                                    // value={''}
                                                    // onChange={(e) => setName(e.target.value)}
                                                    placeholder='Overall 0.0' 
                                                    type='text' 
                                                    // errorMessage={'!name.trim() && error'}
                                                    label='Score' 
                                                />
                                                <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
                                                    <InputField 
                                                        id='reading'
                                                        // value={''}
                                                        // onChange={(e) => setName(e.target.value)}
                                                        placeholder='R 0.0' 
                                                        type='text' 
                                                        // errorMessage={'!name.trim() && error'}
                                                    />
                                                    <InputField 
                                                        id='writing'
                                                        // value={''}
                                                        // onChange={(e) => setName(e.target.value)}
                                                        placeholder='W 0.0' 
                                                        type='text' 
                                                        // errorMessage={'!name.trim() && error'}
                                                    />
                                                    <InputField 
                                                        id='listining'
                                                        // value={''}
                                                        // onChange={(e) => setName(e.target.value)}
                                                        placeholder='L 0.0' 
                                                        type='text' 
                                                        // errorMessage={'!name.trim() && error'}
                                                    />
                                                    <InputField 
                                                        id='speaking'
                                                        // value={''}
                                                        // onChange={(e) => setName(e.target.value)}
                                                        placeholder='S 0.0' 
                                                        type='text' 
                                                        // errorMessage={'!name.trim() && error'}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                            <AddNewFieldButton onClick={() => setProficiencyFields((prev) => [...prev, prev.length])} />
                        </div>
                    </div>
                    <div className='mt-[30px]'>
                        <div className='pb-3.5 flex items-center gap-5'>
                            <h2 className='font-medium text-base leading-none text-primaryColor'>Module Details</h2>
                            <Switcher />
                            <span className='font-normal text-sm leading-5 text-[#4B5563B2]'>More details</span>
                            <HoverCard>
                                <HoverCardTrigger>
                                    <Image width={16} height={16} src={Error} alt='Error icon' />
                                </HoverCardTrigger>
                            <HoverCardContent className='w-[290px] font-medium text-xs text-white -mt-11 ml-6 rounded-lg bg-grayFour' align='start'>
                                If you have a foreign degree, please specify the country where
                                you earned your degree in the dropdown below.
                            </HoverCardContent>
                            </HoverCard>
                        </div>
                        <hr />
                        <div className='flex gap-6 mt-[30px]'>
                            <div className='w-1/2'>
                                <InputField
                                    id='module-name-01'
                                    label='Module Name 01'
                                    type='text'
                                    placeholder='Enter module name'
                                />
                            </div>
                            <div className='w-1/2'>
                                <div className='grid grid-cols-2 gap-6'>
                                    <InputField
                                        id='module-code'
                                        label='Module Code'
                                        type='text'
                                        placeholder='Enter module code'
                                    />
                                    <InputField
                                        id='credit-hours'
                                        label='Credit Hours / Units'
                                        type='text'
                                    />
                                </div>
                            </div>
                        </div>
                        <AddNewFieldButton onClick={() => setProficiencyFields((prev) => [...prev, prev.length])} />
                    </div>
                    <div className='mt-[30px]'>
                        <div className='pb-3.5 flex gap-5 items-center'>
                            <h2 className='font-medium text-base leading-none text-primaryColor'>Brochure</h2>
                            <PlusPrimary />
                        </div>
                        <hr />
                        <div className='w-1/2 mt-[30px] space-y-[30px]'>
                            <div className='flex items-center'>
                                <span className='w-[20%] font-medium text-base leading-none text-grayFive'>Brochure 1</span>
                                <FileUpload />
                            </div>
                            <div className='flex items-center'>
                                <span className='w-[20%] font-medium text-base leading-none text-grayFive'>Brochure 1</span>
                                <FileUpload />
                            </div>
                        </div>
                    </div>
                </div>
                <div className='flex gap-[18px] mt-12 justify-end'>
                    <button className='font-semibold text-sm leading-5 text-tertiary rounded-[20px] py-2.5 px-4 border border-tertiary drop-shadow-[0_1px_2px_rgba(16,24,40,0.05)]'>Cancel</button>
                    <button className='font-semibold text-sm leading-5 text-primaryOne rounded-[20px] py-2.5 px-4 border border-tertiary bg-tertiary drop-shadow-[0_1px_2px_rgba(16,24,40,0.05)]'>Publish</button>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page