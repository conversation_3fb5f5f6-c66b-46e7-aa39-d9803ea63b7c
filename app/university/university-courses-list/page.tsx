'use client'

import React from 'react';
import Link from 'next/link';
import { programs } from '@/common';
import Paid from '@/app/assets/svg/Paid';
import Plus from '@/app/assets/svg/plus';
import Content from '@/app/assets/svg/Content';
import Heading from '@/app/components/Heading';
import Award_star from '@/app/assets/svg/Award_star';
import InputField from '@/app/components/InputField';
import SelectField from '@/app/components/SelectField';
import FilterLists from '@/app/components/FilterLists';
import Person_check from '@/app/assets/svg/Person_check';
import Local_library from '@/app/assets/svg/Local_library';
import Assignment_add from '@/app/assets/svg/Assignment_add';
import VerticalTabLayout from '@/app/components/VerticalTabLayout';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const popular = () => {
        return (
            <>
                <div className='py-5'>
                    <Heading level='h2'>
                        All (23)
                    </Heading>
                </div>
                <div className='grid grid-cols-4 gap-6'>
                    <div className='rounded-[12px] p-4 bg-white'>
                        <h3 className='font-semibold text-base leading-6 text-graySix'>Master of Cloud Computing Applications, Cloud Systems and Cloud Management</h3>

                        <div className='mt-4 space-y-2'>
                            <div className='flex gap-1.5 items-center'>
                                <Paid />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Tuition Fees:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>$37,823 - $50,369 USD</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Assignment_add />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Application Fee:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>$135 USD</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Award_star />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Course Rank:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>03</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Content />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Applicants Number:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>1,83,656</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Person_check />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Post Study Work Permit:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>03</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Local_library />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Program:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>2 year Master’s Degree</span>
                            </div>
                        </div>
                    </div>
                </div>
            </>
        )
    };

    const pending = () => {
        return (
            <>
                <div className='py-5'>
                    <Heading level='h2'>
                        All (23)
                    </Heading>
                </div>
                <div className='grid grid-cols-4 gap-6'>
                    <div className='rounded-[12px] p-4 bg-[#EFF0F0]'>
                        <h3 className='font-semibold text-base leading-6 text-graySix'>Master of Cloud Computing Applications, Cloud Systems and Cloud Management</h3>

                        <div className='mt-4 space-y-2'>
                            <div className='flex gap-1.5 items-center'>
                                <Paid />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Tuition Fees:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>$37,823 - $50,369 USD</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Assignment_add />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Application Fee:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>$135 USD</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Award_star />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Course Rank:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>03</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Content />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Applicants Number:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>1,83,656</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Person_check />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Post Study Work Permit:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>03</span>
                            </div>
                            <div className='flex gap-1.5 items-center'>
                                <Local_library />
                                <span className='font-normal text-sm leading-5 text-grayFive'>Program:</span>
                                <span className='font-medium text-sm leading-5 text-grayFive'>2 year Master’s Degree</span>
                            </div>
                        </div>
                    </div>
                </div>
            </>
        )
    };

    const tabContentsCourses = [
        {
            value: 'popular',
            label: 'Popular',
            content: popular
        },
        {
            value: 'pending',
            label: 'Pending',
            content: pending
        }
    ];

    const tabContents = tabContentsCourses.map((tab) => ({
        value: tab.value,
        children: <tab.content />
    }));

    const handleInputChange = (key: string, value: any) => {
        // onFilterChange((prev: any) => ({
        //   ...prev,
        //   [key]: value,
        // }));
    };
    const FiltersLists =['New York Institute of Technology - Vancouver...', 'Oct 2025', '11 weeks left', '2 days left'];

    return (
        <DashboardLayout>
            <div className='py-5 flex justify-between items-center'>
                <Heading level='h1'>
                    Courses
                </Heading>
                <Link 
                    href={'/university-add-course'} 
                    className='flex gap-1 items-center rounded-full py-2.5 px-3.5 bg-primaryColor drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)]'
                >
                    <Plus className='text-white w-3.5 h-3.5' />
                    <span className='font-semibold text-xs leading-none text-white'>Add Courses</span>
                </Link>
            </div>

            <div className='rounded-[16px] py-[30px] px-5 bg-white border border-[#1E62E033] drop-shadow-[0_2px_2px_rgba(30,98,224,0.1)]'>
                <div className='grid grid-cols-4 gap-3'>
                    <InputField
                        id='course'
                        label='Course'
                        type='text'
                        placeholder='Course Title'
                    />
                    <SelectField 
                        label='Program'
                        options={programs}
                        placeholder=''
                        onChange={(value) => handleInputChange('program', value)}
                    />
                    <SelectField 
                        label='Field of Study'
                        options={programs}
                        placeholder=''
                        onChange={(value) => handleInputChange('program', value)}
                    />
                    <SelectField 
                        label='Tuition Fees'
                        options={programs}
                        placeholder=''
                        onChange={(value) => handleInputChange('program', value)}
                    />
                    <SelectField 
                        label='Post Study Work Permit'
                        options={programs}
                        placeholder=''
                        onChange={(value) => handleInputChange('program', value)}
                    />
                    <SelectField 
                        label='Sort By'
                        options={programs}
                        placeholder=''
                        onChange={(value) => handleInputChange('program', value)}
                    />
                </div>
                <div className='mt-5'>
                    <FilterLists lists={FiltersLists} />
                </div>
            </div>

            <div className='mt-5'>
                <VerticalTabLayout 
                    tabMenu={tabContentsCourses}
                    tabContents={tabContents}
                    defaultValue='popular'
                    tabListClassName='!p-0 max-w-[1193px] md:!justify-start'
                    tabContentClassName = 'pb-[96px]'
                    tabTriggerClassName=''
                />
            </div>
        </DashboardLayout>
    )
}

export default page