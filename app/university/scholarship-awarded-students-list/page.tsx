"use client"

import canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import React, { useState, useEffect } from 'react';
import InputField from '@/app/components/InputField';
import Pagination from '@/app/components/Pagination';
import MultiSelect from '@/app/components/MultiSelect';
import FilterLists from '@/app/components/FilterLists';
import DropDownButton from '@/app/components/DropDownButton';
import TooltipCountry from '@/app/components/ui/TooltipCountry';
import { intakes, sortByOptions, country, courses } from '@/common';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const itemsPerPage = 2;
        const [currentPage, setCurrentPage] = useState(1);
        const totalPages = Math.ceil(courses.length / itemsPerPage);
    
        // const currentCources = courses.slice(
        //     (currentPage - 1) * itemsPerPage,
        //     currentPage * itemsPerPage
        // );
    
        const goToPage = (page: number) => {
            setCurrentPage(page);
        };
    const [filters, setFilters] = useState({
        search: '',
        intake: [] as string[],
        country: [] as string[],
        program: [] as string[],
        scholership: [] as string[],
        grantYear: [] as string[],
        sortBy: [] as string[]
    });

    // Initialize filterLists array to display active filters
    const [filterLists, setFilterLists] = useState<string[]>([]);
    console.log(filterLists)
    // Update filterLists whenever filters change
    useEffect(() => {
        // Create a new array instead of modifying the existing one
        const activeFilters: string[] = [];
        
        // Add selected values from each filter to the activeFilters array
        if (filters.search) activeFilters.push(filters.search);
        
        // Add array-based filters
        filters.intake.forEach(item => activeFilters.push(item));
        filters.country.forEach(item => activeFilters.push(item));
        filters.program.forEach(item => activeFilters.push(item));
        filters.scholership.forEach(item => activeFilters.push(item));
        filters.grantYear.forEach(item => activeFilters.push(item));
        filters.sortBy.forEach(item => activeFilters.push(item));
        
        // Only update state if the filters have actually changed
        // This prevents unnecessary re-renders and potential loops
        if (JSON.stringify(activeFilters) !== JSON.stringify(filterLists)) {
            setFilterLists(activeFilters);
        }
    }, [filters]); // Remove filterLists from the dependency array
    
    



    // Handle removing a filter
    const handleRemoveFilter = (filter: string) => {
        // Create a new state object
        const newFilters = { ...filters };
        
        // Check each filter array and remove the matching item
        Object.keys(newFilters).forEach(key => {
            const filterKey = key as keyof typeof filters;
            const value = newFilters[filterKey];
            
            if (Array.isArray(value)) {
                // If it's an array, filter out the matching item
                newFilters[filterKey] = value.filter(
                    item => item !== filter
                ) as any;
            } else if (typeof value === 'string' && value === filter) {
                // For string values like search
                newFilters[filterKey] = '' as any;
            }
        });
        
        setFilters(newFilters);
    };

    // Add handleInputChange function
    const handleInputChange = (key: string, value: any) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    // Add this function to handle clearing all filters
    const handleClearAllFilters = () => {
        setFilters({
            search: '',
            intake: [],
            country: [],
            program: [],
            scholership: [],
            grantYear: [],
            sortBy: []
        });
    };
    
     const tableHeadData = [
        'SID',
        'Name',
        'Nationality',
        'Program',
        'Scholership',
        'Grant Year',
        'Amount'
    ];

    const allStudentLists = [
        {
            sid: 262,
            name: 'Nolan Westervelt',
            nationality: canada,
            program: '2 year Undergraduate Diploma',
            scholership: 'Fulbright Foreign Student Program',
            grantYear: '2025',
            amount: '$22,000'
        },
        {
            sid: 262,
            name: 'Westervelt',
            nationality: canada,
            program: '2 year Undergraduate Diploma',
            scholership: 'Fulbright Foreign Student Program',
            grantYear: '2023',
            amount: '$22,000'
        },
        {
            sid: 262,
            name: 'Saiful Islam ',
            nationality: canada,
            program: '2 year Undergraduate Diploma',
            scholership: 'Fulbright Foreign Student Program',
            grantYear: '2025',
            amount: '$22,000'
        }

    ];

    const filteredStudents = allStudentLists.filter(student => {
        // Filter by search text (ID, name, or other searchable fields)
        if (filters.search && 
            !student.sid.toString().includes(filters.search) && 
            !student.name.toLowerCase().includes(filters.search.toLowerCase())) {
            return false;
        }
        
        // Filter by country
        if (filters.country.length > 0 && 
            !filters.country.some(country => 
                student.nationality.name && student.nationality.name.toLowerCase().includes(country.toLowerCase())
            )) {
            return false;
        }
        
        // Filter by program
        if (filters.program.length > 0 && 
            !filters.program.includes(student.program)) {
            return false;
        }
        
        // Filter by scholarship
        if (filters.scholership.length > 0 && 
            !filters.scholership.includes(student.scholership)) {
            return false;
        }
        
        // Filter by grant year
        if (filters.grantYear.length > 0 && 
            !filters.grantYear.includes(student.grantYear)) {
            return false;
        }
        
        // Filter by intake (if applicable)
        // if (filters.intake.length > 0 && 
        //     student.intake && 
        //     !filters.intake.includes(student.intake)) {
        //     return false;
        // }
        
        // If all filters pass, include this student
        return true;
    });

    return (
        <DashboardLayout>
            <div className='py-5'>
                <Heading level='h1'>
                    Scholarship Awarded Students List
                </Heading>
            </div>
            <div>
                <div className='grid grid-cols-6 gap-3'>
                    <div>
                        <InputField 
                            type='text'
                            id='id/name/mobile'
                            label='ID/Name/Mobile'
                            placeholder='+880 1843789043'
                            value={filters.search}
                            onChange={(e) => handleInputChange('search', e.target.value)}
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={country}
                            selectedValues={filters.country}
                            onChange={(value) => handleInputChange('country', value)}
                            label='Country'
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={intakes}
                            selectedValues={filters.program}
                            onChange={(value) => handleInputChange('program', value)}
                            label='Program'
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={intakes}
                            selectedValues={filters.scholership}
                            onChange={(value) => handleInputChange('scholership', value)}
                            label='Scholership'
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={intakes}
                            selectedValues={filters.grantYear}
                            onChange={(value) => handleInputChange('grantYear', value)}
                            label='Grant Year'
                        />
                    </div>
                    <div>
                        <MultiSelect
                            options={sortByOptions}
                            selectedValues={filters.sortBy}
                            onChange={(value) => handleInputChange('sortBy', value)}
                            label='Sort by'
                        />
                    </div>
                </div>
                {filterLists && (
                    <div className='mt-5'>
                        <FilterLists 
                            lists={filterLists} 
                            onRemove={handleRemoveFilter} 
                            onClearAll={handleClearAllFilters}
                        />
                    </div>
                )}
                {/* <div className='mt-5'>
                    <FilterLists 
                        lists={filterLists} 
                        onRemove={handleRemoveFilter} 
                        onClearAll={handleClearAllFilters}
                    />
                </div> */}
            </div>
            <div className='mt-10'>
                <div className='flex justify-between items-center mb-5'>
                    <Heading level='h2'>
                        All Students
                    </Heading>
                    <DropDownButton />
                </div>
                <div className='overflow-x-auto'>
                    <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                        <thead>
                            <tr>
                                {tableHeadData.map((thData, index) => (
                                    <th 
                                        key={index} 
                                        className='py-3.5 px-6 font-bold text-xs tracking-[0.4px] text-grayFive'
                                    >
                                        {thData}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                            {filteredStudents.map((student, index) => (
                                <tr key={index}>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-6 font-normal'>{student.sid}</td>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-6 font-normal'>{student.name}</td>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-11 font-normal'>
                                        <span className=''>
                                            {<student.nationality />}
                                        </span>
                                    </td>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-6 font-normal'>{student.program}</td>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-6 font-normal'>{student.scholership}</td>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-6 font-normal'>{student.grantYear}</td>
                                    <td className='text-xs leading-5 text-graySix py-3.5 px-6 font-normal'>{student.amount}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
            <div className='pb-24 pt-12'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default page
