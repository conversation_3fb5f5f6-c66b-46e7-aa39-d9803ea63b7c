'use client';

import { z } from 'zod';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { setUniversityId } from '@/lib/redux/slices/universitySlice';
import { useSelector } from 'react-redux';
import { DropdownOption } from '@/types';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
// import Upload from '@/app/assets/svg/upload';
import { RootState } from '@/lib/redux/store';
import Heading from '@/app/components/Heading';
// import Download from '@/app/assets/svg/download';
import React, { useEffect, useState } from 'react';
import InputField from '@/app/components/InputField';
import EmailInbox from '@/app/assets/svg/emailInbox';
import SelectField from '@/app/components/SelectField';
import NoteTextarea from '@/app/components/NoteTextarea';
import { Country, State, City } from 'country-state-city';
import ImageUploader from '@/app/components/ImageUploader';
import PhoneNumberInput from '@/app/components/PhoneNumberInput';
import InputFieldWithIcon from '@/app/components/InputFieldWithIcon';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import SelectAndSearchCombobox from '@/app/components/SelectAndSearchCombobox';
import { useAddUniversityMutation } from '@/lib/redux/api/addUniversityApi';

const universityFormSchema = z.object({
  name: z.string().min(1, { message: "University name is required" }),
  logo: z.string().min(1, { message: "University logo is required" }),
  address: z.string().min(1, { message: "Address is required" }),
  country: z.string().min(1, { message: "Country is required" }),
  state: z.string().min(1, { message: "State is required" }),
  city: z.string().min(1, { message: "City is required" }),
  postalCode: z.string().optional(),
  primaryContactNumber: z.string().optional(),
  email: z.string().email({ message: "Invalid email address" }),
  about: z.string().optional(),
  type: z.string().min(1, { message: "University type is required" }),
  website: z.string()
        .min(1, { message: "Website URL is required" })
        .refine(
            (url) => {
                try {
                    if (url.startsWith('http://') || url.startsWith('https://')) {
                        new URL(url);
                        return true;
                    }
                    new URL(`https://${url}`);
                    return true;
                } catch (e) {
                    return false;
                }
            },
        { message: "Invalid URL format" }
        ),
  foundedOn: z.string().optional(),
  institutionCode: z.string().optional(),
});

type UniversityFormValues = z.infer<typeof universityFormSchema>;

const Page = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    const user = useSelector((state: RootState) => state.auth.user);
    const [addUniversity, { isLoading }] = useAddUniversityMutation();
  
    useEffect(() => {
        if (user && user.roles.name !== 'SuperAdmin') {
            router.push('/access-restricted');
        }
    }, [user, router]);

    if (!user || user.roles.name !== 'SuperAdmin') {
        return null; 
    }
    
    const UniTypeSelectButton = [
        { value: 'public', label: 'Public' },
        { value: 'private', label: 'Private' },
        { value: 'non-profit', label: 'Non-Profit' }
    ]
    
    const [logo, setLogo] = useState<File | null>(null);
    console.log(logo)
    const [logoUrl, setLogoUrl] = useState<string>('');
    
    const { 
        control, 
        register, 
        handleSubmit, 
        watch, 
        setValue, 
        formState: { errors, isSubmitting } 
    } = useForm<UniversityFormValues>({
        resolver: zodResolver(universityFormSchema),
        defaultValues: {
            name: '',
            logo: '',
            address: '',
            country: '',
            state: '',
            city: '',
            postalCode: '',
            primaryContactNumber: '',
            email: '',
            about: '',
            type: '',
            website: '',
            foundedOn: '',
            institutionCode: '',
        }
    });

    const [states, setStates] = useState<DropdownOption[]>([]);
    const [cities, setCities] = useState<DropdownOption[]>([]);

    const countryOptions: DropdownOption[] = Country.getAllCountries().map((country) => ({
        label: country.name,
        value: country.isoCode,
    }));

    const watchCountry = watch('country');
    const watchState = watch('state');

    useEffect(() => {
        if (watchCountry) {
            const statesList = State.getStatesOfCountry(watchCountry).map(state => ({
                label: state.name,
                value: state.isoCode,
            }));
            setStates(statesList);
            setValue('state', '');
            setValue('city', '');
        }
    }, [watchCountry, setValue]);

    useEffect(() => {
        if (watchState && watchCountry) {
            const citiesList = City.getCitiesOfState(watchCountry, watchState).map(city => ({
                label: city.name,
                value: city.name,
            }));
            setCities(citiesList);
            setValue('city', '');
        }
    }, [watchState, watchCountry, setValue]);

    // Handle logo upload
    // useEffect(() => {
    //     if (logo) {
    //         // In a real app, you would upload the file to a server and get a URL
    //         // For now, we'll simulate this with a placeholder URL
    //         setLogoUrl('https://example.com/uploads/1.jpg');
    //         setValue('logo', 'https://example.com/uploads/1.jpg');
    //     }
    // }, [logo, setValue]);

    const onSubmit = async (data: UniversityFormValues) => {
        try {
            // Format the website URL if needed
            let websiteUrl = data.website;
            if (websiteUrl && !websiteUrl.startsWith('http://') && !websiteUrl.startsWith('https://')) {
                websiteUrl = `https://${websiteUrl}`;
            }

            // Prepare the data for API submission
            console.log("Complete form data:", data);
            const universityData = {
                name: data.name,
                about: data.about || '',
                type: data.type,
                website: websiteUrl,
                foundedOn: data.foundedOn,
                institutionCode: data.institutionCode || '',
                address: data.address,
                primaryContactNumber: data.primaryContactNumber,
                country: data.country,
                state: data.state,
                city: data.city,
                postalCode: data.postalCode || '',
                logo: data.logo,
                email: data.email
            };
            console.log("Formatted data for API:", universityData);

            // Call the API
            const response = await addUniversity(universityData).unwrap();
            console.log(response);

            // Store the university id in localStorage
            if (response?.data?.id) {
                dispatch(setUniversityId(response.data.id));
                // Optionally: localStorage.setItem('universityId', response.data.id);
            }

            // Redirect to university profile page
            router.push('/universities/add-university/university-profile');
        } catch (error: any) {
            console.error('Error adding university:', error);
        }
    };

    return (
        <DashboardLayout>
            <div className="flex justify-between pt-10 pb-5">
                <div className="flex items-center justify-between w-full">
                    <Heading level="h1">Add University</Heading>
                    {/* <div className="flex items-center gap-3.5">
                        <button className="py-2.5 px-[18px] bg-primaryColor text-white rounded-full flex items-center gap-1">
                            <Download />
                            Template
                        </button>
                        <button className="py-2.5 px-[18px] bg-primaryOne text-primaryColor border border-primaryColor/20 rounded-full flex items-center gap-1">
                            <Upload className="w-4 h-4" />
                            CSV
                        </button>
                    </div> */}
                </div>
            </div>
            <form onSubmit={handleSubmit(onSubmit)} className="pb-10">
                <div className="p-[30px] bg-white rounded-[20px] space-y-[30px]">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <InputField
                                id="name"
                                placeholder="Enter the university name"
                                type="text"
                                label="Name"
                                required
                                className="py-3"
                                errorMessage={errors.name?.message}
                                {...register('name')}
                            />
                        </div>
                        <div>
                            <Controller
                                name="logo"
                                control={control}
                                render={({ field }) => (
                                    <ImageUploader
                                        label="University Logo"
                                        multiple={false}
                                        value={logo}
                                        onChange={async (file) => {
                                            setLogo(file as File | null);
                                            if (file) {
                                                // Create a temporary URL for the logo
                                                const fileUrl = URL.createObjectURL(file as File);
                                                setLogoUrl(fileUrl);
                                                setValue('logo', fileUrl); // Set the logo URL in the form
                                            } else {
                                                setLogoUrl('');
                                                setValue('logo', ''); // Clear the logo URL if no file
                                            }
                                        }}
                                    />
                                )}
                            />
                            {errors.logo?.message && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors.logo.message}
                                </p>
                            )}
                        </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="max-h-[137px]">
                            <Controller
                                name="address"
                                control={control}
                                render={({ field }) => (
                                    <NoteTextarea
                                        label="Main Campus Address*"
                                        placeholder="Address"
                                        value={field.value}
                                        onChange={(e) => field.onChange(e.target.value)}
                                    />
                                )}
                            />
                            {errors.address?.message && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors.address.message}
                                </p>
                            )}
                        </div>

                        <div className="grid grid-cols-2 gap-x-6 gap-y-[30px]">
                            <Controller
                                name="country"
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        label="Country"
                                        placeholder='Select a country'
                                        options={countryOptions}
                                        className="py-3"
                                        buttonClassName="border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree"
                                        selectedValue={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                            {errors.country?.message && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors.country.message}
                                </p>
                            )}
                            <Controller
                                name="state"
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        label="State"
                                        placeholder='Select a state'
                                        options={states}
                                        className="py-3"
                                        buttonClassName="border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree"
                                        selectedValue={field.value}
                                        onChange={field.onChange}
                                        disabled={!watchCountry}
                                    />
                                )}
                            />
                            {errors.state?.message && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors.state.message}
                                </p>
                            )}
                            <Controller
                                name="city"
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        label="City"
                                        placeholder='Select a city'
                                        options={cities}
                                        className="py-3"
                                        buttonClassName="border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree"
                                        selectedValue={field.value}
                                        onChange={field.onChange}
                                        disabled={!watchState}
                                    />
                                )}
                            />
                            {errors.city?.message && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors.city.message}
                                </p>
                            )}
                            <InputField
                                id="postalCode"
                                placeholder="Enter the Postal/Zip code"
                                type="text"
                                label="Postal/Zip Code"
                                className="py-3"
                                {...register('postalCode')}
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-2 gap-6">
                        <Controller
                            name="primaryContactNumber"
                            control={control}
                            render={({ field }) => (
                                <PhoneNumberInput
                                    label="Phone Number"
                                    value={field.value}
                                    onChange={field.onChange}
                                    className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                                />
                            )}
                        />
                        {/* <Controller
                            name="email"
                            control={control}
                            render={({ field }) => (
                               
                            )}
                        /> */}
                         <InputFieldWithIcon
                            label="Email"
                            placeholder="Email"
                            icon={<EmailInbox />}
                            type="email"
                            className="mt-0.5"
                            // value={field.value}
                            {...register('email')}
                            // onChange={field.onChange}
                            error={errors.email?.message}
                        />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="max-h-[137px]">
                            <Controller
                                name="about"
                                control={control}
                                render={({ field }) => (
                                    <NoteTextarea
                                        label="About"
                                        placeholder="About"
                                        value={field.value}
                                        onChange={(e) => field.onChange(e.target.value)}
                                    />
                                )}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-6">
                            <Controller
                                name="type"
                                control={control}
                                render={({ field }) => (
                                    <SelectField
                                        label="Type"
                                        options={UniTypeSelectButton}
                                        className="py-6"
                                        value={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                            {errors.type?.message && (
                                <p className="text-red-500 text-sm mt-1">
                                    {errors.type.message}
                                </p>
                            )}
                            <InputField
                                id="website"
                                placeholder="Website link paste here"
                                type="text"
                                label="Website URL"
                                required
                                className="py-3"
                                {...register('website')}
                                errorMessage={errors.website?.message}
                            />
                            <InputField
                                id="foundedOn"
                                type="text"
                                label="Founded On"
                                className="py-3"
                                {...register('foundedOn')}
                            />
                            <InputField
                                id="dliNumber"
                                type="text"
                                label="Institution Code"
                                className="py-3"
                                placeholder="Enter institution code"
                                {...register('institutionCode')}
                            />
                        </div>
                    </div>
                </div>
                <div className="flex justify-end w-full">
                    <button 
                        type="submit" 
                        className="py-2.5 px-[18px] bg-primaryColor text-white rounded-full mt-12"
                        disabled={isLoading || isSubmitting }
                    >
                        {isLoading  ? 'Submitting...' : 'Submit'}
                    </button>
                </div>
            </form>
        </DashboardLayout>
    );
};

export default Page;
