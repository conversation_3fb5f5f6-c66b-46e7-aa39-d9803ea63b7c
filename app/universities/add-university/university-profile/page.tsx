'use client';

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { RootState } from '@/lib/redux/store';
import Heading from '@/app/components/Heading';
import { Button } from '@/components/ui/button';
import Location from '@/app/assets/svg/Location';
import IntakeIcon from '@/app/assets/svg/IntakeIcon';
import EditAction from '@/app/assets/svg/EditAction';
import ProgramIcon from '@/app/assets/svg/ProgramIcon';
import CommonSheet from '@/app/components/CommonSheet';
import PaymentIcon from '@/app/assets/svg/PaymentIcon';
import Payment from '@/app/components/ui/form/Payment';
import IntakeForm from '@/app/components/ui/form/IntakeForm';
import CommissionIcon from '@/app/assets/svg/CommissionIcon';
import NationalityIcon from '@/app/assets/svg/NationalityIcon';
import ProgramForm from '@/app/components/ui/form/ProgramForm';
import StandardizedIcon from '@/app/assets/svg/StandardizedIcon';
import FieldOfStudy from '@/app/components/ui/form/FieldOfStudy';
import CommissionForm from '@/app/components/ui/form/CommissionForm';
import { CircularProgress } from '@/app/components/CircularProgress';
import ContactDetailsIcon from '@/app/assets/svg/ContactDetailsIcon';
import ContactDetails from '@/app/components/ui/form/ContactDetails';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import AccordionWithButton from '@/app/components/AccordionWithButton';
import TheUniversityOfChicago from '@/app/assets/svg/TheUniversityOfChicago';
import EligibleNationalities from '@/app/components/ui/form/EligibleNationalities';
import UniEnrollementFeesForm from '@/app/components/ui/form/UniEnrollementFeesForm';
import UniEnrollmentCourseForm from '@/app/components/ui/form/UniEnrollmentCourseForm';
import UniEnrollmentAlumniForm from '@/app/components/ui/form/UniEnrollmentAlumniForm';
import UniEnrollmentGeneralForm from '@/app/components/ui/form/UniEnrollmentGeneralForm';
import UniEnrollementCampusForm from '@/app/components/ui/form/UniEnrollementCampusForm';
import UniEnrollementConfigurebyCourse from '@/app/components/ui/form/UniEnrollementConfigurebyCourse';
import { useGetUniversityByIdQuery } from '@/lib/redux/api/addUniversityApi';

const sections = [
    { 
        name: 'Intake', 
        icon: <IntakeIcon />,
        content: <IntakeForm />
    },
    { 
        name: 'Program', 
        icon: <ProgramIcon />,
        content: <ProgramForm  />
    },
    { 
        name: 'Field of Study', 
        icon: <StandardizedIcon />,
        content: <FieldOfStudy />
    },
    { 
        name: 'Eligible Nationalities', 
        icon: <NationalityIcon />,
        content: <EligibleNationalities />
    },
    { 
        name: 'Commission', 
        icon: <CommissionIcon />,
        content: <CommissionForm />
    },
    { 
        name: 'Payment Settings', 
        icon: <PaymentIcon />,
        content: <Payment  />
    },
    { 
        name: 'Contact Details', 
        icon: <ContactDetailsIcon />,
        content: <ContactDetails  />
    }
];

const accordions = [
    // {
    //     name: 'General',
    //     component: <UniEnrollmentGeneralForm />,
    // },
    // {
    //     name: 'Fees',
    //     component: <UniEnrollementFeesForm />
    // },
    // {
    //     name: 'Course',
    //     component: <UniEnrollmentCourseForm />
    // },
    // {
    //     name: 'Campus',
    //     component: <UniEnrollementCampusForm />
    // },
    // {
    //     name: 'Configure Campus by Course',
    //     component: <UniEnrollementConfigurebyCourse />
    // },
    // {
    //     name: 'Alumni',
    //     component: <UniEnrollmentAlumniForm />
    // },
]

const page = () => {
    const router = useRouter();
    const user = useSelector((state: RootState) => state.auth.user);
    const universityId = useSelector((state: RootState) => state.university.universityId)
    console.log(universityId);
    console.log(user)
    // const [universityId, setUniversityId] = useState<string | null>(null);

    // useEffect(() => {
    //     const id = localStorage.getItem('universityId');
    //     setUniversityId(id);
    // }, []);

    const { data, error, isLoading } = useGetUniversityByIdQuery(universityId ?? '', {
        skip: !universityId,
    });

    useEffect(() => {
        if (user && user.roles.name !== 'SuperAdmin') {
            router.push('/access-restricted');
        }
    }, [user, router]);

    if (!user || user.roles.name !== 'SuperAdmin') {
        return null; 
    }
    
    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error loading university data.</div>;
    
    return (
        <DashboardLayout>
            <div className='pb-24 overflow-hidden'>
                <Heading level='h1'>
                    University Information
                </Heading>
                <div className='rounded-[16px] border-[0.5px] border-grayOne p-8 bg-white mt-5'>
                    <Heading className='font-semibold text-xl leading-none text-graySix mb-[30px]' level='h2'>
                        UID # {data?.id}
                    </Heading>
                    <div className='flex gap-3 items-center text-primaryColor border-b pb-3.5 border-primaryColor/20'>
                        General Info <Button className='px-2 py-1.5 border border-primaryColor/10 rounded-full text-xs gap-1'><EditAction /> Edit</Button>
                    </div>
                    <div className='flex gap-5 w-full mt-[30px]'>
                        <div>
                            {data?.logo && (
                                <img
                                    src={data.logo}
                                    alt={data.name}
                                    style={{ width: 100, height: 100, objectFit: 'contain', borderRadius: 8 }}
                                />
                            )}
                        </div>
                        <div className='flex flex-col gap-5 w-full'>
                            <div className='space-y-1.5'>
                                <Heading level='h3'>
                                    {data?.name}
                                </Heading>
                                <span className='text-sm text-grayFive inline-flex items-center gap-1.5'>
                                    <Location className='w-4 h-4' />
                                    {data?.address + ', ' + data?.city + ', ' + data?.state + ', ' + data?.country + ' ' + data?.postalCode}
                                </span>
                            </div>
                            <div className='grid grid-cols-4 gap-[30px]'>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Website</span>
                                    <span className='font-semibold text-sm leading-none underline text-primaryColor'>{data?.website}</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Email</span>
                                    <span className='font-semibold text-sm leading-none underline text-primaryColor'>{data?.email}</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Join Date</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>{data?.createdAt && new Date(data.createdAt).toLocaleDateString()}</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Type</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>{data?.type}</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Primary Contact Number</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>{data?.primaryContactNumber}</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Founded On</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>{data?.foundedOn && new Date(data.foundedOn).getFullYear()}</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Institution Code</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>{data?.institutionCode}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* {accordions?.map((content, index) => (
                    <div key={index} className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                        <AccordionWithButton
                            label={content.name}
                            labelClassName='text-2xl font-medium'
                        >
                            {content.component}
                        </AccordionWithButton>
                    </div>
                ))} */}
                <div className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                    <AccordionWithButton
                        label='Configure Advanced Setup'
                        labelClassName='text-2xl font-medium'
                        subLabel='Once you have set up these sections, you will be able to create an advanced university profile.'
                    >
                        <div className='grid grid-cols-4 gap-6'>
                            <div className='row-span-3 flex flex-col justify-center items-center text-[18px] gap-6'>
                                <CircularProgress
                                    value={40}
                                    size={120}
                                    strokeWidth={12}
                                    animationDuration={1500}
                                />
                                <p className='text-grayFive'>Overall Progress</p>
                            </div>
                            {sections.map(( item, index ) => (
                                <CommonSheet
                                    key={index}
                                    label={item.name}
                                    Icon={item.icon}
                                    title={item.name}
                                >
                                    {item.content}
                                </CommonSheet>
                            ))}

                        </div>
                    </AccordionWithButton>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page