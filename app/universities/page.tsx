'use client';

import Link from 'next/link';
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import Heading from '@/app/components/Heading';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { useGetUniversitiesQuery } from '@/lib/redux/api/addUniversityApi';

const Page = () => {
    const router = useRouter();
    const user = useSelector((state: RootState) => state.auth.user);
    const { data: universities, isLoading, error } = useGetUniversitiesQuery();
    console.log(universities)
    useEffect(() => {
        if (user && user.roles.name !== 'SuperAdmin') {
            router.push('/access-restricted');
        }
    }, [user, router]);

    // If user is not yet loaded or not a SuperAdmin, don't render the content
    if (!user || user.roles.name !== 'SuperAdmin') {
        return null;
    }

    return (
        <DashboardLayout>
            <div className='flex justify-between items-center'>
                <Heading level='h1'>
                    Universities
                </Heading>
                <Link 
                    href={'/universities/add-university'}
                    className='bg-primaryColor py-2.5 px-5 rounded-full font-semibold text-white text-sm leading-none' 
                >
                    Add
                </Link>
            </div>
        </DashboardLayout>
    )
}

export default Page;
