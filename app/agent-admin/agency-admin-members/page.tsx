'use client'

import React, { useState } from 'react';
import Trash from '@/app/assets/svg/trash';
import Menu from '@/app/assets/svg/menu';
import Plus from '@/app/assets/svg/plus';
import Grid from '@/app/assets/svg/grid';
import Check from '@/app/assets/svg/check';
import Search from '@/app/assets/svg/search';
import { Button } from '@/components/ui/button';
import EditAction from '@/app/assets/svg/EditAction';
import InputWithIcon from '@/app/components/InputWithIcon';
import DropDownButton from '@/app/components/DropDownButton';
import DateRangeSelect from '@/app/components/DateRangeSelect';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { CounselorBarChartData, employeeList, invitedMembers } from '@/common';
import { 
    Tabs, 
    TabsContent, 
    TabsList, 
    TabsTrigger 
} from '@/components/ui/tabs';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
  } from "@/components/ui/dialog"

const page = () => {
    const [layout, setLayout] = useState(true)
    const options = ['This Year', 'This Month', 'This Week', 'Today'];
    // const [selectedRange, setSelectedRange] = useState<keyof typeof CounselorBarChartData>('This Year');

    const statusColor = (value:string) => {
        if(value === 'On Leave') {
            return 'bg-[#F7E1C140] text-[#F2A735]'
        }else if(value === 'Inactive') {
            return 'bg-[#FF3B300F] text-[#FF3B30]'
        }else {
            return 'bg-[#ECFDF3] text-[#027A48]'
        }
    }

    const tableHeadData = [
        'ID',
        'Name',
        'Designation',
        'Email',
        'Mobile',
        'Joining Date',
        'Status',
        'Action'
    ];

    const rolePermissionsTableHeadData = [
        'Permissions',
        'Admin',
        'HR',
        'Accounts',
        'BDO',
        'App. Manager',
        'App. Officer',
        'Counselor',
        'Student'
    ];

    const invitedTableHeadData = [
        'Name',
        'Designation',
        'Email',
        'Action'
    ];

    const tabData = [
        { 
            label: 'Members', 
            value: 'members'
        },
        { 
            label: 'Roles/Permissions', 
            value: 'roles-permissions'
        },
        { 
            label: 'Invite', 
            value: 'invite'
        }
    ];

    return (
        <DashboardLayout>
            <Tabs defaultValue='Members' className='w-full mt-10'>
                <TabsList className=' bg-primaryOne flex flex-col md:flex-row gap-2.5 md:items-center items-start md:justify-between justify-start md:mb-0 mb-28 pb-6  rounded-none border-b px-0'>
                    <div className='flex gap-14 place-items-start text-grayFive'>
                        {tabData.map((item, index) => (
                            <TabsTrigger
                                key={index}
                                value={item.label}
                                className='py-4 data-[state=active]:text-primaryColor data-[state=active]:shadow-none data-[state=active]:rounded-none rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 -mb-1 border-primaryColor px-0'
                            >
                                {item.label}
                            </TabsTrigger>
                        ))}
                    </div>
                </TabsList>
                <TabsContent value='Members'>
                    <div className='flex justify-between mt-10 items-center'>
                        <InputWithIcon 
                            icon={Search} 
                            placeholder='search by id, name, mobile, email' 
                            className='w-[300px] rounded-[50px] py-2.5 border-[0.5px] bg-white border-grayTwo' 
                        />
                        <div className='flex items-center gap-5 md:mt-0 mt-5'>
                            <DropDownButton />
                            <Dialog>
                                <DialogTrigger asChild>
                                    <button className='flex rounded-[50px] items-center gap-1.5 bg-primaryColor py-2 px-3 font-semibold text-sm text-white'>
                                        <Plus className='text-white w-4 h-4' />
                                        Add member
                                    </button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-[425px]">
                                    <DialogHeader>
                                        <DialogTitle>Create Member</DialogTitle>
                                        {/* <DialogDescription>
                                            Make changes to your profile here. Click save when you're done.
                                        </DialogDescription> */}
                                    </DialogHeader>
                                    <div className="grid gap-4 py-8">
                                        fdsfsdfs
                                    </div>
                                    <DialogFooter className='flex gap-3'>
                                        <button className='py-2 px-5 font-semibold text-base text-grayFive'>Cancel</button>
                                        <button className='rounded-[50px] bg-primaryColor py-2 px-5 font-semibold text-base text-white'>
                                            Save
                                        </button>
                                    </DialogFooter>
                                </DialogContent>
                            </Dialog>
                        </div>
                    </div>
                    <div className='mt-5'>
                        <div className='overflow-x-auto'>
                            <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                                <thead>
                                    <tr>
                                        <th className='p-4'>
                                            <input 
                                                type='checkbox' 
                                                className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                            />
                                        </th>
                                        {tableHeadData.map((thData, index) => (
                                            <th 
                                                key={index} 
                                                className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                            >
                                                {thData}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                    {employeeList.map((employee, index) => (
                                        <tr key={index}>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>
                                                <input 
                                                    type='checkbox' 
                                                    className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                                />
                                            </td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{employee.eid}</td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{employee.name}</td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{employee.designation}</td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{employee.email}</td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{employee.mobile}</td>
                                            <td className='text-xs leading-5 text-graySix p-4'>{employee.joinDate}</td>
                                            <td className={``}>
                                                <span className={`rounded-[16px] font-medium text-xs ${statusColor(employee.status)} leading-4 py-[2px] px-2`}>{employee.status}</span>
                                            </td>
                                            <td className='text-xs leading-5 text-graySix p-4 flex gap-2.5 items-center'>
                                                <EditAction />
                                                <Trash />
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </TabsContent>
                <TabsContent value='Roles/Permissions'>
                    <div className='flex justify-between my-10 items-center'>
                        <InputWithIcon 
                            icon={Search} 
                            placeholder='search by id, name, mobile, email' 
                            className='w-[300px] rounded-[50px] py-2.5 border-[0.5px] bg-white border-grayTwo' 
                        />
                        <div className='flex items-center gap-5 md:mt-0 mt-5'>
                            <DropDownButton />
                        </div>
                    </div>
                    <div className='overflow-x-auto'>
                        <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                            <thead>
                                <tr>
                                    {rolePermissionsTableHeadData.map((thData, index) => (
                                        <th 
                                            key={index} 
                                            className='w-[140px] p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                        >
                                            {thData}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                <tr>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        Permissions 1
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                    <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                        <input 
                                            type='checkbox' 
                                            className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </TabsContent>
                <TabsContent value='Invite' className='py-5'>
                    <div className='overflow-x-auto'>
                        <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                            <thead>
                                <tr>
                                    {invitedTableHeadData.map((thData, index) => (
                                        <th 
                                            key={index} 
                                            className='w-[140px] p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                        >
                                            {thData}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                
                                    {invitedMembers.map((member, index) => (
                                        <tr key={index}>
                                            <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                                {member.name}
                                            </td>
                                            <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                                {member.Designation}
                                            </td>
                                            <td className='w-[140px] text-xs leading-5 text-graySix p-4 font-normal'>
                                                {member.Email}
                                            </td>
                                            <td className='text-xs leading-5 text-graySix p-4 flex gap-2.5 items-center'>
                                                <EditAction />
                                                <Trash />
                                            </td>
                                        </tr>
                                    ))}
                            </tbody>
                        </table>
                    </div>
                </TabsContent>
                </Tabs>
        </DashboardLayout>
    )
}

export default page