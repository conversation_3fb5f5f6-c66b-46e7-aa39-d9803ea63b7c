import React from 'react';
import Image from 'next/image';
import Ficc from '@/app/assets/svg/Ficc';
import Call from '@/app/assets/svg/Call';
import { topUniversitiesList } from '@/common';
import UkLarge from '@/app/assets/svg/UkLarge';
import UsaLarge from '@/app/assets/svg/UsaLarge';
import AusLarge from '@/app/assets/svg/AusLarge';
import EmailInbox from '@/app/assets/svg/emailInbox';
import StarRating from '@/app/components/StarRating';
import CanadaLarge from '@/app/assets/svg/CanadaLarge';
import ProgressBar from '@/app/components/ProgressBar';
import FinlandLarge from '@/app/assets/svg/FinlandLarge';
import GermanyLarge from '@/app/assets/svg/GermanyLarge';
import StatCardLayout from '@/app/components/StatCardLayout';
import AboutUniversity from '@/app/assets/svg/AboutUniversity';
import TooltipCountry from '@/app/components/ui/TooltipCountry';
import KeyAccountManager from '@/app/assets/svg/KeyAccountManager';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import UniversityProfileCard from '@/app/components/UniversityProfileCard';
import DescriptionsWithSeeMore from '@/app/components/DescriptionsWithSeeMore';
import AvatarAccountManager from '@/app/assets/img/AvatarKeyAccountManager.png';

const page = () => {
    const tableHeadData = [
        'UID',
        'University',
        'Location',
        'Country',
        'University Type'
    ];

    return (
        <DashboardLayout>
            <StatCardLayout className='p-8 mt-5 drop-shadow-[0_1px_2px_rgba(0,0,0,0.15)] mb-[48px]'>
                <p className='font-semibold text-lg leading-6 text-graySix'>AID # 567</p>

                <div className='flex gap-5 mt-[30px]'>
                    <div>
                        <Ficc />
                    </div>
                    <div className='flex flex-col gap-3.5'>
                        <div>
                            <h2 className='font-semibold text-[28px] leading-[42px] text-graySix'>Farees International Career Counseling (FICC)</h2>
                        </div>
                        <div className='flex gap-20'>
                            <div>
                                <span className='font-normal text-sm leading-4 text-grayFour'>Website</span>
                                <p className='mt-1.5 font-semibold text-sm underline  text-primaryColor'>ficc.com.bd</p>
                            </div>
                            <div>
                                <span className='font-normal text-sm leading-4 text-grayFour'>Email</span>
                                <p className='mt-1.5 font-semibold text-sm underline  text-primaryColor'><EMAIL></p>
                            </div>
                            <div>
                                <span className='font-normal text-sm leading-4 text-grayFour'>Location</span>
                                <p className='mt-1.5 font-semibold text-sm leading-6 text-grayFour'>9th floor, 10/2, Gawsia Kashem Center, Motijheel, Dhaka.</p>
                            </div>
                            <div>
                                <span className='font-normal text-sm leading-4 text-grayFour'>Join Date</span>
                                <p className='mt-1.5 font-semibold text-sm leading-6 text-grayFour'>22 Oct 2012</p>
                            </div>
                            <div>
                                <span className='font-normal text-sm leading-4 text-grayFour'>Agency Size</span>
                                <p className='mt-1.5 font-semibold text-sm leading-6 text-grayFour'>150-200 Employees</p>
                            </div>
                            <div>
                                <span className='font-normal text-sm leading-4 text-grayFour'>Membership Status</span>
                                <p className='mt-1.5 font-semibold text-sm leading-6 text-grayFour'>Gold</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='flex gap-6 mt-[30px]'>
                    <div className='w-[80%]'>
                        <UniversityProfileCard 
                            heading='About'
                            icon={<AboutUniversity />}
                        >
                            <DescriptionsWithSeeMore 
                                className='text-base leading-8'
                                description='Farees International Career Counseling (FICC) is a leading center in Bangladesh that provides student-friendly support to study abroad. Since its inception, it has accomplished remarkable achievement and progress. Our strong commitment and continuous devotion have provided highly professional service which in turn gained impressive good will and trust from our clients. Since its inception, it has accomplished remarkable achievement and progress.Since its inception, it has accomplished remarkable achievement and progress.' 
                            />
                        </UniversityProfileCard>
                    </div>
                    <div className='w-[20%]'>
                        <UniversityProfileCard 
                            heading='Key Account Manager'
                            icon={<KeyAccountManager />}
                        >
                            <div className='space-y-3'>
                                <div className='flex gap-4 items-center'>
                                    <Image
                                        src={AvatarAccountManager}
                                        alt='Key Account Manager'
                                        width={40}
                                        height={40}
                                    />
                                    <span className='font-semibold text-lg leading-7 text-grayFive'>Jane Rafayel</span>
                                </div>
                                <div className='flex gap-3 items-center'>
                                    <EmailInbox />
                                    <span className='font-normal text-base leading-5 text-primaryColor'><EMAIL></span>
                                </div>
                                <div className='flex gap-3 items-center'>
                                    <Call />
                                    <span className='font-normal text-base leading-5 text-primaryColor'>+8801952-654654</span>
                                </div>
                            </div>
                        </UniversityProfileCard>
                    </div>
                </div>
                <div className='mt-[30px] flex gap-6'>
                    <div className='w-1/2'>
                        <UniversityProfileCard 
                            heading='Current Applications Status'
                            icon={<AboutUniversity/>}
                        >
                            <div className='grid grid-cols-3 gap-6'>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>In Progress</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>234</p>
                                </StatCardLayout>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Accepted</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>123</p>
                                </StatCardLayout>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Rejected</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>56</p>
                                </StatCardLayout>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Awaiting Documents</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>49</p>
                                </StatCardLayout>
                            </div>
                        </UniversityProfileCard>
                    </div>
                    <div className='w-1/2'>
                        <UniversityProfileCard 
                            heading='Application Metrics'
                            icon={<AboutUniversity/>}
                        >
                            <div className='grid grid-cols-3 gap-6'>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Total Applications Processed</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>1.3k</p>
                                </StatCardLayout>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Applications Success</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>80%</p>
                                </StatCardLayout>
                                <StatCardLayout className='p-4'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Avg. Processing Time (in days)</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>25</p>
                                </StatCardLayout>
                                <StatCardLayout className='p-5'>
                                    <span className='font-medium text-sm leading-6 text-grayThree'>Lead Conversation</span>
                                    <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>76% </p>
                                </StatCardLayout>
                            </div>
                        </UniversityProfileCard>
                    </div>
                </div>
                <div className='mt-[30px]'>
                    <UniversityProfileCard 
                        heading='Commissions'
                        icon={<AboutUniversity/>}
                    >
                        <div className='grid grid-cols-4 gap-6'>
                            <StatCardLayout className='p-5'>
                                <span className='font-medium text-sm leading-6 text-grayThree'>Commission Percentage</span>
                                <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>10.0%</p>
                            </StatCardLayout>
                            <StatCardLayout className='p-5'>
                                <span className='font-medium text-sm leading-6 text-grayThree'>Total Commission</span>
                                <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>$68,930.54</p>
                            </StatCardLayout>
                            <StatCardLayout className='p-5'>
                                <span className='font-medium text-sm leading-6 text-grayThree'>Paid</span>
                                <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>$45,567.12</p>
                            </StatCardLayout>
                            <StatCardLayout className='p-5'>
                                <span className='font-medium text-sm leading-6 text-grayThree'>Due</span>
                                <p className='mt-1.5 font-bold text-[28px] leading-8 text-primaryColor'>$23,363.42</p>
                            </StatCardLayout>
                        </div>
                    </UniversityProfileCard>
                </div>
                <div className='grid grid-cols-3 gap-6 mt-[30px]'>
                    <UniversityProfileCard 
                        heading='Top Destination Countries'
                        icon={<AboutUniversity/>}
                    >
                        <div className='grid grid-cols-4 gap-6'>
                            <StatCardLayout className='p-5 flex justify-center'>
                                <CanadaLarge />
                            </StatCardLayout>
                            <StatCardLayout className='p-5 flex justify-center'>
                                <UsaLarge />
                            </StatCardLayout>
                            <StatCardLayout className='p-5 flex justify-center'>
                                <AusLarge />
                            </StatCardLayout>
                            <StatCardLayout className='p-5 flex justify-center'>
                                <UkLarge />
                            </StatCardLayout>
                            <StatCardLayout className='p-5 flex justify-center'>
                                <FinlandLarge />
                            </StatCardLayout>
                            <StatCardLayout className='p-5 flex justify-center'>
                                <GermanyLarge />
                            </StatCardLayout>
                        </div>
                    </UniversityProfileCard>
                    <UniversityProfileCard 
                        heading='Top Programs'
                        icon={<AboutUniversity/>}
                    >
                        <div>
                            <ul className='list-none font-normal text-base leading-5 text-graySix space-y-[18px]'>
                                <li>2 year Undergraduate Diploma</li>
                                <li>2 year Master Degree</li>
                                <li>4 year Bachelor's Degree</li>
                                <li>2 year Master’s Degree</li>
                            </ul>
                        </div>
                    </UniversityProfileCard>
                    <UniversityProfileCard 
                        heading='Student Feedback Score'
                        icon={<AboutUniversity/>}
                    >
                        <div className='flex justify-between'>
                            <div className='w-1/2'>
                                <p className='mb-3.5 font-normal text-base leading-5 text-grayFive'>Overall Rating</p>
                                <span className='font-bold text-[28px] leading-8 text-graySix'>4.5</span>
                                <div className='mt-1.5'>
                                    <StarRating rating={3.5} />
                                </div>
                                <div className='mt-3.5'>
                                    <p className='font-normal text-xs leading-4 text-grayThree'>Based on 148 reviews</p>
                                </div>
                            </div>
                            <div className='w-1/2'>
                                <div className='space-y-2.5'>
                                    <ProgressBar progressValue={100} star={5} />
                                    <ProgressBar progressValue={70} star={4} />
                                    <ProgressBar progressValue={50} star={3} />
                                    <ProgressBar progressValue={30} star={2} />
                                    <ProgressBar progressValue={40} star={1} />
                                </div>
                            </div>
                        </div>
                    </UniversityProfileCard>
                </div>
                <div className='mt-[30px]'>
                    <UniversityProfileCard 
                        heading='Top Universities'
                        icon={<AboutUniversity/>}
                    >
                        <div className='overflow-x-auto'>
                            <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                                <thead>
                                    <tr>
                                        {tableHeadData.map((thead, index) => (
                                            <th 
                                                key={index} 
                                                className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                            >
                                                {thead}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                    {topUniversitiesList.map((university) => (
                                        <tr key={university.id}>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.id}</td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal flex gap-2.5'>
                                                <Image 
                                                    src={university.universityLogo}
                                                    alt='University logo'
                                                />
                                                {university.universityName}
                                            </td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.location}</td>
                                            <td className='text-xs leading-5 text-graySix p-4 flex items-center gap-2 font-normal'>
                                                <TooltipCountry 
                                                    logo={<university.country.logo />} 
                                                    label={university.country.label} 
                                                />
                                            </td>
                                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.type}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </UniversityProfileCard>
                </div>
            </StatCardLayout>
        </DashboardLayout>
    )
}

export default page