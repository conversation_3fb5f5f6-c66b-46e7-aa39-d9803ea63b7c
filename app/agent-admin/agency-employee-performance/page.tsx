import React from 'react';
import Search from '@/app/assets/svg/search';
import { employeePerformanceList } from '@/common';
import InputWithIcon from '@/app/components/InputWithIcon';
import DropDownButton from '@/app/components/DropDownButton';
import StatCardLayout from '@/app/components/StatCardLayout';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import CircularProgressBar from '@/app/components/CircularProgressBar';

const page = () => {
    const performanceStyles: Record<string, string> = {
        Good: 'bg-[#ECFDF3] text-[#027A48]', 
        Avarage: 'bg-[#F7E1C140] text-[#F2A735]', 
        Weak: 'bg-[#FF3B301A] text-[#FF3B30]', 
    };

    const tableHeadData = [
        'EID',
        'Name',
        'Designation',
        'Performance',
        'Progress'
    ];

    return (
        <DashboardLayout>
            <div className='flex justify-between mt-10'>
                <InputWithIcon 
                    icon={Search} 
                    placeholder='search by id, name, mobile, email' 
                    className='w-[300px] rounded-[50px] py-2.5 border-[0.5px] bg-white border-grayTwo' 
                />
                <DropDownButton />
            </div>
            <StatCardLayout className='mt-5'>
                <div className='overflow-x-auto rounded-[20px]'>
                    <table className='bg-white min-w-full text-sm text-left'>
                        <thead>
                            <tr>
                                {tableHeadData.map((thead,index) => (
                                    <th 
                                        key={index} 
                                        className='p-3.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive'
                                    >
                                        {thead}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                            {employeePerformanceList.map((success, index) => (
                                <tr key={index}>
                                    <td className='text-xs leading-5 text-graySix p-3.5 font-normal'>{success.id}</td>
                                    <td className='text-xs leading-5 text-graySix p-3.5 font-normal'>{success.name}</td>
                                    <td className='text-xs leading-5 text-graySix p-3.5 font-normal'>{success.designation}</td>
                                    <td className='text-xs leading-5 text-graySix p-3.5 font-normal'>
                                        <span className={`rounded-[16px] ${performanceStyles[success.performance]} inline-block py-0.5 px-2`}>{success.performance}</span>
                                    </td>
                                    <td className='text-xs leading-5 text-graySix p-3.5 flex items-center gap-2 font-normal'>
                                        <CircularProgressBar 
                                            value={success.progress} 
                                            size={32} 
                                            strokeWidth={2} 
                                        />
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </StatCardLayout>
        </DashboardLayout>
    )
}

export default page