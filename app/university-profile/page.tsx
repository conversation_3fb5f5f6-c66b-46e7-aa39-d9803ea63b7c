import Link from 'next/link';
import Image from 'next/image';
import Distance from '@/app/assets/svg/distance';
import CoursesIcon from '@/app/assets/svg/courses-icon';
import { UniversityProfileAccordionData } from '@/common';
import ArrowForward from '@/app/assets/svg/arrow_forward';
import AccordionLayout from '../components/AccordionLayout';
import AboutUniversity from '@/app/assets/svg/AboutUniversity';
import DashboardLayout from '../components/layout/DashboardLayout';
import HarvardLogo from '@/app/assets/svg/harvard-uni-logo.svg';
import UniversityProfileCard from '../components/UniversityProfileCard';
import UniProfileCostIcon from '@/app/assets/svg/uni-profile-cost-icon';
import ReactiveFavoriteIcon from '@/app/assets/svg/ReactiveFavoriteIcon';
import DescriptionsWithSeeMore from '../components/DescriptionsWithSeeMore';
import UniversityProfileSlider from '../components/UniversityProfileSlider';
import UniProfileTimeframeIcon from '@/app/assets/svg/uni-profile-timeframe-icon';
import UniProfileGeneralInfoIcon from '@/app/assets/svg/uni-profile-general-info-icon';
import { GeneralInformation, AcceptanceLetterTimeframe, CostAndDuration } from '@/common';

const Page = () => {
    return (
        <DashboardLayout>
            <div className="pt-5 pb-20">
                <h1 className='md:text-[28px] text-[18px] md:leading-[33px] leading-[21px] font-bold text-graySix'>
                    University Information
                </h1>
                <div className='flex flex-col gap-[30px] mt-5 md:p-6 p-4 bg-white rounded-2xl'>
                    <div className='flex justify-between md:items-center items-start'>
                        <div className='flex gap-5 md:items-center items-start'>
                            <div className=''>
                                <Image
                                    src={HarvardLogo}
                                    alt='uni logo'
                                    className='md:max-w-16 max-w-12 h-fit'
                                />
                            </div>
                            <div className='flex flex-col gap-2'>
                                <h2 className='font-semibold md:text-[28px] md:leading-[42px] text-2xl leading-[29px] text-graySix'>
                                    The University of Chicago
                                </h2>
                                <div className='flex md:flex-row flex-col gap-2 text-grayFour font-normal text-sm leading-[17px]'>
                                    <div className='flex gap-1.5 items-center'>
                                        <Distance />
                                        <p>5801 S Ellis Ave, Chicago, IL 60637,United States</p>
                                    </div>
                                    <div className='ml-6'>
                                        <ul className='list-disc flex md:flex-row flex-col gap-2'>
                                            <li>Private</li>
                                            <li className=''>Non-profit</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ReactiveFavoriteIcon />
                    </div>
                    <div className='justify-center items-center'>

                    <UniversityProfileSlider />
                    </div>
                    
                    <UniversityProfileCard 
                        heading='About' 
                        icon={<AboutUniversity />} 
                    >
                        <DescriptionsWithSeeMore
                            description={`The University of Chicago (UChicago, Chicago, U of C, or UChi) is a private research university in Chicago, Illinois. The university has its main campus in Chicago's Hyde Park neighborhood. The university is composed of an undergraduate college, four graduate research divisions, and eight professional schools, most of which also house academic research: the Law School; the Booth School of Business; the Pritzker School of Medicine; the Crown Family School of Social Work, Policy, and Practice; the Harris School of Public Policy; the Divinity School; the Graham School of Continuing Liberal and Professional Studies; and the Pritzker School of Molecular Engineering. The university has additional campuses and centers in London, Paris, Beijing, Delhi, and Hong Kong, as well as in downtown Chicago.`}
                        />
                    </UniversityProfileCard>
                    <div className='flex md:flex-row flex-col w-full justify-between gap-6'>
                        <UniversityProfileCard
                            className='w-full'
                            heading='General Information'
                            icon={<UniProfileGeneralInfoIcon />}
                        >
                            <div className='grid grid-cols-1 gap-[18px]'>
                                {GeneralInformation.map((item, index) => (
                                    <div className='flex w-full justify-between text-[13px] leading-4 font-normal' key={index}>
                                        <span className=''>{item.Label}</span>
                                        <span>{item.value}</span>
                                    </div>
                                ))}
                            </div>
                        </UniversityProfileCard>

                        <UniversityProfileCard
                            className='w-full'
                            heading='Typical timeframe for receiving an acceptance letter'
                            icon={<UniProfileTimeframeIcon />}
                        >
                            <div className='grid grid-cols-1 gap-[18px]'>
                                {AcceptanceLetterTimeframe.map((item, index) => (
                                    <div className='flex w-full justify-between text-[13px] leading-4 font-normal' key={index}>
                                        <span className=''>{item.Label}</span>
                                        <span>{item.value}</span>
                                    </div>
                                ))}
                            </div>
                        </UniversityProfileCard>

                        <UniversityProfileCard
                            className='w-full'
                            heading='Cost & Duration'
                            icon={<UniProfileCostIcon />}
                        >
                            <div className='grid md:grid-cols-2 grid-cols-1 gap-3'>
                                {CostAndDuration.map((item, index) => (
                                    <div className='flex items-start gap-2.5' key={index}>
                                        <div className='h-[18px] w-[18px]'>
                                            {item.icon()}
                                        </div>
                                        <div className='flex flex-col gap-1'>
                                            <span className='text-base leading-5 font-medium text-graySix'>{item.value}</span>
                                            <span className='text-xs leading-[15px] font-normal text-grayFour'>{item.Label} </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </UniversityProfileCard>
                    </div>
                    <UniversityProfileCard
                        className='w-full gap-4'
                        heading='Courses'
                        icon={<CoursesIcon />}
                    >
                        <AccordionLayout data={UniversityProfileAccordionData} />
                        <Link
                            href='/course-finder'
                            className='mt-4 flex items-baseline gap-1 text-primaryColor'
                        >
                            <span className='text-[13px] font-semibold leading-4'>See More</span>
                            <ArrowForward className='!w-2 !h-2' />
                        </Link>
                        
                    </UniversityProfileCard>
                </div>
            </div>
        </DashboardLayout>
    );
};

export default Page;
