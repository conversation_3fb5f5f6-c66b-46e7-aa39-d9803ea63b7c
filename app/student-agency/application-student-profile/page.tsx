'use client';

import Image from 'next/image';
import dynamic from 'next/dynamic';
import React, { useState } from 'react';
import Heading from '@/app/components/Heading';
import { useToast } from '@/hooks/use-toast';
// import Stepper from '../components/Stepper';
import { But<PERSON> } from '@/components/ui/button';
import School from '@/app/assets/svg/school';
import Calender from '@/app/assets/svg/calendar';
import AccountIcon from '@/app/assets/svg/account';
// import ApplicantTab from '../components/ApplicantTab';
import ApplicantInfoCard from '@/app/components/ApplicantInfoCard';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import AddDocumentIcon from '@/app/assets/svg/add-document-outline';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import DialogIcon from '@/app/assets/svg/change-intake-modal-icon.svg';
const Stepper = dynamic(() => import('@/app/components/Stepper'), { ssr: false });
import { ApplicationProfileIntakes, ApplicationProfileStepperData } from '@/common';
const ApplicantTab = dynamic(() => import('@/app/components/ApplicantTab'), { ssr: false });
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Sheet,
    SheetClose,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from "@/components/ui/sheet";
import Link from 'next/link';
import AgencyApplicantTab from '@/app/components/AgencyApplicantTab';


const Page:React.FC = () => {

    const [selectedOption, setSelectedOption] = useState<number>(1);
    const { toast } = useToast();

    return (
        <DashboardLayout>
            <div className=' flex flex-col gap-5 h-auto justify-between pt-5 pb-20 '>
                <div className='flex justify-between items-center'>
                    <Heading level='h1'>
                        Application
                    </Heading>
                    <Link href={'/profile?tab=application'} className='flex gap-3 py-2.5 px-5 rounded-[50px] bg-primaryColor text-white md:text-sm text-xs leading-[17px] font-semibold drop-shadow-5xl hover:bg-secondaryColor'>
                        <AddDocumentIcon />
                        Add Application
                    </Link>
                </div>
                <div>
                    <div className='grid md:grid-cols-3 grid-cols-1 gap-5 md:place-items-center place-content-start bg-white py-4 px-[25px] rounded-[10px] drop-shadow-10xl'>
                        <div>
                            <div className='text-base leading-[19px] text-grayFive flex gap-3.5 font-medium '>
                                <div className='w-5 h-5'>
                                    <Calender />
                                </div>
                                22 Jun 2023
                            </div>
                        </div>
                        <div>
                            <div className='text-base leading-[19px] text-grayFive flex gap-3.5 font-medium '>
                                <div className='w-5 h-5'>
                                    <School />
                                </div>
                                The University of Chicago
                            </div>
                        </div>
                        <div>
                            <div className='text-base leading-[19px] text-grayFive flex gap-3.5 font-medium '>
                                <div className='w-5 h-5'>
                                    <AccountIcon />
                                </div>
                                Guptin Lee
                            </div>
                        </div>
                    </div>
                </div>
                <div className='flex flex-wrap justify-between items-center gap-[18px]'>
                    <div className='flex  gap-5 items-center'>
                        <h2 className='md:text-2xl text-[22px] md:leading-[29px] leading-[27px] font-semibold text-graySix'>
                            ID # 4658
                        </h2>
                        <span className='text-sm leading-[21px] font-medium border border-[#FF3B30] bg-[#FF52521A] rounded-[50px] text-[#FF3B30] py-2 px-3'>Application Cancelled</span>
                    </div>
                    <Sheet>
                        <SheetTrigger className='py-2.5 px-5 rounded-[50px] bg-white text-primaryColor md:text-sm text-xs leading-[17px] font-semibold drop-shadow-5xl hover:bg-primaryOne'>Change Intake</SheetTrigger>
                        <SheetContent>
                            <SheetHeader>
                                <div className='flex flex-col gap-6 items-center pb-10 pt-12'>
                                    <Image
                                        src={DialogIcon}
                                        alt='dialog icon'
                                    />
                                    <div>
                                        <SheetTitle className='text-center text-lg leading-[21px] font-semibold text-graySix'>
                                            Change Intake
                                        </SheetTitle>
                                    </div>
                                </div>
                            </SheetHeader>
                            <SheetDescription>
                            </SheetDescription>
                                <div className='space-y-1.5 mb-5'>
                                    <label className='text-sm font-medium '>
                                        ESL Intake
                                    </label>
                                    <Select>
                                        <SelectTrigger className='w-full border-[#1952BB33] shadow-none focus:ring-0'>
                                            <SelectValue placeholder='Select' />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value='Spring 2025'>Spring 2025</SelectItem>
                                            <SelectItem value='Summer 2025'>Summer 2025</SelectItem>
                                            <SelectItem value='Fall 2025'>Fall 2025</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-3.5">
                                    <label className="text-sm font-medium -mb-1">Academic Intake</label>
                                    <RadioGroup
                                        defaultValue={selectedOption.toString()}
                                        onValueChange={(value) => setSelectedOption(Number(value))}
                                        className="space-y-2"
                                    >
                                        {ApplicationProfileIntakes.map((intake) => (
                                        <div
                                            key={intake.id}
                                            className={`flex space-x-2 border rounded-[8px] p-2.5 ${
                                            selectedOption === intake.id
                                                ? "border-primaryColor bg-primaryOne"
                                                : "border-grayOne"
                                            }`}
                                        >
                                            <RadioGroupItem
                                            value={intake.id.toString()}
                                            id={intake.id.toString()}
                                            className="shrink-0"
                                            />
                                            <label
                                            htmlFor={intake.id.toString()}
                                            className="cursor-pointer flex flex-col gap-2.5"
                                            >
                                            <span className="font-semibold text-base leading-[21px] text-graySix">
                                                {intake.monthYear}
                                            </span>
                                            <div className="grid grid-cols-2 text-sm leading-[17px] text-grayFive gap-2">
                                                <span>Intake Status</span>
                                                <span>Submission Deadline</span>
                                                <span className="font-medium">{intake.status}</span>
                                                <span className="font-medium">{intake.deadline}</span>
                                            </div>
                                            </label>
                                        </div>
                                        ))}
                                    </RadioGroup>
                                </div>

                                <div className='flex w-full mt-[30px] gap-3'>
                                    <SheetClose asChild>
                                        <Button className='w-1/2 font-semibold leading-[24px] bg-white text-graySix shadow-none drop-shadow-[0px_1px_2px_rgba(16,24,40,0.05)] border border-grayOne rounded-[8px]'>
                                            Cancel
                                        </Button>
                                    </SheetClose>
                                    <Button 
                                        onClick={() => {
                                            toast({
                                                description: "Your message has been sent.",
                                            })
                                        }}
                                        className='w-1/2 font-semibold leading-[24px] bg-primaryColor text-white shadow-none drop-shadow-[0px_1px_2px_rgba(16,24,40,0.05)] border border-primaryColor rounded-[8px]'
                                    >
                                        Confirm
                                    </Button>
                                </div>
                        </SheetContent>
                    </Sheet>
                </div>
                <div className='bg-white py-6 px-5 md:px-20 rounded-[10px] drop-shadow-10xl overflow-hidden md:block hidden'>
                    <Stepper currentStep={4} steps={ApplicationProfileStepperData} />
                </div>
                <div className='flex flex-col md:flex-row gap-6 '>
                    <ApplicantInfoCard 
                        name='Guptin Lee'
                        email='<EMAIL>'
                        university='The University of Chicago'
                        program='Master of Science - in Office Management'
                        level='Master’s Degree'
                        requiredLevel='4 Years Bachelor’s Degree'
                        applicationId='#46449698'
                        deliveryMethod='In Class'
                        intakes={{ esl: null, academic: 'Closed', sessionend: 'Oct 2024' }}
                        submissionDeadline='Nov 10, 2024'
                        paymentStatus='Pending'
                        paymentAmount='$500'
                    />
                    <div className='flex flex-col  gap-5 w-full'>
                        <ApplicantTab />
                        <AgencyApplicantTab />
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default Page;
