import React from 'react';
import Link from 'next/link';
import AuthLayout from '../components/layout/AuthLayout';
import SuccessfulGreenIcon from '../assets/svg/SuccessfulGreenIcon';
import SuccessfulResetPasswordCover from '../assets/svg/SuccessfulResetPasswordCover';

const page = () => {
    return (
        <AuthLayout
            imageSrc={<SuccessfulResetPasswordCover />}
            title='Successful!'
            successIcon={<SuccessfulGreenIcon />}
            heading='Congratulations! Your password has been changed. Click continue to login.'
            description='Password updated! You’re one step closer to a safer, stronger journey.'
        >
            <div className='max-w-[420px] mx-auto'>
                <Link
                    href=''
                    className='mt-8 block w-full font-semibold text-base leading-6 rounded-[8px] py-2.5 px-[18px] bg-primaryColor text-center text-white'
                >
                    Continue
                </Link>
            </div>
        </AuthLayout>
    )
}

export default page