import { auth } from '../../lib/auth';
import Image from 'next/image';
import React, { useState} from 'react';
import { doLogout } from '@/app/actions';
import { redirect } from 'next/navigation';
import Avatar from '../assets/img/Avatar.png';
import UniversityList from '../components/UniversityList';
import FormSubmitionDemo from '../components/FormSubmitionDemo';
import ReactQueryProvider from '../providers/ReactQueryProvider';
import DashboardLayout from '../components/layout/DashboardLayout';
import UniversityListAndPagination from '../components/UniversityListAndPagination';

const page = async () => {
    const session = await auth();
    if (!session?.user) redirect('/');

    return (
        <DashboardLayout>
            {/* <p>{session.user.name}</p>
            <p>{session.user.email}</p>
            <Image 
                src={session.user.image ?? Avatar}
                alt='Profile picture'
                width={140}
                height={140}
            />
             <form action={doLogout}>
                <button className='bg-blue-400 my-2 text-white p-1 rounded' type='submit'>Logout</button>
            </form> */}
            {/* <ReactQueryProvider> */}
                {/* <UniversityList /> */}
                {/* <FormSubmitionDemo /> */}
                {/* <UniversityListAndPagination /> */}
            {/* </ReactQueryProvider> */}
            <p>logedin</p>
        </DashboardLayout>
    )
}

export default page
