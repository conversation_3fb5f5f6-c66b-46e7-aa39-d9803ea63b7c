'use client'

import { useState } from 'react';
import { ArrowUp } from 'lucide-react';
import DateRangeSelect from './DateRangeSelect';
import { ApplyGoalSuccessData } from '@/common';
import TrophyReward from '../assets/svg/TrophyReward';
import { 
    Line, 
    XAxis, 
    <PERSON>Axi<PERSON>,
    <PERSON>hart, 
    CartesianGrid
} from 'recharts';
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';
const chartConfig = {
    desktop: {
        label: 'Desktop',
        color: '#1E62E0',
    },
    mobile: {
        label: 'Mobile',
        color: '#1E62E057',
    },
} satisfies ChartConfig;

export function MultipleLineChart() {
    const [selectedRange, setSelectedRange] = useState<keyof typeof ApplyGoalSuccessData>('This Year');
    const chartData = ApplyGoalSuccessData[selectedRange];

    const tickFormatter = (value: string) => {
        if (selectedRange === 'This Year' || selectedRange === 'This Week') {
            return value.slice(0, 3);
        }
        return value;
    };

    const options = ['This Year', 'This Month', 'This Week', 'Today'];

    return (
        <Card className='border-none shadow-none'>
            <CardHeader>
                <CardTitle className=' flex justify-between ite'>
                    <div className='flex items-center gap-2.5 md:text-2xl text-base md:leading-[29px] leading-5 text-primaryColor font-bold'>
                        <TrophyReward />
                        <h2>ApplyGoal Success Ratio</h2>
                    </div>
                    <div className='text-primaryColor md:text-sm text-[10px] md:leading-[17px] leading-3 font-semibold'>
                        <DateRangeSelect
                            options={options}
                            selectedValue={selectedRange}
                            onChange={(value) => setSelectedRange(value as keyof typeof ApplyGoalSuccessData)}
                            placeholder="Select Time Range"
                        />
                    </div>
                </CardTitle>
                <div className='md:ml-[30px] ml-[16px] flex items-center gap-[30px]'>
                    <span className='font-bold md:text-[32px] text-2xl md:leading-[38px] leading-[29px] text-primaryColor'>15k+</span>
                    <span className='font-medium md:text-base text-sm md:leading-[19px] leading-[17px] text-primaryColor flex items-center gap-1'> 
                        <ArrowUp />
                        65% than last year
                    </span>
                </div>
            </CardHeader>
            <CardContent>
                <ChartContainer config={chartConfig} className='md:max-h-[300px] max-h-[335px] w-full'>
                    <LineChart
                        accessibilityLayer
                        data={chartData}
                        // margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >   
                        
                        <CartesianGrid vertical={false} />
                        <XAxis
                            dataKey={selectedRange === 'Today' ? 'hour' : selectedRange === 'This Month' || selectedRange === 'This Week' ? 'day' : 'month'}
                            tickLine={false}
                            axisLine={false}
                            tickMargin={10}
                            tickFormatter={tickFormatter}
                        />
                        <YAxis 
                            axisLine={false} 
                            tickLine={false} 
                            tickMargin={50}
                            textAnchor='right'
                        />
                        <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
                        <Line
                            dataKey='desktop'
                            type='natural'
                            stroke='var(--color-desktop)'
                            strokeWidth={2.19}
                            dot={{
                                fill: 'var(--color-desktop)',
                            }}
                            activeDot={{
                                r: 6,
                            }}
                        />
                        <Line
                            type='monotone'
                            dataKey='mobile'
                            stroke='var(--color-mobile)'
                            strokeWidth={2.19}
                            strokeDasharray='10'
                            dot={{
                                fill: 'var(--color-mobile)',
                            }}
                            activeDot={{
                                r: 6,
                            }}
                        />
                    </LineChart>
                </ChartContainer>
            </CardContent>
            <CardFooter>
                <div className='flex items-center justify-center gap-5 w-full'>
                    <div className='flex items-center gap-2'>
                        <div className='py-[2px] px-[9px] rounded-2xl bg-primaryColor'></div>
                        <div className='text-grayFour font-normal text-xs leading-[14px]'>This Year</div>
                    </div>
                    <div className='flex items-center gap-2'>
                        <div className='py-[2px] px-[9px] border border-dashed border-[#B3CAF4] rounded-2xl  bg-primaryOne'></div>
                        <div className='text-grayFour font-normal text-xs leading-[14px]'>Last year</div>
                    </div>
                </div>
            </CardFooter>
        </Card>
    )
}
