import { StudentAgencyBannerNotificationProps } from '@/types'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const BannerNotification: React.FC<StudentAgencyBannerNotificationProps> = ({
BannerImage,
description,
CompanyLogo
}) => {
    return (
        <div className=' md:pl-12 pl-[35px] md:pr-12 pr-[70px] flex justify-between w-full rounded-3xl bg-gradient-to-r from-tertiary to-[#377DFF80] drop-shadow-4xl'>
            <div className='flex flex-col justify-between gap-[60px] md:py-11 py-8 text-white'>
                <div>
                    <Image 
                        alt='ApplyGoal white'
                        src={CompanyLogo}
                    />
                    <h2 className='text-[22px] font-normal leading-none mt-[18px]'>"Fast-Track Your Student Visa: Join Our Free Webinar!"</h2>
                    <div className='flex items-center gap-3 mt-[30px]'>
                        <Link href={'/'} className='px-5 py-3 text-sm border rounded-full bg-white text-primaryColor'>Join Now</Link>
                        <Link href={'/'} className='px-5 py-3 text-sm border rounded-full'>Find Out More</Link>
                    </div>
                </div>
            </div>

            <div className='md:block hidden'>
                {BannerImage}
            </div>
        </div>
    )
}

export default BannerNotification
