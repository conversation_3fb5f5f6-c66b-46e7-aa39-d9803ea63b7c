import React from 'react';
import { VerticalTabLayoutProps } from '@/types';
import {
    Tabs,
    TabsList,
    TabsTrigger,
    TabsContent,
} from '@/components/ui/tabs';

const VerticalTabLayout: React.FC<VerticalTabLayoutProps> = ({
    tabMenu,
    tabContents,
    defaultValue,
    tabListClassName = '',
    tabTriggerClassName = '',
    tabContentClassName = '',
}) => {
    return (
        <>
        <Tabs 
            defaultValue={defaultValue}
            className='w-full'
        >
            <TabsList className={`${tabListClassName} h-12 bg-primaryOne flex flex-col md:flex-row max-w-full gap-2.5 md:items-center items-start md:justify-between justify-start md:mb-0 mb-28 pb-6 rounded-none border-b px-0`}>
                <div className='flex gap-14 place-items-start text-grayFive'>
                    {tabMenu.map((tab, index) => (
                        <TabsTrigger
                            key={index}
                            value={tab.value}
                            className={`py-3 ${tabTriggerClassName} data-[state=active]:text-primaryColor data-[state=active]:shadow-none data-[state=active]:rounded-none rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 -mb-1 border-primaryColor px-0 mr-8`}
                        >
                            {tab.label}
                        </TabsTrigger>
                    ))}
                </div>
            </TabsList>
            {tabContents.map((content) => (
                <TabsContent
                    key={content.value}
                    value={content.value}
                    className={` ${tabContentClassName}`}
                >
                    {content.children}
                </TabsContent>
            ))}
        </Tabs>
        </>
    )
}

export default VerticalTabLayout;