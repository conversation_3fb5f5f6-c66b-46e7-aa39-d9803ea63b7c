import Image from 'next/image';
import { useState } from 'react';
import FileUpload from './FileUpload';
import { DatePicker } from './DatePicker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
    Sheet,
    SheetClose,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from '@/components/ui/sheet';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const SideDrawer = () => {
    const [formData, setFormData] = useState({
        eventTitle: '',
        eventType: '',
        eventDescription: '',
        date: '',
        timeZone: '(GMT -8:00) Pacific Time',
        startTime: '8:30 AM',
        endTime: '10:30 AM',
        platform: 'Zoom',
        platformLink: '',
        associatedUniversity: '',
        associatedAgencies: '',
        requiresRegistration: false,
        registrationDate: '',
        eventBanner: null as File | null,
        attachments: null as File | null,
        visibility: 'Public'
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'eventBanner' | 'attachments') => {
        if (e.target.files?.[0]) {
            setFormData(prev => ({
                ...prev,
                [field]: e.target.files?.[0]
            }));
        }
    };

    return (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline">Create Event</Button>
            </SheetTrigger>
            <SheetContent className="w-[500px] sm:w-[540px] overflow-y-auto">
                <SheetHeader>
                    {/* <Image src={DialogIcon} alt='dialog icon' /> */}
                    <SheetTitle className='text-center'>Create New Event</SheetTitle>
                </SheetHeader>
                
                <div className="space-y-4 py-4">
                    <div className="space-y-2 grid grid-cols-1">
                        <Label htmlFor="eventTitle">Event Title</Label>
                        <input
                            type="text"
                            id="eventTitle"
                            name="eventTitle"
                            className='outline-none border border-tertiary border-opacity-20 py-2 px-3 rounded-lg placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                            value={formData.eventTitle}
                            onChange={handleChange}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label>Event Type</Label>
                        <Select onValueChange={(value) => setFormData(prev => ({ ...prev, eventType: value }))}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select event type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="webinar">Webinar</SelectItem>
                                <SelectItem value="workshop">Workshop</SelectItem>
                                <SelectItem value="conference">Conference</SelectItem>
                                <SelectItem value="seminar">Seminar</SelectItem>
                                <SelectItem value="virtualFair">Virtual Fair</SelectItem>
                                <SelectItem value="infoSession">Info Session</SelectItem>
                                <SelectItem value="interview">Interview</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label>Event Description</Label>
                        <Textarea
                            name="eventDescription"
                            value={formData.eventDescription}
                            onChange={handleChange}
                            className="min-h-[100px]"
                        />
                    </div>

                    <div className="space-y-2">
                        {/* <Label>Date</Label>
                        <Input
                            type="date"
                            name="date"
                            value={formData.date}
                            onChange={handleChange}
                        /> */}
                        <DatePicker label='Date' />
                    </div>

                    <div className="space-y-2">
                        <Label>Your Time Zone</Label>
                        <Select onValueChange={(value) => setFormData(prev => ({ ...prev, timeZone: value }))}>
                            <SelectTrigger>
                                <SelectValue placeholder={formData.timeZone} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="pt">(GMT -8:00) Pacific Time</SelectItem>
                                <SelectItem value="et">(GMT -5:00) Eastern Time</SelectItem>
                                <SelectItem value="ct">(GMT -6:00) Central Time</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label>Start Time</Label>
                            <Select onValueChange={(value) => setFormData(prev => ({ ...prev, startTime: value }))}>
                                <SelectTrigger>
                                    <SelectValue placeholder={formData.startTime} />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="8:30 AM">8:30 AM</SelectItem>
                                    <SelectItem value="9:00 AM">9:00 AM</SelectItem>
                                    <SelectItem value="9:30 AM">9:30 AM</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>End Time</Label>
                            <Select onValueChange={(value) => setFormData(prev => ({ ...prev, endTime: value }))}>
                                <SelectTrigger>
                                    <SelectValue placeholder={formData.endTime} />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10:30 AM">10:30 AM</SelectItem>
                                    <SelectItem value="11:00 AM">11:00 AM</SelectItem>
                                    <SelectItem value="11:30 AM">11:30 AM</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label>Platform</Label>
                        <Select onValueChange={(value) => setFormData(prev => ({ ...prev, platform: value }))}>
                            <SelectTrigger>
                                <SelectValue placeholder="Zoom" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="zoom">Zoom</SelectItem>
                                <SelectItem value="teams">Microsoft Teams</SelectItem>
                                <SelectItem value="meet">Google Meet</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label>Platform Link</Label>
                        <Input
                            name="platformLink"
                            value={formData.platformLink}
                            onChange={handleChange}
                            placeholder="https://zoom.us/j/..."
                        />
                    </div>

                    <div className="flex items-center space-x-2">
                        <Label>Registration Required?</Label>
                        <Switch
                            checked={formData.requiresRegistration}
                            onCheckedChange={(checked) => 
                                setFormData(prev => ({ ...prev, requiresRegistration: checked }))
                            }
                        />
                    </div>

                    {/* <div className="space-y-2">
                        <Label>Event Banner/Image</Label>
                        <Input
                            type="file"
                            onChange={(e) => handleFileChange(e, 'eventBanner')}
                            accept="image/*"
                        />
                    </div> */}
                    {/* <FileUpload /> */}

                    {/* <div className="space-y-2">
                        <Label>Attachments</Label>
                        <Input
                            type="file"
                            onChange={(e) => handleFileChange(e, 'attachments')}
                            accept=".pdf,.doc,.docx"
                        />
                    </div> */}

                    <div className="space-y-2">
                        <Label>Visibility</Label>
                        <Select onValueChange={(value) => setFormData(prev => ({ ...prev, visibility: value }))}>
                            <SelectTrigger>
                                <SelectValue placeholder="Public" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="public">Public</SelectItem>
                                <SelectItem value="private">Private</SelectItem>
                                <SelectItem value="invited">Invited Only</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="flex justify-end space-x-4 mt-6">
                    <SheetClose asChild>
                        <Button variant="outline">Cancel</Button>
                    </SheetClose>
                    <Button type="submit" onClick={() => console.log(formData)}>Confirm</Button>
                </div>
            </SheetContent>
        </Sheet>
    );
};

export default SideDrawer;
