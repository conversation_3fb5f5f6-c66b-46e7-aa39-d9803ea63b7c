import React from 'react';
import { AllStudentStatusProps } from '@/types';

const AllStudentStatus: React.FC<AllStudentStatusProps> = ({ status }) => {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'Pending':
                return 'bg-[#F7E1C1] bg-opacity-25 text-[#F2A735]';
            case 'Reject':
                return 'text-[#B42318] bg-[#FEF3F2]';
            case 'Success':
                return 'text-[#027A48] bg-[#ECFDF3]';
            case 'Discontinue':
                return 'text-[#9FA0A6] bg-[#EFF0F0]';
            default:
                return 'text-grayThree bg-black';
        }
    };
    return (
        <p
            className={`w-fit flex items-center gap-1 rounded-2xl py-0.5 px-2 font-medium text-xs leading-4 ${getStatusColor(status.label)}`}
        >
             {status.icon}
             {status.label}
        </p>
    )
}

export default AllStudentStatus