'use client'

import { z } from 'zod';
import Image from 'next/image';
import Switcher from './Switcher';
import Lock from '../assets/svg/Lock';
import EditButton from './EditButton';
import { notifications } from '@/common';
import { useForm } from 'react-hook-form';
import Notifications from './Notifications';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import React, { useState, useRef  } from 'react';
import HelpIcon from '@/app/assets/svg/HelpIcon';
import Profile from '@/app/assets/img/profile.png';
import Visibility from '@/app/assets/svg/visibility';
import { zodResolver } from '@hookform/resolvers/zod';
import PhotoUpload from '@/app/assets/svg/photoUpload';
import ArrowForward from '../assets/svg/arrow_forward';
import ArrowLeftIcon from '../assets/svg/ArrowLeftIcon';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import {
    <PERSON>,
    CardContent,
    CardFooter,
    CardHeader
} from '@/components/ui/card';
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger
} from '@/components/ui/tabs';

import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';

const formSchema = z
    .object({
        username: z.string().min(5, {
            message: 'Username must be at least 5 characters.',
        }),
        email: z.string().email({
            message: 'Please enter a valid email address.',
        }),
        phone: z
            .string()
            .regex(/^\d{10}$/, { message: 'Phone number must be 10 digits.' }),
        password: z
            .string()
            .min(8, { message: 'Password must be at least 8 characters.' }),
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords must match.',
        path: ['confirmPassword'],
    });

const SettingsTab = () => {
    const [editMode, setEditMode] = useState(false);
    const [showForm, setShowForm] = useState(false);
    const [showPasswordField, setShowPasswordField] = useState(true);
    const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
    const [isChecked, setIsChecked] = useState(false);
    const [profileImage, setProfileImage] = useState<string>(Profile.src);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const showChangePasswordField = () => {
        setShowPasswordField(false);
        setShowForm(true)
    }
    const handleToggle = (checked: boolean) => {
        setIsChecked(checked);
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
          const imageUrl = URL.createObjectURL(file);
          setProfileImage(imageUrl);
        }
    };
    
    const triggerFileInput = () => {
        fileInputRef.current?.click();
    };

    const inputButtonClasss = 'focus:outline-none focus:border focus:border-[#1E62E0] focus:border-opacity-60 font-normal text-sm leading-6 border border-[#1E62E0] border-opacity-20 bg-primaryOne rounded-lg px-3.5 py-2.5 w-1/2 text-[#667085]';

    const [showPassword, setShowPassword] = useState(false);
        const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    
        const togglePasswordVisibility = () => setShowPassword((prev) => !prev);
        const toggleConfirmPasswordVisibility = () =>
            setShowConfirmPassword((prev) => !prev);
    
        const form = useForm<z.infer<typeof formSchema>>({
            resolver: zodResolver(formSchema),
            defaultValues: {
                username: '',
                email: '',
                phone: '',
                password: '',
                confirmPassword: '',
            },
        });
    
        function onSubmit(values: z.infer<typeof formSchema>) {
            // console.log(values);
            return values;
        }
    return (
        <Tabs defaultValue='personal' className='w-full'>
            <TabsList className='bg-[#E9F0FF] h-20 rounded-[16px] w-full'>
                <div className='mx-1.5 bg-primaryOne grid  grid-cols-2 w-full rounded-xl'>
                    <TabsTrigger 
                        className='data-[state=active]:shadow-none rounded-none rounded-l-xl data-[state=active]:text-primaryColor font-semibold text-base leading-5 text-grayFive py-5' 
                        value='personal'
                    >
                        Personal
                    </TabsTrigger>
                    <TabsTrigger 
                        className='data-[state=active]:shadow-none rounded-none rounded-r-xl data-[state=active]:text-primaryColor font-semibold text-base leading-5 text-grayFive py-5' 
                        value='notification'
                    >
                        Notification
                    </TabsTrigger>
                </div>
            </TabsList>
            <TabsContent className='bg-white mt-6 rounded-[16px]' value='personal'>
                <Card className='max-w-[700px] mx-auto shadow-none border-none'>
                    {!showAdvancedSettings && (
                        <CardHeader className='p-0'>
                        <div className='px-4 md:px-0 gap-6 md:gap-0 pt-[50px] flex flex-col md:flex-row justify-between'>
                            <div className='flex items-center gap-6 relative'>
                                <div className='relative'>
                                    <Image 
                                        width={82} 
                                        height={82} 
                                        src={profileImage} 
                                        alt='Profile image'
                                        className='rounded-full object-cover'
                                    />
                                    <div
                                        onClick={triggerFileInput}
                                        className='absolute bottom-0 right-0 cursor-pointer bg-white rounded-full p-1 drop-shadow-2xl'
                                    >
                                        <PhotoUpload />
                                    </div>
                                    <input
                                        type='file'
                                        accept='image/*'
                                        ref={fileInputRef}
                                        onChange={handleImageChange}
                                        className='hidden'
                                    />
                                </div>
                                <div className='flex flex-col'>
                                    <span className='font-medium text-xl leading-6 text-graySix'>Jack Tark</span>
                                    <p className='font-normal text-base leading-5 text-grayFour'><EMAIL></p>
                                </div>
                            </div>
                            <div>
                                <EditButton 
                                    onClick={() => setEditMode(!editMode)}
                                    className='bg-primaryOne' 
                                />
                            </div>
                        </div>
                    </CardHeader>
                    )}
                    <CardContent className=''>
                        {!showAdvancedSettings && (
                            <>
                            <div className='mt-6 space-y-4'>
                                <div className='border-t pt-10 flex justify-between text-sm'>
                                    <span className='text-base font-medium leading-[35px] text-graySix'>Id</span>
                                    <span className='font-normal text-base leading-[35px] text-grayFour'>#A345</span>
                                </div>
                                <div className='border-t pt-10 flex justify-between text-sm'>
                                    <span className='text-base font-medium leading-[35px] text-graySix'>User Name</span>
                                    {editMode ? (
                                        <input
                                            type='text'
                                            className={inputButtonClasss}
                                            defaultValue='Jack-Tark'
                                        />
                                    ) : (
                                        <span className='font-normal text-base leading-[35px] text-grayFour'>Jack-Tark</span>
                                    )}
                                </div>
                                <div className='border-t pt-10 flex justify-between text-sm'>
                                    <span className='text-base font-medium leading-[35px] text-graySix'>Email account</span>
                                    {editMode ? (
                                        <input
                                            type='email'
                                            className={inputButtonClasss}
                                            defaultValue='<EMAIL>'
                                        />
                                    ) : (
                                        <span className='font-normal text-base leading-[35px] text-grayFour'><EMAIL></span>
                                    )}
                                </div>
                                <div className='border-t pt-10 flex justify-between text-sm'>
                                    <span className='text-base font-medium leading-[35px] text-graySix'>Mobile number</span>
                                    {editMode ? (
                                        <input
                                            type='tel'
                                            className={inputButtonClasss}
                                            defaultValue='+880 1877-365587'
                                        />
                                    ) : (
                                        <span className='font-normal text-base leading-[35px] text-grayFour'>+880 1877-365587</span>
                                    )}
                                </div>
                                <div className='border-y pt-10 pb-3.5 flex justify-between text-sm'>
                                    <span className='text-base font-medium leading-[35px] text-graySix'>Location</span>
                                    {editMode ? (
                                        <textarea
                                            className={inputButtonClasss}
                                            defaultValue='58/GH/ 22, Sukrabad, Dhanmondi, Dhaka'
                                        />
                                    ) : (
                                        <span className='font-normal text-base leading-[35px] text-grayFour'>
                                            58/GH/ 22, Sukrabad, Dhanmondi, Dhaka
                                        </span>
                                    )}
                                </div>
                            </div>
                            <div onClick={() => setShowAdvancedSettings(true)} className='rounded-[10px] border py-3 px-4 border-primaryTwo mt-[35px] cursor-pointer'>
                                <div className='flex justify-between items-center'>
                                    <div>
                                        <span className='font-medium text-xs leading-[18px] text-graySix'>Advanced Settings</span>
                                        <p className='font-normal leading-4 text-[10px] text-grayThree'>Customize features and fine-tune your experience.</p>
                                    </div>
                                    <div>
                                        <ArrowForward className='text-graySix w-3.5 h-3.5' />
                                    </div>
                                </div>
                            </div>
                            </>   
                        )}
                        
                        {showAdvancedSettings && (
                            <div className='py-12'>
                                <button 
                                    onClick={() => setShowAdvancedSettings(false)} 
                                    className='flex items-center gap-2.5'
                                >
                                    <ArrowLeftIcon />
                                    Advanced Settings
                                </button>
                                {showPasswordField && (
                                    <button 
                                        onClick={showChangePasswordField}
                                        className='text-left w-full font-medium text-sm leading-5 text-graySix mt-[35px] rounded-[10px] border border-[#E3E7FC] p-[18px]'
                                    >
                                        Change Password
                                     </button>
                                )}
                                {showPasswordField && (
                                    <div className='bg-primaryOne rounded-xl'>
                                        <div className='flex justify-between mt-8 py-6 px-5 '>
                                            <div className='flex gap-2.5 items-center'>
                                                <p className='font-medium text-xs md:text-sm text-graySix'>
                                                    Planning to Study at Another University or School? (Transfer Student)
                                                </p>
                                                <HoverCard>
                                                    <HoverCardTrigger>
                                                        <HelpIcon />
                                                    </HoverCardTrigger>
                                                    <HoverCardContent className='w-[290px] font-medium text-xs text-white -mt-11 ml-6 rounded-lg bg-[#144296]' align={'start'}>
                                                        'If you have a foreign degree, please specify the country where you earned your degree in the dropdown below.'
                                                    </HoverCardContent>
                                                </HoverCard>
                                            </div>
                                            <Switcher 
                                                classes='bg-grayTwo' 
                                                className='ml-3 border border-grayTwo' 
                                                isChecked={isChecked} 
                                                handleToggle={handleToggle}
                                            />
                                        </div>
                                        {isChecked && (
                                            <Button 
                                                className='mb-4 ml-4 hover:text-tertiary border border-tertiary font-semibold text-sm text-primaryOne bg-tertiary rounded-[220px] py-2.5 px-5 drop-shadow-3xl' 
                                                variant='secondary'
                                            >
                                                Transfer Student
                                            </Button>
                                        )}
                                    </div>
                                )}
                                {showForm  && (
                                    <div className='mt-7'>
                                        <div className='flex gap-4'>
                                            <Lock />
                                            <div className='flex flex-col'>
                                                <span className='font-medium text-base leading-6 text-graySix'>Change Password</span>
                                                <p className='font-normal text-xs leading-4 text-grayFive pt-0.5'>Choose a unique password to protect your account</p>
                                            </div>
                                        </div>
                                        <Form {...form}>
                                            <form onSubmit={form.handleSubmit(onSubmit)}>
                                                <FormField
                                                    control={form.control}
                                                    name='password'
                                                    render={({ field }) => (
                                                        <FormItem className='space-y-1.5 mt-5'>
                                                            <FormLabel className='text-sm font-medium text-graySix'>
                                                                Current Password *
                                                            </FormLabel>
                                                            <FormControl>
                                                                <div className="relative">
                                                                    <input
                                                                        type={showPassword ? 'text' : 'password'}
                                                                        className="flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
                                                                        placeholder="Enter your password"
                                                                        {...field}
                                                                    />
                                                                    <button
                                                                        type="button"
                                                                        onClick={togglePasswordVisibility}
                                                                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                                                    >
                                                                        { showPassword ? (<VisibilityOff />) : (<Visibility className='text-grayThree' />)}
                                                                    </button>
                                                                </div>
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                                <FormField
                                                    control={form.control}
                                                    name='password'
                                                    render={({ field }) => (
                                                        <FormItem className='space-y-1.5 mt-5'>
                                                            <FormLabel className='text-sm font-medium text-graySix'>
                                                                Password *
                                                            </FormLabel>
                                                            <FormControl>
                                                                <div className="relative">
                                                                    <input
                                                                        type={showPassword ? 'text' : 'password'}
                                                                        className="flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
                                                                        placeholder="Enter your password"
                                                                        {...field}
                                                                    />
                                                                    <button
                                                                        type="button"
                                                                        onClick={togglePasswordVisibility}
                                                                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                                                    >
                                                                        { showPassword ? (<VisibilityOff />) : (<Visibility className='text-grayThree' />)}
                                                                    </button>
                                                                </div>
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                                <FormField
                                                    control={form.control}
                                                    name='password'
                                                    render={({ field }) => (
                                                        <FormItem className='space-y-1.5 mt-5'>
                                                            <FormLabel className='text-sm font-medium text-graySix'>
                                                                Confirm Password *
                                                            </FormLabel>
                                                            <FormControl>
                                                                <div className="relative">
                                                                    <input
                                                                        type={showPassword ? 'text' : 'password'}
                                                                        className="flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
                                                                        placeholder="Enter your password"
                                                                        {...field}
                                                                    />
                                                                    <button
                                                                        type="button"
                                                                        onClick={togglePasswordVisibility}
                                                                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                                                    >
                                                                        { showPassword ? (<VisibilityOff />) : (<Visibility className='text-grayThree' />)}
                                                                    </button>
                                                                </div>
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                                <div className='flex gap-4 mt-8'>
                                                    <button className='w-full rounded-[8px] border py-2 px-[18px] border-primaryColor font-semibold text-base leading-6 text-primaryColor'>Cancel</button>
                                                    <button className='w-full rounded-[8px] border border-primaryColor bg-primaryColor py-2 px-[18px] text-white font-semibold text-base leading-6'>Save Changes</button>
                                                </div>
                                            </form>
                                        </Form>
                                    </div>
                                )}
                            </div>
                        )}
                    </CardContent>
                    {editMode && (
                        <CardFooter className='mt-9 flex gap-6 justify-end'>
                            <Button
                                className='hover:text-primaryOne hover:bg-tertiary border border-tertiary font-semibold text-sm text-tertiary rounded-[220px] py-2.5 px-5 drop-shadow-3xl'
                                variant='outline'
                            >
                                Cancel
                            </Button>
                            <Button 
                                className='hover:text-tertiary border border-tertiary font-semibold text-sm text-primaryOne bg-tertiary rounded-[220px] py-2.5 px-5 drop-shadow-3xl' 
                                variant='secondary'
                            >
                                Save Change
                            </Button>
                        </CardFooter>
                    )}
                </Card>
            </TabsContent>
            <TabsContent className='bg-white mt-6 rounded-[16px] px-4' value='notification'>
                <div className='py-10 max-w-[700px] mx-auto shadow-none'>
                    {notifications.map((notification, index) => (
                        <Notifications 
                            title={notification.title} 
                            description={notification.description} 
                            key={index} 
                        />
                    ))}
                </div>
            </TabsContent>
        </Tabs>
    )
}

export default SettingsTab