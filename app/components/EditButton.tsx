import React from 'react';
import Edit from '@/app/assets/svg/edit';
import { EditButtonProps } from '@/types';

const EditButton: React.FC<EditButtonProps> = ({ 
    className,
    onClick
}) => {
    return (
        <button 
            className={`flex ${className} items-center gap-2 rounded-[50px] py-2.5 px-5 drop-shadow-6xl`}
            onClick={onClick}
        >
            <Edit />
            <span className='font-semibold text-sm leading-[17px] text-tertiary'>Edit</span>
        </button>
    )
}

export default EditButton