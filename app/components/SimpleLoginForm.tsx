'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Checkbox } from "@/components/ui/checkbox";
import Visibility from '@/app/assets/svg/visibility';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import GoogleSignIn from './GoogleSignIn';

export default function SimpleLoginForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsPending(true);
    setError(null);

    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError(result.error);
      } else if (result?.ok) {
        router.push('/logedin');
        router.refresh();
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsPending(false);
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="space-y-1.5">
          <label className="text-sm font-medium text-graySix">
            Email*
          </label>
          <input
            name="email"
            type="email"
            required
            className="flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
            placeholder="Enter your email"
          />
        </div>

        <div className="space-y-1.5 mt-5">
          <label className="text-sm font-medium text-graySix">
            Password*
          </label>
          <div className="relative">
            <input
              name="password"
              type={showPassword ? 'text' : 'password'}
              required
              className="flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
              placeholder="Enter your password"
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
            >
              {showPassword ? (<VisibilityOff />) : (<Visibility className='text-grayThree' />)}
            </button>
          </div>
        </div>

        {error && (
          <div className="mt-4 p-2 bg-red-50 text-red-600 rounded-md text-sm">
            {error}
          </div>
        )}

        <div className='flex justify-between items-center mt-6'>
          <div className='flex items-center space-x-2 py-1'>
            <Checkbox className='border-tertiary border-opacity-20 shadow-none' />
            <label className='text-sm font-medium text-graySix'>
              Remember me
            </label>
          </div>
          <Link href={'/forget-password'} className='text-primaryColor font-semibold text-sm'>
            Forgot password
          </Link>
        </div>

        <Button
          type='submit'
          disabled={isPending}
          className='w-full bg-primaryColor rounded-[8px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white text-base font-semibold disabled:opacity-50'
        >
          {isPending ? 'Logging in...' : 'Log in'}
        </Button>
      </form>

      <div className='w-full'>
        <GoogleSignIn />
      </div>
      
      <div className='flex justify-center gap-1 mt-8'>
        <span className='text-sm font-normal text-grayFive'>
          Don't have an account?
        </span>
        <Link
          className='text-sm font-semibold text-primaryColor'
          href={'/sign-up'}
        >
          Sign up
        </Link>
      </div>
    </div>
  );
}
