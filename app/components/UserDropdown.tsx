'use client'

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import LogoutBtn from './LogoutBtn';
import ArrowUp from '../assets/svg/arrowUp';
import profile from '@/app/assets/svg/profile.svg';
import Verticalcontainer from '@/app/assets/img/Verticalcontainer.png';
import { useSelector } from 'react-redux';
import { RootState } from '../../lib/redux/store';


const Dropdown = () => {
    const [isOpen, setIsOpen] = useState(false);
    const user = useSelector((state: RootState) => state.auth.user);

    return (
        <div className='relative inline-block text-left'>
            <div
                className='flex items-center gap-2 cursor-pointer'
                onClick={() => setIsOpen(!isOpen)}
            >
                <Image
                    src={Verticalcontainer}
                    alt='User Avatar'
                    width={40}
                    height={40}
                    className='rounded-full'
                />
                <div className='hidden md:block'>
                    <p className='text-base font-normal text-graySix'>{user?.name}</p>
                    <p className='text-xs font-normal text-grayFour'>{user?.roles.name}</p>
                </div>
                <span className='hidden md:block'>
                    <ArrowUp className={`duration-500 ${isOpen ? '': 'rotate-180'}`} />
                </span>
            </div>


            {isOpen && (
                <div className='z-[9999] absolute right-0 mt-2 w-28 '>
                    <div className=' font-normal text-xs text-grayfive bg-white rounded-[6px] border border-tertiary/20 overflow-hidden'>
                        <Link
                            href={'/profile'}
                            className='flex items-center gap-2 w-full px-3 py-1.5 hover:bg-primaryOne'
                        >
                            <Image src={user?.image && user.image.trim() !== '' ? user.image : profile} alt='Profile logo' />
                            Profile
                        </Link>
                        
                        <LogoutBtn />
                    </div>
                </div>
            )}

            {isOpen && (
                <div
                    onClick={() => setIsOpen(false)}
                    className='fixed inset-0 z-10'
                >
                </div>
            )}

        </div>
    )};

export default Dropdown;