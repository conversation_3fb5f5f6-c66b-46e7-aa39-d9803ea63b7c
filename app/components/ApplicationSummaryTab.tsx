import React, { useState } from 'react';
import TabLayout from './layout/TabLayout';
import { CourseFinderFiltersProps } from '@/types';
import CoursesFilter from '../components/CoursesFilter';
import ApplicationSummaryBox from './applicationSummaryBox';
import { 
    // courses, 
    countries, 
    programs, 
    sortByOptions, 
    intakes 
} from '@/common';

const ApplicationSummaryTab = () => {
    const [incompleteTask, setIncompleteTask] = useState(true);
    const [completeTask, setCompleteTask] = useState(true);
    const [filters, setFilters] = useState<CourseFinderFiltersProps>({
        program: '',
        intake: '',
        country: [],
        tuitionFees: '',
        sortBy: '',
    });
    const tabLists = [
        { value: 'incomplete_tasks', label: 'Incomplete Tasks'},
        { value: 'completed_tasks', label: 'Completed Tasks'}
    ];

    const incompleteTasks = () => (
        <>
        <div className='space-y-3.5'>
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={5} />
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={15} />
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={5} />
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={5} />
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={5} />
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={5} />
            <ApplicationSummaryBox isIncomplete={incompleteTask} daysLeft={5} />
        </div>
        </>
    );

    const completedTasks = () =>  (
        <>
        <div className='space-y-3.5'>
            <ApplicationSummaryBox complete={completeTask} daysLeft={5} />
            <ApplicationSummaryBox complete={completeTask} daysLeft={15} />
            <ApplicationSummaryBox complete={completeTask} daysLeft={5} />
            <ApplicationSummaryBox complete={completeTask} daysLeft={5} />
            <ApplicationSummaryBox complete={completeTask} daysLeft={5} />
            <ApplicationSummaryBox complete={completeTask} daysLeft={5} />
            <ApplicationSummaryBox complete={completeTask} daysLeft={5} />
        </div>
        </>
    );
    
    const tabContents = [
        { value: 'incomplete_tasks', content: incompleteTasks },
        { value: 'completed_tasks', content: completedTasks },
    ];

    return (
        <TabLayout
            tabLists={tabLists}
            tabContents={tabContents}
        >
            <div className='mt-10'>
                <CoursesFilter 
                    programs={programs}
                    intakes={intakes}
                    countries={countries}
                    tuitionFees={filters.tuitionFees}
                    sortByOptions={sortByOptions}
                    onFilterChange={setFilters}
                    className='rounded-2xl border bg-white border-[#1E62E0] border-opacity-20 py-[30px] px-5'
                />
                <p className='mt-10 font-semibold text-xl leading-6 text-graySix'>All (123)</p>
            </div>
        </TabLayout>     
    )
}

export default ApplicationSummaryTab