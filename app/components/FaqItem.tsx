import { FaqItemProps } from '@/types';
import ArrowDown from '@/app/assets/svg/arrowDown';
import React, { 
    useRef, 
    useEffect, 
    useState 
} from 'react';


const FaqItem: React.FC<FaqItemProps> = ({ 
    question, 
    answer,
    boldListAns,
    listAns,
    listAnsText, 
    isOpen, 
    onToggle,
}) => {
    const contentRef = useRef<HTMLDivElement>(null);
    const [contentHeight, setContentHeight] = useState(0);

    useEffect(() => {
        if (contentRef.current) {
            setContentHeight(isOpen ? contentRef.current.scrollHeight : 0);
        }
    }, [isOpen]);

    return (
        <div 
            className='py-6 cursor-pointer border-b border-grayOne' 
            onClick={onToggle}
        >
            <div className='flex justify-between items-start gap-5'>
                <div className='w-[95%]'>
                    <h3 className='text-base leading-[18.58px] md:leading-6 md:text-xl font-medium text-grayFive'>
                        {question}
                    </h3>
                </div>
                <div className='w-[5%] flex justify-end'>
                    <div className='min-w-[32px] min-h-[32px] transition-all' >
                        <ArrowDown className={`transition-all duration-500 text-grayFive ${isOpen ? 'rotate-180':''}`} />
                    </div>
                </div>
            </div>
            
            <div 
                ref={contentRef}
                className={`w-[90%] transition-all duration-500 ease-in-out overflow-hidden`}
                style={{
                    height: `${contentHeight}px`,
                    opacity: isOpen ? 1 : 0,
                }}
            >
                {isOpen && (
                    <div>
                        {listAnsText && (
                            <p className='font-normal text-justify leading-[21px] md:leading-[27px] text-sm md:text-lg mt-4 text-grayFive'>
                                {listAnsText}
                            </p>
                        )}
                        {listAns && (
                            <ul className='font-normal text-justify leading-[21px] md:leading-[27px] text-sm md:text-lg mt-4 text-grayFive list-disc list-inside '>
                                {listAns.map((item,index) => (
                                    <li className='mt-2' key={index}>{item}</li>
                                ))}
                            </ul>
                        )}
                        {boldListAns && (
                            <ul className='list-disc list-inside'>
                                {boldListAns.map((item,index) => (
                                    <li className='font-normal text-justify leading-[21px] md:leading-[27px] text-sm md:text-lg mt-4 text-grayFive ' key={index}>
                                        <span className='font-semibold'>{item.label} </span>
                                        {item.description}
                                    </li>
                                    
                                ))}
                            </ul>
                        )}
                        {answer && (
                            <p className='font-light text-justify leading-[21px] md:leading-[27px] text-sm md:text-lg mt-4 text-grayFour'>
                                {answer}
                            </p>
                        )}
                    </div>
                )}
                
            </div>
        </div>
    )
}

export default FaqItem;