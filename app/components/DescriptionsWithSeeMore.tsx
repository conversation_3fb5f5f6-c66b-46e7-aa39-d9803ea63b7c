'use client';

import { useState } from 'react';
import Downward from '@/app/assets/svg/downward';

interface DescriptionSeeMoreProps {
    description: string;
    className?: string;
}

const DescriptionsWithSeeMore: React.FC<DescriptionSeeMoreProps> = ({ 
    description, 
    className 
}) => {
    const [seeMore, setSeeMore] = useState<boolean>(false);

    const toggleSeeMore = () => {
        setSeeMore((prev) => !prev);
    };

    return (
        <div className={`font-normal text-[13px] ${className} leading-[19px] text-graySix `}>
            <p className={`${seeMore ? 'line-clamp-none':'line-clamp-4'}`}>
                {description}
            </p>
            <button 
                onClick={toggleSeeMore} 
                className='flex gap-1 items-center text-[13px] pt-3 leading-5 font-semibold text-primaryColor'
            >
                <span>See more</span>
                <Downward />
            </button>
        </div>
    );
};

export default DescriptionsWithSeeMore;
