import React from 'react';
import { PaginationProps } from '@/types';
import ArrowLeft from '@/app/assets/svg/ArrowLeft';
import PaginationArrowRight from '@/app/assets/svg/pagination-arrow-right';


const Pagination: React.FC<PaginationProps> = ({
    currentPage,
    totalPages,
    onPageChange,
}) => {
    const renderPagination = () => {
        const pages = [];

        if (totalPages <= 5) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            if (currentPage <= 3) {
                pages.push(1, 2, 3, '...', totalPages);
            } else if (currentPage > totalPages - 3) {
                pages.push(
                    1,
                    '...',
                    // totalPages - 3,
                    totalPages - 2,
                    totalPages - 1,
                    totalPages
                );
            } else {
                pages.push(
                    1,
                    '...',
                    currentPage - 1,
                    currentPage,
                    currentPage + 1,
                    '...',
                    totalPages
                );
            }
        }

        return pages.map((page, index) => (
            <button
                key={index}
                onClick={() => typeof page === 'number' && onPageChange(page)}
                className={`py-2.5 px-4 text-sm font-medium rounded-[8px] ${
                    currentPage === page
                        ? 'bg-primaryColor/15 text-primaryColor'
                        : typeof page === 'number'
                        ? 'text-grayFour'
                        : 'text-grayFour cursor-default'
                }`}
                disabled={typeof page !== 'number'}
            >
                {page}
            </button>
        ));
    };
    return (
        <div className="flex justify-between items-center py-3.5">
            <button
                onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-3.5 py-2 rounded-[8px] flex items-center gap-2 bg-white border border-[#1E62E033] drop-shadow-3xl ${
                    currentPage === 1
                        ? 'text-grayThree'
                        : 'text-primaryColor'
                }`}
            >
                <ArrowLeft />
                <span className="text-sm font-semibold md:block hidden">
                    Previous
                </span>
            </button>
            <p className="text-sm text-gray-700">{renderPagination()}</p>
            <button
                onClick={() =>
                    currentPage < totalPages && onPageChange(currentPage + 1)
                }
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-[8px] flex items-center gap-2 bg-white border border-[#1E62E033] drop-shadow-3xl ${
                    currentPage === totalPages
                        ? 'text-grayThree'
                        : 'text-primaryColor'
                }`}
            >
                <span className="text-sm font-semibold md:block hidden">
                    Next
                </span>
                <PaginationArrowRight />
            </button>
        </div>
    );
};

export default Pagination;
