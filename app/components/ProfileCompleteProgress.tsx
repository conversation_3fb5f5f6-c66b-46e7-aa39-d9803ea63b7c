'use client';

import React from 'react'
import { CircularProgress } from './CircularProgress';

type ProfileCompleteProgressProps = {
    progressValue: number;
};

const ProfileCompleteProgress: React.FC<ProfileCompleteProgressProps> = ({ 
    progressValue 
}) => {
    return (
            <div className='flex flex-col gap-[38px] py-[24px] px-[20px]'>
                <h2 className='font-bold text-base leading-5'>Profile Complete</h2>
                <div className="flex justify-center">
                <CircularProgress
                    value={progressValue}
                    size={120}
                    strokeWidth={12}
                    animationDuration={1500}
                />
                </div>
                <div className='flex items-center justify-center gap-5'>
                    <div className='flex items-center gap-2'>
                        <div className='py-[2px] px-[9px] rounded-2xl bg-primaryColor'></div>
                        <div className='text-grayFour font-normal text-xs leading-[14px]'>Complete</div>
                    </div>
                    <div className='flex items-center gap-2'>
                        <div className='py-[2px] px-[9px] rounded-2xl bg-primaryOne'></div>
                        <div className='text-grayFour font-normal text-xs leading-[14px]'>Incomplete</div>
                    </div>
                </div>
            </div>
    )
}

export default ProfileCompleteProgress;
