'use client';

import Undo from '@/app/assets/svg/undo';
import Redo from '@/app/assets/svg/redo';
import Bold from '@/app/assets/svg/Bold';
import Quote from '@/app/assets/svg/quote';
import { type Editor } from '@tiptap/react';
import Italic from '@/app/assets/svg/Italic';
import { Button } from '@/components/ui/button';
import Subscript from '@/app/assets/svg/subscript';
import React, { useCallback, useState } from 'react';
import Superscript from '@/app/assets/svg/superscript';
import ListNumbered from '@/app/assets/svg/list-numbered';
import ListBulleted from '@/app/assets/svg/list-bulleted';
import Strikethrough from '@/app/assets/svg/Strikethrough';
import {
    Heading2,
    Heading1,
    Heading3,
} from 'lucide-react';

type Props = {
    editor: Editor | null;
    content: string;
};

type PopupProps = {
    onSave: (url: string) => void;
    onCancel: () => void;
    initialUrl?: string;
};

const LinkPopup = ({ onSave, onCancel, initialUrl }: PopupProps) => {
    const [url, setUrl] = useState(initialUrl || '');

    return (
        <div 
            onClick={onCancel} 
            className="fixed inset-0 flex justify-center items-center z-10 py-5"
        >
            <div 
                onClick={(e) => e.stopPropagation()}
                className="bg-white drop-shadow-[0px_2px_4px_#0000001F] p-4 rounded-lg w-[40%]"
            >
                <input
                    type="text"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    className="w-full border border-secondaryColor/20 rounded py-1 px-2 mb-2 focus:outline-none text-sm "
                    placeholder="https://example.com"
                />
                <div className="flex justify-end gap-1">
                    <Button
                        onClick={onCancel}
                        className="text-grayFour px-4 py-2 rounded-[50px] mt- focus:outline-none hover:bg-grayFour duration-500 hover:text-white"
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={() => onSave(url)}
                        className="text-primaryColor px-4 py-2 rounded-[50px] mt- focus:outline-none hover:bg-primaryColor duration-500 hover:text-white"
                    >
                        Save
                    </Button>
                </div>
            </div>
        </div>
    );
};

const Toolbar = ({ editor }: Props) => {

      // const setLink = useCallback(() => {
      //     const previousUrl = editor?.getAttributes('link').href
      //     const url = window.prompt('URL', previousUrl)
    
      //     // cancelled
      //     if (url === null) {
      //         return
      //     }
    
      //     // empty
      //     if (url === '') {
      //         editor?.chain().focus().extendMarkRange('link').unsetLink()
      //         .run()
    
      //         return
      //     }
    
      //     // update link
      //     try {
      //         editor?.chain().focus().extendMarkRange('link').setLink({ href: url })
      //         .run()
      //     } catch (e) {
      //         if (e instanceof Error) {
      //             alert(e.message);
      //         } else {
      //             alert('An unknown error occurred');
      //         }
      //     }
      // }, [editor])

        const [showPopup, setShowPopup] = useState(false);

        const setLink = useCallback(() => {
            const previousUrl = editor?.getAttributes('link').href;
    
            setShowPopup(true);
        }, [editor]);
    
        const saveLink = (url: string) => {
            setShowPopup(false);
    
            if (!url) {
                editor?.chain().focus().extendMarkRange('link').unsetLink().run();
                return;
            }
    
            try {
                editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
            } catch (error) {
                alert(error instanceof Error ? error.message : 'An unknown error occurred');
            }
        };
    
        const cancelLink = () => {
            setShowPopup(false);
        };

    if (!editor) {
        return null;
    }
    return (
        <div className="bg-white px-4 py-3 border-t border-l border-r border-grayOne rounded-tl-md rounded-tr-md flex justify-between items-center gap-5 w-full flex-wrap">
            <div className="flex justify-start items-center w-full flex-wrap ">
                <div className='pr-2.5 space-x-2.5'>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().toggleBold().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('bold')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Bold className="w-[18px] h-[18px]" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().toggleItalic().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('italic')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Italic className="w-[18px] h-[18px]" />
                    </button>

                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().toggleStrike().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('strike')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Strikethrough className="w-[18px] h-[18px]" />
                    </button>
                </div>
                <span className='h-4 w-px bg-grayOne'></span>
                <div className='px-2.5 space-x-2.5'>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor
                                .chain()
                                .focus()
                                .toggleHeading({ level: 1 })
                                .run();
                        }}
                        className={` transition-colors rounded-lg
                ${
                    editor.isActive('heading', { level: 1 })
                        ? 'bg-tertiary p-1 text-white '
                        : 'text-grayFive p-1'
                }
              `}
                    >
                        <Heading1 className="w-[18px] h-[18px]" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor
                                .chain()
                                .focus()
                                .toggleHeading({ level: 2 })
                                .run();
                        }}
                        className={` transition-colors rounded-lg
                ${
                    editor.isActive('heading', { level: 2 })
                        ? 'bg-tertiary p-1 text-white '
                        : 'text-grayFive p-1'
                }
              `}
                    >
                        <Heading2 className="w-[18px] h-[18px]" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor
                                .chain()
                                .focus()
                                .toggleHeading({ level: 3 })
                                .run();
                        }}
                        className={` transition-colors rounded-lg
                ${
                    editor.isActive('heading', { level: 3 })
                        ? 'bg-tertiary p-1 text-white '
                        : 'text-grayFive p-1'
                }
              `}
                    >
                        <Heading3 className="w-[18px] h-[18px]" />
                    </button>
                </div>
                <span className='h-4 w-px bg-grayOne'></span>
                <div className='px-2.5 space-x-2.5'>
                    <button
                        onClick={() => 
                            editor.chain().focus().toggleBulletList().run()
                        }
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('bulletList')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <ListBulleted className="w-[18px] h-[18px]" />
                    </button>
                    <button
                        onClick={() => editor.chain().focus().toggleOrderedList().run()}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('orderedList')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <ListNumbered className="w-[18px] h-[18px]" />
                    </button>
                </div>
                <span className='h-4 w-px bg-grayOne'></span>
                <div className='px-2.5 space-x-2.5'>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().toggleBlockquote().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('blockquote')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Quote className="w-[18px] h-[18px]" />
                    </button>
                    {/* <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().setCode().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('code')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Code className="w-[18px] h-[18px]" />
                    </button> */}
                </div>
                <span className='h-4 w-px bg-grayOne'></span>
                <div className='px-2.5 space-x-2.5'>
                    <button
                        onClick={() =>
                            editor.chain().focus().toggleSuperscript().run()
                        }
                        className={` transition-colors rounded-lg
                          ${
                              editor.isActive('superscript')
                                  ? 'bg-tertiary p-1 text-white '
                                  : 'text-grayFive p-1'
                          }
                        `}
                    >
                        <Superscript className="w-[18px] h-[18px]" />
                    </button>
                    <button
                        onClick={() =>
                            editor.chain().focus().toggleSubscript().run()
                        }
                        className={` transition-colors rounded-lg
                          ${
                              editor.isActive('subscript')
                                  ? 'bg-tertiary p-1 text-white '
                                  : 'text-grayFive p-1'
                          }
                        `}
                    >
                        <Subscript className="w-[18px] h-[18px]" />
                    </button>
                </div>
                <span className='h-4 w-px bg-grayOne'></span>
                <div className='px-2.5 space-x-2.5'>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().undo().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('undo')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Undo className="w-[18px] h-[18px]" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            editor.chain().focus().redo().run();
                        }}
                        className={` transition-colors rounded-lg
                            ${
                                editor.isActive('redo')
                                    ? 'bg-tertiary p-1 text-white '
                                    : 'text-grayFive p-1'
                            }
                        `}
                    >
                        <Redo className="w-[18px] h-[18px]" />
                    </button>
                </div>
                <span className='h-4 w-px bg-grayOne'></span>
                <div className='px-2.5 space-x-2.5'>
                    <button
                        onClick={setLink}
                        className={`transition-colors text-sm rounded-lg ${
                            editor.isActive('link') ? 'bg-blue-500 text-white p-1' : 'text-gray-500 p-1'
                        }`}
                    >
                        Set Link
                    </button>
                    <button
                        onClick={() => editor.chain().focus().unsetLink().run()}
                        disabled={!editor.isActive('link')}
                        className={`transition-colors text-sm rounded-lg ${
                            editor.isActive('link') ? 'text-red-500' : 'text-gray-400 cursor-not-allowed'
                        }`}
                    >
                        Unset Link
                    </button>
                </div>
                {showPopup && (
                <LinkPopup
                    onSave={saveLink}
                    onCancel={cancelLink}
                    initialUrl={editor?.getAttributes('link').href}
                />
            )}
            </div>
        </div>
    );
};

export default Toolbar;
