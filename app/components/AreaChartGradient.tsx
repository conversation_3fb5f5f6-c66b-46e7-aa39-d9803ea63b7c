'use client';

import { 
    CartesianGrid, 
    Legend, 
    Line, 
    LineChart, 
    XAxis, 
    YAxis 
} from 'recharts';

import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';

interface LineData {
  label: string;
  color: string;
  dataKey: string;
  strokeWidth?: number;
}

interface ChartDataPoint {
  [key: string]: string | number;
}

interface AreaChartGradientProps {
    chartContainerClassName?: string;
    title: string;
    data: ChartDataPoint[];
    lines: LineData[];
    xAxisKey: string;
    xAxisFormatter?: (value: any) => string;
    className?: string;
    cardClassName?: string;
    showLegends?: boolean;
    xAxisHeight?: number;
}

export function AreaChartGradient({ 
    chartContainerClassName = 'max-h-[320px]',
    data, 
    title = 'Chart',
    lines,
    xAxisKey,
    xAxisFormatter = (value) => String(value).slice(0, 3),
    className,
    cardClassName,
    showLegends= true,
    xAxisHeight= 20
}: AreaChartGradientProps) {
    // Dynamically build chart config from lines
    const chartConfig: ChartConfig = {};
    lines.forEach(line => {
        chartConfig[line.dataKey] = {
            label: line.label,
            color: line.color,
        };
    });

    return (
        <Card className={`drop-shadow-none shadow-none border-none space-y-[30px] rounded-[20px] ${cardClassName || ''}`}>
            <CardHeader className="pb-0">
                <CardTitle className="flex md:flex-row flex-col gap-4 justify-between">
                    <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                        <h2>{title}</h2>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <ChartContainer 
                    config={chartConfig}
                    className={`w-full rounded-[20px] ${chartContainerClassName || ''}`}
                >
                    <LineChart
                        accessibilityLayer
                        data={data}
                        margin={{
                            left: 12,
                            right: 12,
                        }}
                        className={className}
                    >
                        <CartesianGrid vertical={false} />
                        <YAxis
                            axisLine={false}
                            tickLine={false}
                            tickMargin={30}
                            textAnchor="left"
                        />
                        <XAxis
                            dataKey={xAxisKey}
                            tickLine={false}
                            axisLine={false}
                            tickMargin={8}
                            angle={-30}
                            textAnchor='end'
                            height={xAxisHeight}
                            // tickFormatter={xAxisFormatter}
                        />
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent />}
                        />
                        {lines.map((line, index) => (
                            <Line
                                key={index}
                                dataKey={line.dataKey}
                                type="monotone"
                                stroke={line.color}
                                strokeWidth={line.strokeWidth || 5}
                                dot={false}
                                activeDot={{ r: 6,
                                    stroke: 'white', 
                                    strokeWidth: 2
                                 }}
                                radius={40}
                                
                                // className={`drop-shadow-[0px_4px_4px_rgba(${line.color.replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(',')},${index === 0 ? '0.25' : '0.1'})]`}
                                className='drop-shadow-xl'
                                
                            />
                        ))}
                        {showLegends && 
                            <Legend 
                                verticalAlign="bottom" 
                                height={50} 
                                iconType='circle' 
                                iconSize={10} 
                                wrapperStyle={{bottom:'-20px'}} 
                            />
                        }
                    </LineChart>
                </ChartContainer>
            </CardContent>
        </Card>
    );
}