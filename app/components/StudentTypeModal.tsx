'use client';

import Image from 'next/image';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import React, { useEffect, useState } from 'react';
import DialogIcon from '@/app/assets/svg/student-select-modal.svg';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from '@/components/ui/dialog';

const SelectStudentTypeModal = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [studentType, setStudentType] = useState('');

    useEffect(() => {
        setIsOpen(true);
    }, []);

    const handleConfirm = () => {
        // console.log(`Selected Student Type: ${studentType}`);
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className='md:max-w-[420px] max-w-[335px] pb-6 px-6 pt-[68px] rounded-xl gap-0 mx-auto '>
                <DialogHeader>
                <div className='flex flex-col gap-6 items-center'>
                    <Image
                        src={DialogIcon}
                        alt='dialog icon'
                    />
                    <div>
                        <DialogTitle className='text-center text-lg leading-[21px] font-semibold text-graySix mb-[14px]'>
                            Select Student Type
                        </DialogTitle>
                        <DialogDescription className='text-center font-normal text-base leading-5 text-graySix'>
                            Choose the category that best describes you.
                        </DialogDescription>
                    </div>
                </div>
                </DialogHeader>
                <RadioGroup
                    value={studentType}
                    onValueChange={setStudentType}
                    className='flex pt-8 justify-center gap-8'
                >
                    <div className='flex items-center gap-1.5'>
                        <RadioGroupItem
                            value='new-enrollment'
                            className='flex items-center space-x-3'
                            id='new-enrollment'
                        >
                        </RadioGroupItem>
                        <Label htmlFor='new-enrollment' className='text-base font-normal text-graySix cursor-pointer'>
                            New Enrollment
                        </Label>
                    </div>
                    <div className='flex items-center gap-1.5'>
                        <RadioGroupItem
                            value='transfer'
                            className='flex items-center space-x-3'
                            id='transfer'
                        >
                        </RadioGroupItem>
                        <Label htmlFor='transfer' className='text-base font-normal text-graySix cursor-pointer'>
                            Transfer
                        </Label>
                    </div>
                </RadioGroup>
                <p className='text-xs font-normal leading-[18px] text-center py-[36px] text-grayFour'>
                    A New Enrollment Student is one starting their academic journey at an institution for the first time, without transferring credits.
                </p>
                <div className='flex w-full justify-center space-x-3'>
                <Button
                    variant='outline'
                    onClick={() => setIsOpen(false)}
                    className='w-1/2 py-2.5 text-base font-semibold bg-white text-grayFive rounded-[8px]'
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleConfirm}
                    className='w-1/2 py-2.5 text-base font-semibold bg-primaryColor text-white rounded-[8px] hover:bg-tertiary'
                    disabled={!studentType}
                >
                    Confirm
                </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default SelectStudentTypeModal;
