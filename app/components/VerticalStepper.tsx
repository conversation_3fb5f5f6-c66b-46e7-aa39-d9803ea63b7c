import Image from 'next/image';
import { Download } from 'lucide-react';
import { VerticalStepperProps } from '@/types';
import RightIcon from '@/app/assets/svg/righticon';

const VerticalStepper: React.FC<VerticalStepperProps> = ({ steps }) => {
    return (
        <>
            <ol className=' overflow-hidden space-y-8'>
                {steps.map((step, index) => (

                <li key={index} className='relative flex-1  h-full after:bg-primaryColor '>
                    <div className='flex w-full'>
                        <p className='w-[15%] text-base leading-5 text-[#465668] hidden md:block'>{step.date}</p>
                        <div className='w-[10%] grid grid-flow-row'>
                            <div className='relative flex flex-col items-center '>
                                <div
                                    className={`w-5 h-5 flex justify-center items-center rounded-full border ${
                                    step.status === 'completed'
                                        ? 'bg-primaryColor border-primaryColor text-white'
                                        : step.status === 'active'
                                        ? 'bg-primaryColor border-primaryColor'
                                        : step.status === 'regular'
                                        ? 'bg-white border-grayTwo'
                                        : 'bg-[#FF3B30]'
                                    }`}
                                >
                                    {
                                        step.status === 'completed'? <RightIcon /> :
                                        step.status === 'active'? <div className='w-[8px] h-[8px] rounded-full bg-white'></div> :
                                        step.status === 'regular'? <div className='w-[8px] h-[8px] rounded-full bg-grayThree'></div> :
                                        <div className='w-[8px] h-[8px] rounded-full bg-[#FF3B30]'></div>
                                    }
                                </div>
                                {index !== steps.length - 1 && (
                                    <div
                                    className={`absolute top-[26px] w-px h-full ${
                                        step.status === 'completed' ? 'bg-primaryColor' : 'bg-gray-300'
                                    }`}
                                    ></div>
                                )}
                            </div>
                        </div>
                        <div className='md:w-[75%] w-[90%]'>
                            <p className='text-base leading-5 text-[#465668] md:hidden block pb-5'>{step.date}</p>
                            <div className={`bg-white ${step.content ? 'border border-[#CFE0FFCC] p-4 rounded-xl':''} `}>
                                <div className='flex flex-col gap-2.5 pb-10'>
                                    <h3 className='text-base leading-5 font-normal text-tertiary flex justify-between'>
                                        {step.title}
                                        {step.iconUrl ? (
                                        <div className='p-[5px] bg-primaryOne rounded-[8px] h-fit'>
                                            <Image
                                                src={step.iconUrl}
                                                alt='Icon'
                                            />
                                        </div>
                                        ) : (
                                            null
                                        )}
                                    </h3>
                                    <p className='text-base leading-5 font-semibold text-grayFive'>{step.update}</p>
                                    <p className='text-sm text-grayThree'>{step.content}</p>
                                </div>
                                <div>
                                    {step.link && (
                                        <div className='flex justify-between flex-wrap md:gap-0 gap-5'>
                                            <div className='text-sm text-grayThree space-x-3'>
                                                <span>
                                                    Apply Proof Link:
                                                </span>
                                                <a
                                                    href={step.link}
                                                    target='_blank'
                                                    rel='noopener noreferrer'
                                                    className='text-sm leading-5 text-blue-600 hover:text-blue-700'
                                                >
                                                    {step.linkText}
                                                </a>
                                            </div>
                                            <div className='flex items-center gap-1 py-1 px-2 text-tertiary border-tertiary bg-primaryOne text-xs leading-[18px] border rounded-2xl'>
                                                {step.file} 
                                                <Download size={12} />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                ))}
            </ol>
        </>
    )
}

export default VerticalStepper
