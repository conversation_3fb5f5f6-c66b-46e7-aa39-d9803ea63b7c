import Nav from './Nav';
import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { AsideProps } from '@/types';
import ApplyGoalLogo from '@/app/assets/svg/ApplyGoalLogo';
import ApplyGoalLogoSmall from '@/app/assets/svg/ApplyGoalLogoSmall';

const Aside: React.FC<AsideProps> = ({ 
    isExpanded, 
    expandSidbar, 
    toggleSidebar
}) => {
    return (
        <aside
            className={cn('bg-white transition-all h-screen overflow-hidden duration-300 drop-shadow-[0_1px_4px_rgba(0,0,0,0.2)]',
            isExpanded || toggleSidebar ? 'w-64' : 'w-20')}
            onMouseEnter={toggleSidebar ? undefined : expandSidbar}
            onMouseLeave={toggleSidebar ? undefined : expandSidbar}
        >
            <div className='flex items-center justify-center py-12'>
                <Link href={'/'} className='flex'>
                    {isExpanded || toggleSidebar ? (
                        <>
                        <ApplyGoalLogo />
                        </>
                    ) : (
                        <ApplyGoalLogoSmall />
                    )}
                </Link>
            </div>

            <Nav 
                toggleSidebar={toggleSidebar} 
                isExpanded={isExpanded} 
            />
        </aside>
    )
}

export default Aside