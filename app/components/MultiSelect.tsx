import React from 'react';
import InputField from './InputField';
import { MultiSelectProps } from '@/types';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Select,
    SelectContent,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const MultiSelect: React.FC<MultiSelectProps> = ({
    label,
    options,
    onChange,
    selectedValues,
    placeholder = 'Select options',
}) => {
    const toggleOption = (value: string) => {
    const newSelectedValues = selectedValues.includes(value)
      ? selectedValues.filter((item) => item !== value)
      : [...selectedValues, value];

    onChange(newSelectedValues);
  };

  return (
    <div className='flex flex-col'>
        {label && (
            <Label className="font-medium text-sm text-gray-500 mb-1.5">
                {label}
            </Label>
        )}
        <Select>
            <SelectTrigger className='bg-white w-full'>
                <SelectValue>
                    {selectedValues.length > 0
                        ? selectedValues
                            .map(
                            (value) => options.find((option) => option.value === value)?.label
                            )
                            .join(', ')
                        : placeholder}
                </SelectValue>
            </SelectTrigger>
            <SelectContent>
                {options.map((option) => (
                    <div
                        key={option.value}
                        className={`flex hover:bg-primaryOne rounded items-center space-x-2 mb-0.5 px-3 py-2 cursor-pointer 
                            ${selectedValues.includes(option.value) ? 'bg-primaryOne' : ''}`}
                        onClick={() => toggleOption(option.value)}
                    >
                        <Checkbox checked={selectedValues.includes(option.value)} />
                        <span>{option.label}</span>
                    </div>
                ))}
                {/* {selectedValues[0] === 'select_range' && (
                    <div className='flex gap-2 p-2.5'>
                        <InputField 
                            id='min'
                            //onChange={(e) => setName(e.target.value)}
                            placeholder='38,760' 
                            type='number' 
                            // errorMessage={'!name.trim() && error'}
                            label='min' 
                            className="max-w-32"
                        />
                        <InputField 
                            id='max'
                            //onChange={(e) => setName(e.target.value)}
                            placeholder='938,760' 
                            type='number' 
                            // errorMessage={'!name.trim() && error'}
                            label='max' 
                            className="max-w-32"
                        />
                    </div>
                )} */}
            </SelectContent>
        </Select>
    </div>
  );
};

export default MultiSelect;
