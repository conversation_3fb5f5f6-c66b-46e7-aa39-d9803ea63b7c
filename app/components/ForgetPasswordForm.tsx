'use client';

import { z } from 'zod';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { zodResolver } from '@hookform/resolvers/zod';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { useForgetPasswordMutation } from '@/lib/redux/api/authApi';
import { useToast } from '@/hooks/use-toast';

const formSchema = z.object({
    email: z.string().email({
        message: 'Please enter a valid email address.',
    }),
});

const ForgetPasswordForm = () => {
    const [forgetPassword, { isLoading }] = useForgetPasswordMutation();
    const { toast } = useToast();
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
        },
    });

    async function onSubmit(values: z.infer<typeof formSchema>) {        
        try {
            const res = await forgetPassword(values).unwrap();
            if (res.success) {
                toast({
                    title: 'Success',
                    description: 'Please check your email for Password reset link  ',
                });
            }
            form.reset();
        } catch (err: any) {
            const message = err?.data?.message || 'Failed to send reset email.';
            toast({
                title: 'Error',
                description: message,
                variant: 'destructive',
            });
        }
    }

    return (
        <div>
            <Form {...form}>
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className='space-y-5'
                >
                    <FormField
                        control={form.control}
                        name='email'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Email
                                </FormLabel>
                                <FormControl>
                                    <Input
                                        className='flex w-full rounded-[50px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                        placeholder='Enter your email'
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <Button
                        type='submit'
                        className='w-full bg-primaryColor rounded-[50px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white'
                        disabled={isLoading}
                    >
                        {isLoading ? 'Sending...' : 'Reset Password'}
                    </Button>
                </form>
            </Form>
            <div className='flex justify-center gap-1 mt-8'>
                <span className='text-sm font-normal text-grayFive'>
                    Back to
                </span>
                <Link
                    className='text-sm font-semibold text-primaryColor'
                    href={'/login'}
                >
                    Log in
                </Link>
            </div>
        </div>
    );
};

export default ForgetPasswordForm;
