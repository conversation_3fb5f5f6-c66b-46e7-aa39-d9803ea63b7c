'use client';

import React, { useState } from 'react';
import { SelectFieldProps } from '@/types';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const SelectField: React.FC<SelectFieldProps> = ({
    value,
    options,
    sublabel,
    onChange,
    className,
    label = '',
    placeholder = '',
    defaultValue = '',
}) => {
    return (
        <div>
            {label && (
                <Label className='flex font-medium text-sm text-grayFive mb-1.5 items-center'>
                    {label}
                    <span className='text-xs font-normal text-grayFive ml-2'>
                        {sublabel}
                    </span>
                </Label>
            )}
            <Select 
                value={value} 
                onValueChange={(newValue) => onChange?.(newValue)}
                defaultValue={defaultValue}
            >
                <SelectTrigger className={`bg-white ${className} focus-within:outline-none  focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60`}>
                    <SelectValue 
                        className='font-medium text-sm text-grayFive mb-1.5 placeholder:text-grayThree' 
                        placeholder={placeholder}
                    />
                </SelectTrigger>
                <SelectContent>
                    {options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                            {option.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
    };

export default SelectField;
