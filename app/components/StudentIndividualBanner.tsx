import { StudentIndividualBannerProps } from '@/types'

const StudentIndividualBanner: React.FC<StudentIndividualBannerProps> = ({ 
    date, 
    heading, 
    description, 
    BannerImage 
}) => {
    return (
        <div className=' md:pl-12 pl-[35px] md:pr-12 pr-[70px] flex justify-between w-full rounded-3xl bg-gradient-to-r from-tertiary to-[#377DFF80] drop-shadow-4xl'>
            <div className='flex flex-col justify-between gap-[60px] md:py-11 py-8'>
                <span className='font-normal md:text-base text-sm md:leading-5 leading-[17px] text-white/75'>{date}</span>
                <div className='space-y-2 text-white'>
                    <h1 className='font-semibold md:text-[32px] text-[26px] md:leading-[38px] leading-8'>Welcome, {heading}!</h1>
                    <p className='font-normal md:text-base text-sm leading-5 text-white/80'>{description}</p>
                </div>
            </div>

            <div className='md:block hidden'>
                {BannerImage}
            </div>
        </div>
    )
}

export default StudentIndividualBanner;