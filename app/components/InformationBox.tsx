import React from 'react';
import { InformationBoxProps } from '@/types';
import { Separator } from '@/components/ui/separator';

const InformationBox: React.FC<InformationBoxProps> = ({ 
    data 
}) => {
    return (
        <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
            {data.degree ? (
                <h3 className="text-lg font-semibold text-gray-700">
                    {data.degree}
                </h3>) : (
                     <h3 className="text-lg font-semibold text-gray-700">
                     {data.title}
                 </h3>
                )}
            
            <Separator className='bg-[#1952BB] bg-opacity-15 my-4' />
            
            {data.data && data.data.map((item, index) => (
                <div className='flex mb-4' key={index}>
                    <span className='font-normal text-base leading-5 text-graySix w-1/3'>{item.label}</span>
                    <span className='font-normal text-base leading-5 text-graySix w-2/3'>: {item.value}</span>
                </div>
            ))}
            {data.info && data.info.map((item, index) => (
                <p className='mb-3 font-normal text-base leading-5 text-graySix' key={index}>{item}</p>
            ))}
        </div>
    )
}

export default InformationBox