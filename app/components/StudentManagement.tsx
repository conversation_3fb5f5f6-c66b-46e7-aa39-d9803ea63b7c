import React, { useState } from 'react';
import Personal from './Personal';
import StudentDetailView from './StudentDetailView';
import { useStudentSave } from '@/hooks/useStudentSave';

interface StudentManagementProps {
  mode: 'create' | 'edit' | 'view';
  studentId?: string | number;
  initialData?: any;
  onModeChange?: (mode: 'create' | 'edit' | 'view') => void;
}

const StudentManagement: React.FC<StudentManagementProps> = ({
  mode,
  studentId,
  initialData,
  onModeChange
}) => {
  const [currentStudentId, setCurrentStudentId] = useState<string | number | undefined>(studentId);
  const { saveStudentData, isLoading } = useStudentSave();

  const handleSaveSuccess = (data: any) => {
    console.log('Student saved successfully:', data);
    if (data?.studentId) {
      setCurrentStudentId(data.studentId);
    }
    // Optionally switch to view mode after successful save
    if (onModeChange) {
      onModeChange('view');
    }
  };

  const renderContent = () => {
    switch (mode) {
      case 'create':
      case 'edit':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">
                {mode === 'create' ? 'Create New Student' : 'Edit Student Information'}
              </h2>
              {mode === 'edit' && onModeChange && (
                <button
                  onClick={() => onModeChange('view')}
                  className="text-indigo-600 hover:text-indigo-800 font-medium"
                >
                  View Profile
                </button>
              )}
            </div>
            
            <Personal
              studentId={currentStudentId}
              initialData={initialData}
              onSave={handleSaveSuccess}
            />
          </div>
        );

      case 'view':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Student Profile</h2>
              {onModeChange && currentStudentId && (
                <button
                  onClick={() => onModeChange('edit')}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition"
                >
                  Edit Information
                </button>
              )}
            </div>
            
            {currentStudentId ? (
              <StudentDetailView
                studentId={currentStudentId}
                showPersonalInfo={true}
                showSocialLinks={true}
              />
            ) : (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-600">No student selected for viewing</p>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {renderContent()}
    </div>
  );
};

export default StudentManagement;
