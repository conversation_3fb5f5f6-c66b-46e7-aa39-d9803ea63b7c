import React from 'react';
import Image from 'next/image';
import { requirementCardsData, VerticalSteps } from '@/common';
import VerticalStepper from './VerticalStepper';
import RequirementCard from './RequirementCard';
// import ApplicantsMessage from './ApplicantsMessage';
import NotesIcon from '@/app/assets/svg/speaker_notes.svg';
import progressIcon from '@/app/assets/svg/progress-record.svg';
// import ApplyGoalLogo from '@/app/assets/svg/ApplyGoalLogoSmall';
import ApplicationStudentProfileNotes from './ApplicationStudentProfileNotes';
import AplicationRequirementIcon from '@/app/assets/svg/application-requirement.svg';
import { 
    Tabs, 
    TabsContent, 
    TabsList, 
    TabsTrigger 
} from '@/components/ui/tabs';
import FileReview from '../assets/svg/FileReview';
import FileReviewComponent from './FileReviewComponent';

const ApplicantTab = () => {

    // const handleResponseSubmit = (response: string) => {
    //     console.log(`User's response:`, response);
    //     alert('Response submitted successfully!');
    // };

    // const pdfFileUrl = 'https://file-examples.com/storage/fe1b07b09f67bcb9b96354c/2017/10/file-sample_150kB.pdf';
    // const pdfFileUrl = 'assets/demo.pdf'; // Place your PDF file in the `public` directory

    return (
        <div className='bg-white rounded-xl p-4 w-full '>
            <Tabs defaultValue='Application' className='w-full'>
                <TabsList className=' flex flex-col md:flex-row gap-2.5 md:items-center items-start md:justify-between justify-start bg-white md:mb-0 mb-28'>
                    <div className='grid md:grid-cols-3 grid-cols-1 gap-2.5 place-items-start text-primaryColor '>
                        <TabsTrigger value='Application' className='flex gap-2 px-5 py-2.5 data-[state=active]:text-primaryColor data-[state=active]:bg-primaryColor/5 data-[state=active]:shadow-none data-[state=active]:rounded-full data-[state=active]:drop-shadow-5xl '>
                            <Image
                                src={AplicationRequirementIcon}
                                alt='icon'
                            />
                            Application Requirements
                        </TabsTrigger>
                        <TabsTrigger value='Progress' className='flex gap-2 px-5 py-2.5 data-[state=active]:text-primaryColor data-[state=active]:bg-primaryColor/5 data-[state=active]:shadow-none data-[state=active]:rounded-full data-[state=active]:drop-shadow-5xl '>
                            <Image
                                src={progressIcon}
                                alt='icon'
                            />
                            Progress Records
                        </TabsTrigger>
                        <TabsTrigger value='FileReview' className='flex gap-2 px-5 py-2.5 data-[state=active]:text-primaryColor data-[state=active]:bg-primaryColor/5 data-[state=active]:shadow-none data-[state=active]:rounded-full data-[state=active]:drop-shadow-5xl '>
                            <FileReview />
                                File Review
                        </TabsTrigger>
                    </div>
                    <TabsTrigger value='Notes' className='bg-primaryColor/5 text-primaryColor rounded-full data-[state=active]:text-primaryColor data-[state=active]:bg-primaryColor/5 data-[state=active]:shadow-none data-[state=active]:drop-shadow-5xl shadow-none relative flex gap-2 px-5 py-2.5'>
                        <Image
                            src={NotesIcon}
                            alt='icon'
                        />
                            Notes
                        <div className='bg-tertiary w-3 h-3 flex items-center justify-center absolute top-0 right-0 rounded-full'></div>
                    </TabsTrigger>
                </TabsList>
                <TabsContent value='Application'>
                    <div className='py-5'>
                        {/* <div className='container mx-auto space-y-5'>
                            <RequirementCard
                                title='Copy of Passport'
                                description={`Please attach a copy of the applicant's passport - pages that include the applicant's identity information.`}
                                status='Reviewing'
                                required
                                uploadedFiles={['Passport.jpg']}
                                allowUpload
                                file={pdfFileUrl}
                            />
                            <RequirementCard
                                title='English Language Proficiency Test'
                                description={`Please provide a copy of the applicant's English test scores.`}
                                status='Missing'
                                required
                                uploadedFiles={['IELTS.pdf', 'document1.pdf']}
                                allowUpload
                            />
                            <RequirementCard
                                title='Request for Student Loans'
                                description='Please provide details about your student loan requirements.'
                                status='No Status'
                                allowAnswer
                            />
                            <RequirementCard
                                title='Request for Student Loans'
                                description='Please provide details about your student loan requirements.'
                                status='Completed'
                                allowUpload
                            />
                            <RequirementCard
                                title='Request for Student Loans'
                                description='Please provide details about your student loan requirements.'
                                status='Rejected'
                                allowUpload
                                uploadedFiles={['s_statement.pdf']}
                                ResubmissionDate='Sept 12,2024'
                                RejectionNote='This file is incomplete documents or unmet visa requirements.'
                            />
                        </div> */}
                        <div className='container mx-auto space-y-5'>
                            {requirementCardsData.map((card, index) => (
                                <RequirementCard
                                    key={index}
                                    title={card.title}
                                    description={card.description}
                                    status={card.status}
                                    required={card.required}
                                    uploadedFiles={card.uploadedFiles}
                                    allowUpload={card.allowUpload}
                                    allowAnswer={card.allowAnswer}
                                    file={card.file}
                                    ResubmissionDate={card.ResubmissionDate}
                                    RejectionNote={card.RejectionNote}
                                />
                            ))}
                        </div>
                    </div>
                </TabsContent>
                <TabsContent value='Progress' className='py-5'>
                    <VerticalStepper steps={VerticalSteps} />
                </TabsContent>
                <TabsContent value='FileReview'>
                    <FileReviewComponent
                        
                    />
                </TabsContent>
                <TabsContent value='Notes' className='py-5'>
                    {/* <ApplicantsMessage
                        senderImage={<ApplyGoalLogo />}
                        senderName='Applygoal'
                        title='Application Cancelled'
                        message='Please be advised that due to the long period of inactivity of this application, we have cancelled the application and credited the fee, if any, back to your account.'
                        messagetime='Sep 8, 2023, 12:47 PM'
                        onSubmit={handleResponseSubmit} 
                    /> */}
                    <ApplicationStudentProfileNotes />
                </TabsContent>

            </Tabs>
        </div>
    )
}

export default ApplicantTab
