import React from 'react';
import { StudentData } from '@/lib/redux/api/studentApi';
import { useGetStudentQuery } from '@/lib/redux/api/studentApi';
import ArrowOutward from '@/app/assets/svg/ArrowOutward';

interface StudentDetailViewProps {
  studentId: string | number;
  showPersonalInfo?: boolean;
  showSocialLinks?: boolean;
}

const StudentDetailView: React.FC<StudentDetailViewProps> = ({ 
  studentId, 
  showPersonalInfo = true, 
  showSocialLinks = true 
}) => {
  const { data: studentResponse, isLoading, error } = useGetStudentQuery(studentId);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-600">Loading student information...</span>
      </div>
    );
  }

  if (error || !studentResponse?.success) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600">Failed to load student information</p>
      </div>
    );
  }

  const student = studentResponse.data;

  if (!student) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-600">No student data found</p>
      </div>
    );
  }

  const personalInfoItems = [
    { label: 'Full Name', value: `${student.firstName} ${student.lastName}` },
    { label: 'Name in Native Language', value: student.nameInNative },
    { label: 'Email', value: student.email },
    { label: 'Phone', value: student.phone },
    { label: 'Guardian Phone', value: student.guardianPhone },
    { label: 'Date of Birth', value: student.dateOfBirth },
    { label: 'Gender', value: student.gender },
    { label: 'Father\'s Name', value: student.fatherName },
    { label: 'Mother\'s Name', value: student.motherName },
    { label: 'National ID', value: student.nid },
    { label: 'Passport', value: student.passport },
    { label: 'Marital Status', value: student.maritalStatus.status },
    { label: 'Spouse Name', value: student.maritalStatus.spouseName !== 'none' ? student.maritalStatus.spouseName : 'N/A' },
    { label: 'Present Address', value: `${student.presentAddress.address}, ${student.presentAddress.city}, ${student.presentAddress.state}, ${student.presentAddress.country} - ${student.presentAddress.postalCode}` },
    { label: 'Permanent Address', value: `${student.permanentAddress.address}, ${student.permanentAddress.city}, ${student.permanentAddress.state}, ${student.permanentAddress.country} - ${student.permanentAddress.postalCode}` },
    { label: 'Sponsor Name', value: student.sponsor.name },
    { label: 'Sponsor Relation', value: student.sponsor.relation },
    { label: 'Sponsor Phone', value: student.sponsor.phone },
    { label: 'Sponsor Email', value: student.sponsor.email },
    { label: 'Emergency Contact', value: `${student.emergencyContact.firstName} ${student.emergencyContact.lastName} (${student.emergencyContact.relation})` },
    { label: 'Emergency Phone Home', value: student.emergencyContact.phoneHome },
    { label: 'Emergency Phone Mobile', value: student.emergencyContact.phoneMobile },
    { label: 'Preferred Subjects', value: student.preferredSubject.join(', ') },
    { label: 'Preferred Countries', value: student.preferredCountry.join(', ') },
    { label: 'Reference', value: student.reference },
    { label: 'Note', value: student.note },
  ];

  return (
    <div className="space-y-8">
      {showPersonalInfo && (
        <div>
          <h3 className="pb-6 font-semibold text-xl leading-6 text-graySix">Personal Information</h3>
          <div className="rounded-lg border p-5 border-tertiary border-opacity-20">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4">
              {personalInfoItems.map((info, index) => (
                <div key={index} className="flex">
                  <span className="font-normal text-base leading-5 text-graySix w-1/3">
                    {info.label}
                  </span>
                  <span className="font-normal text-base leading-5 text-graySix w-2/3">
                    : <span className="ml-3">{info.value || 'N/A'}</span>
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {showSocialLinks && student.socialLinks && student.socialLinks.length > 0 && (
        <div>
          <h3 className="pb-6 font-semibold text-xl leading-6 text-graySix">Social Links</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {student.socialLinks
              .filter(social => social.platform && social.url)
              .map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex border border-tertiary border-opacity-20 items-center px-5 py-3 rounded-lg transition hover:bg-gray-50"
                >
                  <div className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      {social.platform.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="ml-3.5 flex-1 text-gray-800 font-medium">
                    {social.platform}
                  </span>
                  <ArrowOutward />
                </a>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentDetailView;
