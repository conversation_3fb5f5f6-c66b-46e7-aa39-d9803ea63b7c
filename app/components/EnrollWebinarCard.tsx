import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { EnrollWebinarCardProps } from '@/types';

const EnrollWebinarCard: React.FC<EnrollWebinarCardProps> = ({
    date,
    title,
    endTime,
    enrolled,
    imageSrc,
    startTime,
    totalEnrollee
}) => {
    return (
        <div className="flex flex-col items-center p-4 gap-2">
            <div className="">
                <Image
                    src={imageSrc}
                    alt="Webinar image"
                    className="rounded-lg"
                />
            </div>
            <div className="flex flex-col gap-2.5">
                <h2 className="text-sm leading-[21px] text-graySix font-semibold">
                    {title}
                </h2>
                <div className="font-normal text-[10px] leading-3 text-grayFive flex items-center">
                    <div className="flex -space-x-2">
                        {enrolled.slice(0, 3).map((enrollee, index) => (
                            <Image
                                key={index}
                                src={enrollee}
                                alt='enroller profile'
                                width={20}
                                height={20}
                                className="rounded-full border-2 border-white"
                            />
                        ))}
                    </div>
                    <span className="text-graySix font-semibold text-[12px] leading-[14px] px-1.5">
                        {totalEnrollee}+
                    </span>
                    <span className='text-[12px] font-normal leading-3'>
                        students enrolled in the webinar
                    </span>
                </div>
                <div className="flex justify-between text-grayFour text-[10px] font-medium leading-[10px]">
                    <span>{date}</span>
                    <span>
                        {startTime} - {endTime}
                    </span>
                </div>
                <Button className="bg-primaryColor hover:bg-tertiary text-white rounded-[14px] text-[10px] leading-3 font-medium py-2 px-[18px] border border-primaryColor">
                    Enroll Webinar
                </Button>
            </div>
        </div>
    );
};

export default EnrollWebinarCard;
