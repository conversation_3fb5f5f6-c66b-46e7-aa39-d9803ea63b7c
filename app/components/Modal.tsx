'use client';

import React, { useEffect, useState, ReactNode } from 'react';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogDescription,
} from '@/components/ui/dialog';

interface ModalProps {
    children: (close: () => void) => ReactNode;
    openOnLoad?: boolean;
    trigger?: ReactNode;
    onConfirm?: () => void;
    className?: string;
}

const Modal = ({ children, openOnLoad = false, trigger, onConfirm, className = 'md:max-w-[420px] max-w-[335px]' }: ModalProps) => {
    const [isOpen, setIsOpen] = useState(openOnLoad);

    useEffect(() => {
        if (openOnLoad) {
            setIsOpen(true);
        }
    }, [openOnLoad]);

    const handleClose = () => setIsOpen(false);

    const handleConfirm = () => {
        onConfirm?.();
        handleClose();
    };

    return (
        <>
            {trigger && React.cloneElement(trigger as React.ReactElement, {
                onClick: () => setIsOpen(true)
            })}

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className={`${className}  pb-6 px-6 pt-[68px] rounded-xl gap-0 mx-auto `}>
                    <DialogTitle hidden />
                    <DialogDescription hidden />
                    {children(handleClose)}
                </DialogContent>
            </Dialog>
        </>
    );
};

export default Modal;
