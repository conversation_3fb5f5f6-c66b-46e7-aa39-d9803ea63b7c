'use client';

import ArrowUp from '@/app/assets/svg/arrowUp';
import ArrowDown from '@/app/assets/svg/arrowDown';
import { AccordionWithButtonProps } from '@/types';
import { cn } from '@/lib/utils';
import Download from '../assets/svg/download';
import Upload from '@/app/assets/svg/upload';
import ToolTipIcon from '../assets/svg/ToolTipIcon';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";

const AccordionWithButton:React.FC<AccordionWithButtonProps> = ({
    children,
    label,
    labelClassName,
    subLabel
}) => {
    return (
        <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
                <AccordionTrigger
                    IconClass='!text-graySix' 
                    FrontArrowIcon={ArrowDown}
                    className={cn('text-graySix flex gap-3 items-center p-[26px] hover:no-underline', labelClassName)}
                >
                    {label}
                    {/* <div className='flex items-center gap-3.5 text-sm'>
                        <div className='py-2.5 px-[18px] bg-primaryColor text-white rounded-full flex items-center gap-1'>
                            <Download />
                            Template
                        </div>
                        <div className='py-2.5 px-[18px] bg-primaryOne text-primaryColor border border-primaryColor/20 rounded-full flex items-center gap-1'>
                            <Upload className='w-4 h-4' />
                            CSV
                        </div>
                    </div> */}
                    {subLabel &&
                        <span className='text-xs text-grayThree mt-2'>{subLabel}</span>
                    }
                </AccordionTrigger>
                <AccordionContent className='p-[26px]'>
                    {children}
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    )
}

export default AccordionWithButton
