'use client';

import { z } from 'zod';
import Link from 'next/link';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import EyeIcon from '@/app/assets/svg/visibility';
import { zodResolver } from '@hookform/resolvers/zod';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import {
    Form,
    FormItem,
    FormLabel,
    FormField,
    FormControl,
    FormMessage
} from '@/components/ui/form';

const formSchema = z
    .object({
        name: z.string().min(5, {
            message: 'name must be at least 5 characters.',
        }),
        email: z.string().email({
            message: 'Please enter a valid email address.',
        }),
        organizationName: z
            .string(),
        password: z
            .string()
            .min(8, { message: 'Password must be at least 8 characters.' }),
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords must match.',
        path: ['confirmPassword'],
    });

const UniversitySignup = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const togglePasswordVisibility = () => setShowPassword((prev) => !prev);
    const toggleConfirmPasswordVisibility = () =>
        setShowConfirmPassword((prev) => !prev);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            email: '',
            organizationName: '',
            password: '',
            confirmPassword: '',
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        console.log(values);
        return values;
    }

    return (
        <div>
            <Form {...form}>
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                >
                    <FormField
                        control={form.control}
                        name='name'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Name*
                                </FormLabel>
                                <FormControl>
                                    <input
                                        type='text'
                                        className='flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                        placeholder='Enter your name'
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name='email'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5 mt-5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Email*
                                </FormLabel>
                                <FormControl>
                                    <input
                                        type='email'
                                        className='flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                        placeholder='Enter your email'
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name='organizationName'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5 mt-5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Organization Name*
                                </FormLabel>
                                <FormControl>
                                    <input
                                        type='text'
                                        className='flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                        placeholder='Enter your Organization Name'
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name='password'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5 mt-5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Password*
                                </FormLabel>
                                <FormControl>
                                    <div className='relative'>
                                        <input
                                            type={
                                                showPassword ? 'text' : 'password'
                                            }
                                            className='flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                            placeholder="Enter your password"
                                            {...field}
                                        />
                                        <button
                                            type='button'
                                            onClick={togglePasswordVisibility}
                                            className='absolute right-3 top-1/2 transform -translate-y-1/2'
                                        >
                                            { showPassword ? (<VisibilityOff />) : (<EyeIcon className='text-grayThree' />)}
                                        </button>
                                    </div>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name='confirmPassword'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5 mt-5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Confirm Password*
                                </FormLabel>
                                <FormControl>
                                    <div className='relative'>
                                        <Input
                                            type={
                                                showConfirmPassword
                                                    ? 'text'
                                                    : 'password'
                                            }
                                            className='flex w-full rounded-[8px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                            placeholder="Confirm your password"
                                            {...field}
                                        />
                                        <button
                                            type='button'
                                            onClick={toggleConfirmPasswordVisibility}
                                            className='absolute right-3 top-1/2 transform -translate-y-1/2'
                                        >
                                            { showPassword ? (<VisibilityOff />) : (<EyeIcon className='text-grayThree' />)}
                                        </button>
                                    </div>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <Button
                        type='submit'
                        className='w-full bg-primaryColor rounded-[8px]  py-2.5 mt-6 mb-5 hover:bg-tertiary text-white text-base font-semibold'
                    >
                        Create account
                    </Button>
                </form>
            </Form>
            <div className='flex justify-center gap-1 mt-8'>
                <span className='text-sm font-normal text-grayFive'>Already have an account?</span>
                <Link 
                    href={'/login'}
                    className='text-sm font-semibold text-primaryColor' 
                >
                    Log in
                </Link>
            </div>
        </div>
    );
};

export default UniversitySignup;
