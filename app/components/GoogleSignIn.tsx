import { doSocialLogin } from '@/lib/sso';
import { usePathname } from 'next/navigation';
import GoogleIcon from '../assets/svg/GoogleIcon';

const GoogleSignIn = () => {
    const pathName = usePathname();

    return (
        <form action={doSocialLogin}>
            <button 
                type='submit' 
                name='action' 
                value='google' 
                className='flex justify-center gap-3 py-2.5 rounded-[8px] bg-white hover:bg-grayOne border border-tertiary border-opacity-20 text-graySix w-full px-2'
            >
               <span className='w-6 h-6'>
                    <GoogleIcon />
               </span>
                Sign {pathName.includes('login') ? ' in' : ' up'} with Google
            </button>
        </form>
    );
};

export default GoogleSignIn;