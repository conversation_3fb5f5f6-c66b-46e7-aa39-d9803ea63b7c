import React, { useState } from 'react';
import ModalLayout from '../layout/ModalLayout';

interface EditDepartmentModalProps {
    open: boolean;
    onClose: () => void;
    departmentName: string;
    onSubmit: (newName: string) => void;
}

const EditDepartmentModal = ({ open, onClose, departmentName, onSubmit }: EditDepartmentModalProps) => {
    const [name, setName] = useState(departmentName);

    const handleSubmit = () => {
        onSubmit(name);
        onClose();
    };

    return (
        <ModalLayout open={open} onClose={onClose}>
            <div className="w-full">
                <h2 className="font-semibold text-lg leading-7 text-graySix mb-6 border-b border-[#1952BB33] pb-2.5">Department</h2>
                <div className="mb-6">
                    <label className="block text-sm font-medium leading-5 text-grayFive mb-2">Edit Department/Team Name</label>
                    <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full px-3.5 py-2.5 border border-[#1952BB33] rounded-[8px] focus:outline-none focus:ring-1 focus:ring-primaryColor text-grayFive font-normal text-base leading-6"
                        placeholder="Department name"
                    />
                </div>

                <div className="flex justify-between gap-3">
                    <button
                        className="flex-1 py-2.5 rounded-[8px] border border-[#1952BB33] text-grayFive font-medium text-base leading-6"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                    <button
                        className="flex-1 py-2.5 rounded-[8px] bg-primaryColor text-white font-medium text-base leading-6"
                        onClick={handleSubmit}
                    >
                        Submit
                    </button>
                </div>
            </div>
        </ModalLayout>
    );
};

export default EditDepartmentModal;