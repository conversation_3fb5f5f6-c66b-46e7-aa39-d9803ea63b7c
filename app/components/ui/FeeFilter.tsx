'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TutionFeeFilterProps } from '@/types';
import { Slider } from '@/components/ui/slider';
import Keyboard_arrow_down from '@/app/assets/svg/Keyboard_arrow_down';
import {
    Select,
    SelectItem,
    SelectValue,
    SelectContent,
    SelectTrigger,
} from '@/components/ui/select';

const FeeFilter: React.FC<TutionFeeFilterProps> = ({
    label,
    value,
    onChangeValue,
}) => {
    const [range, setRange] = useState<[number, number]>([0, 92000]);
    const [sortOrder, setSortOrder] = useState('');
    const [isRangeOpen, setIsRangeOpen] = useState(false);

    return (
        <div>
            {label && (
                <Label className='flex font-medium text-sm text-grayFive mb-1.5'>
                    {label}
                </Label>
            )}
            <Select>
                <SelectTrigger className='w-full bg-white boder border-primaryColor/20'>
                    <SelectValue
                        placeholder='Select'
                        className='text-grayThree'
                    />
                </SelectTrigger>
                <SelectContent className='p-3'>
                    <SelectItem 
                        className='cursor-pointer p-2 font-normal text-sm leading-none text-grayFive' 
                        value='lowToHigh'
                    >
                        Tuition Fee (Low to High)
                    </SelectItem>
                    <SelectItem 
                        className='cursor-pointer p-2 font-normal text-sm leading-none text-grayFive' 
                        value='highToLow'
                    >
                        Tuition Fee (High to Low)
                    </SelectItem>
                    
                    <button 
                        onClick={() => setIsRangeOpen(!isRangeOpen)}
                        className='bg-primaryOne rounded p-2 cursor-pointer mt-1 w-full'
                    >
                        <div className='flex justify-between items-center'>
                            <span className='font-normal text-sm leading-none text-grayFive'>Select range</span>
                            <Keyboard_arrow_down />
                        </div>
                    </button>
                    {/* {isRangeOpen && (
                        <div className='flex flex-col space-y-2 mt-3'>
                            <Slider
                                min={0}
                                max={92000}
                                step={1000}
                                value={range}
                                onValueChange={(val) => setRange(val as [number, number])}
                                className='w-full cursor-pointer'
                            />
                            <div className='flex justify-between items-center mt-3'>
                                <Input
                                    type='number'
                                    value={range[0]}
                                    onChange={(e) => setRange([+e.target.value, range[1]])}
                                    className='text-center py-2.5 font-normal text-sm leading-[18px] text-grayFive rounded border border-[#1E62E033] w-20 focus:outline-none focus-visible:ring-1 focus-visible:ring-blue-500'
                                />
                                <Input
                                    type='number'
                                    value={range[1]}
                                    onChange={(e) => setRange([range[0], +e.target.value])}
                                    className='text-center py-2.5 font-normal text-sm leading-[18px] text-grayFive rounded border border-[#1E62E033] w-20 focus:outline-none focus-visible:ring-1 focus-visible:ring-blue-500'
                                />
                            </div>
                        </div>
                    )} */}
                </SelectContent>
            </Select>
        </div>
        
    );
};

export default FeeFilter;
