import Plus from '@/app/assets/svg/plus'
import React from 'react'
import InputField from '../../InputField'
import FormSubSection from '../../FormSubSection'
import SelectField from '../../SelectField'
import PhoneNumberInput from '../../PhoneNumberInput'
import InputFieldWithIcon from '../../InputFieldWithIcon'
import EmailInbox from '@/app/assets/svg/emailInbox'
import MultiSelectDropdown from '../../MultiSelectDropdown'

const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' },
];

const UniEnrollementCampusForm = () => {
    return (
            <form action="" className='space-y-[30px]'>
                <FormSubSection heading={'General Info'}>
                    <InputField
                        id="campus_name"
                        type="text"
                        label="Campus Name"
                        className="py-3 "
                    />
                    <MultiSelectDropdown label="Program Level" showSearch />
                    <div className='grid grid-cols-2 gap-6'>
                        <div className='grid grid-cols-1 gap-6'>
                            <InputField
                                id="address"
                                placeholder="Enter addresses"
                                type="text"
                                label="Address"
                                className='py-3'
                            />
                            <div className='grid grid-cols-2 gap-6'>
                                <PhoneNumberInput
                                    label="Contact Number"
                                    className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 rounded-lg w-full"
                                />
                                <InputFieldWithIcon
                                    id="email"
                                    placeholder="Enter campus email"
                                    type="text"
                                    label="Email"
                                    className='py-3 mt-1'
                                    icon={<EmailInbox />}
                                />
                            </div>
                        </div>
                        <div className='grid grid-cols-2 gap-6'>
                            <SelectField
                                label="Country"
                                options={DemoSelectButtonData}
                                className='py-5'
                            />
                            <SelectField
                                label="State"
                                options={DemoSelectButtonData}
                                className='py-5'
                            />
                            <SelectField
                                label="City"
                                options={DemoSelectButtonData}
                                className='py-5'
                            />
                            <InputField
                                id="zip_code"
                                placeholder="Enter postal/zip code"
                                type="text"
                                label="Postal/Zip Code"
                                className='py-3'
                            />
                        </div>
                        
                    </div>
                    
                </FormSubSection>
                <button className='py-2.5 px-4 bg-primaryThree text-primaryColor flex items-center gap-1.5 w-fit rounded-full'>
                    <Plus />
                    Add More
                </button>
                <div className='w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5'>
                    <button className='px-4 py-2 bg-primaryColor rounded-full text-white font-semibold'>
                        Submit
                    </button>
                </div>
            </form>
    )
}

export default UniEnrollementCampusForm
