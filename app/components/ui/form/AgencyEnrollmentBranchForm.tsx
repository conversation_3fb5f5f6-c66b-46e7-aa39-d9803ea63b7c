'use client';

import React from 'react'
import FormSubSection from '../../FormSubSection'
import InputField from '../../InputField'
import SelectField from '../../SelectField'
import AddNewFieldButton from '../../AddNewFieldButton'
import PhoneNumberInput from '../../PhoneNumberInput';
import InputFieldWithIcon from '../../InputFieldWithIcon';
import EmailInbox from '@/app/assets/svg/emailInbox';
import Plus from '@/app/assets/svg/plus';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' }
]

const AgencyEnrollmentBranchForm = () => {
    return (
        <div className='space-y-10'>
            <FormSubSection heading={'Branch Details'}>
                <div className="grid grid-cols-4 gap-6">
                    <div className='col-span-2'>
                        <InputField
                            id="branch_name"
                            placeholder="Enter the agency branch name"
                            type="text"
                            label="Branch Name"
                        />
                    </div>
                    <SelectField
                        label="Country"
                        options={DemoSelectButtonData}
                    />
                    <SelectField
                        label="State"
                        options={DemoSelectButtonData}
                    />
                </div>
                <div className='grid grid-cols-4 gap-6'>
                    <div className='col-span-2'>
                        <InputField
                            id="address"
                            placeholder="Enter the agency branch name"
                            type="text"
                            label="Address"
                        />
                    </div>
                    <SelectField
                        label="State"
                        options={DemoSelectButtonData}
                    />
                    <InputField
                        id="zip_code"
                        type="text"
                        label="Postal/Zip Code"
                    />
                </div>
                <div className='grid grid-cols-2 gap-6'>
                    <PhoneNumberInput
                        label="Primary Contact Number"
                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                    />
                    <InputFieldWithIcon
                        label="Email"
                        placeholder="Email"
                        icon={<EmailInbox />}
                        type="email"
                    />
                </div>
                <button className='py-2.5 px-4 bg-primaryThree text-primaryColor flex items-center gap-1.5 w-fit rounded-full'>
                    <Plus />
                    Add More
                </button>
                <div className='w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5'>
                    <button className='px-4 py-2 bg-primaryColor rounded-full text-white font-semibold'>
                        Submit
                    </button>
                </div>
            </FormSubSection>
        </div>
    )
}

export default AgencyEnrollmentBranchForm
