import React, { forwardRef } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import InputField from '../../InputField';
import EmailInbox from '@/app/assets/svg/emailInbox';
import PhoneNumberInput from '../../PhoneNumberInput';
import InputFieldWithIcon from '../../InputFieldWithIcon';
import { useAddContactDetailsMutation } from '@/lib/redux/api/addUniversityApi';
import { useForm, Controller } from 'react-hook-form';
import { FieldValues, SubmitHandler, UseFormHandleSubmit } from 'react-hook-form';

// Define props type
interface ContactDetailsProps {
    onSubmit?: React.FormEventHandler<HTMLFormElement>;
}

const ContactDetails = forwardRef<HTMLFormElement, ContactDetailsProps>(({ onSubmit }, ref) => {
    const [addContactDetails, { isLoading, isSuccess, error }] = useAddContactDetailsMutation();
    const { register, handleSubmit, reset, control } = useForm<FieldValues>();
    const universityId = useSelector((state: RootState) => state.university.universityId)

    const handleFormSubmit: SubmitHandler<FieldValues> = async (data) => {
        try {
            const payload = {
                universityId,
                contactName: data.contact_name,
                designation: data.Designation,
                email: data.email,
                contactNumber: data.contact_number,
                alternativeEmail: data.alternative_email,
            };
            await addContactDetails(payload).unwrap();
            alert('Contact details saved!');
            reset();
        } catch (err) {
            alert('Failed to save contact details');
        }
    };

    return (
        <form ref={ref} onSubmit={onSubmit ? onSubmit : handleSubmit(handleFormSubmit)}>
            <div className='flex flex-col gap-6'>
                <InputField
                    id="contact_name"
                    placeholder="Enter contact name"
                    type="text"
                    label="Contact Name"
                    className='py-3'
                    {...register('contact_name')}
                />
                <div className='grid grid-cols-2 gap-3'>
                    <InputField
                        id="Designation"
                        placeholder="Enter designation"
                        type="text"
                        label="Designation"
                        className='py-3'
                        {...register('Designation')}
                    />
                    <InputFieldWithIcon
                        id="email"
                        type="text"
                        label="Email"
                        className='py-3'
                        icon={<EmailInbox />}
                        {...register('email')}
                    />
                    <Controller
                        name="contact_number"
                        control={control}
                        render={({ field }) => (
                            <PhoneNumberInput
                                label="Contact Number"
                                className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 rounded-lg w-full"
                                value={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />
                    />
                    <InputFieldWithIcon
                        id="alternative_email"
                        type="text"
                        label="Alternative Email"
                        className='py-3'
                        icon={<EmailInbox />}
                        {...register('alternative_email')}
                    />
                </div>
            </div>
        </form>
    );
});

ContactDetails.displayName = 'ContactDetails';

export default ContactDetails
