import { format } from 'date-fns';
import InputField from '../../InputField';
import { DatePicker } from '../../DatePicker';
import { Checkbox } from '@/components/ui/checkbox';
import React, { useState, forwardRef } from 'react';
import EditAction from '@/app/assets/svg/EditAction';
import DateMonthCalendar, { DateValue } from '../../DateMonthCalender';
import { useAddIntakesMutation } from '@/lib/redux/api/addUniversityApi';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';

interface IntakeData {
    intakeName: string;
    startDate: Date | null;
    endDate: Date | null;
    classStartDate: Date | null;
    selectedTypes: {
        initial: boolean;
        transfer: boolean;
    };
    dates: {
        initial: {
            applicationOpens: DateValue;
            applicationDeadline: DateValue;
            enrollmentDeadline: DateValue;
        };
        transfer: {
            applicationOpens: DateValue;
            applicationDeadline: DateValue;
            enrollmentDeadline: DateValue;
        };
    };
}

const IntakeForm = forwardRef<HTMLFormElement, { onSubmit?: (e: React.FormEvent) => void }>((props, ref) => {
    const [intake, setIntake] = useState<IntakeData>({
        intakeName: '',
        startDate: null,
        endDate: null,
        classStartDate: null,
        selectedTypes: {
            initial: true,
            transfer: true,
        },
        dates: {
            initial: {
                applicationOpens: { date: new Date(new Date().getFullYear(), 0, 1) },
                applicationDeadline: { date: new Date(new Date().getFullYear(), 4, 1) },
                enrollmentDeadline: { date: new Date(new Date().getFullYear(), 6, 30) },
            },
            transfer: {
                applicationOpens: { date: null },
                applicationDeadline: { date: null },
                enrollmentDeadline: { date: null },
            },
        },
    });

    const universityId = useSelector((state: RootState) => state.university.universityId);
    const [addIntakes, { isLoading, isSuccess, error }] = useAddIntakesMutation();
    const updateIntake = (updates: Partial<IntakeData>) => {
        setIntake((prev) => ({ ...prev, ...updates }));
    };

    const handleCheckboxChange = (
        type: 'initial' | 'transfer',
        checked: boolean
    ) => {
        setIntake((prev) => ({
            ...prev,
            selectedTypes: {
                ...prev.selectedTypes,
                [type]: checked,
            },
        }));
    };

    const handleDateChange = (
        studentType: 'initial' | 'transfer',
        dateField: 'applicationOpens' | 'applicationDeadline' | 'enrollmentDeadline',
        selectedDate: Date | null
    ) => {
        setIntake((prev) => ({
            ...prev,
            dates: {
                ...prev.dates,
                [studentType]: {
                    ...prev.dates[studentType],
                    [dateField]: { date: selectedDate },
                },
            },
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        const apiBody = {
            universityId,
            intake: [
                {
                    name: intake.intakeName,
                    startDate: intake.startDate ? format(intake.startDate, 'yyyy-MM-dd') : '',
                    endDate: intake.endDate ? format(intake.endDate, 'yyyy-MM-dd') : '',
                    applicationDates: [
                        ...(intake.selectedTypes.initial ? [{
                            studentType: 'regular',
                            applicationOpen: intake.dates.initial.applicationOpens.date
                                ? format(intake.dates.initial.applicationOpens.date, 'yyyy-MM-dd')
                                : '',
                            applicationEnd: intake.dates.initial.applicationDeadline.date
                                ? format(intake.dates.initial.applicationDeadline.date, 'yyyy-MM-dd')
                                : '',
                            enrollmentDeadline: intake.dates.initial.enrollmentDeadline.date
                                ? format(intake.dates.initial.enrollmentDeadline.date, 'yyyy-MM-dd')
                                : '',
                            actions: true,
                        }] : []),
                        ...(intake.selectedTypes.transfer ? [{
                            studentType: 'transfer',
                            applicationOpen: intake.dates.transfer.applicationOpens.date
                                ? format(intake.dates.transfer.applicationOpens.date, 'yyyy-MM-dd')
                                : '',
                            applicationEnd: intake.dates.transfer.applicationDeadline.date
                                ? format(intake.dates.transfer.applicationDeadline.date, 'yyyy-MM-dd')
                                : '',
                            enrollmentDeadline: intake.dates.transfer.enrollmentDeadline.date
                                ? format(intake.dates.transfer.enrollmentDeadline.date, 'yyyy-MM-dd')
                                : '',
                            actions: false,
                        }] : []),
                    ]
                }
            ]
        };

        try {
            await addIntakes(apiBody).unwrap();
            alert('Intake saved!');
        } catch (err) {
            alert('Failed to save intake');
        }
    };


    return (
        <div className="max-w-6xl mx-auto bg-white">
            <form ref={ref} onSubmit={handleSubmit}>
                <div className="space-y-6">
                    <div className="flex flex-col gap-6">
                        <InputField
                            id={`intake_name`}
                            placeholder="Enter the intake name"
                            type="text"
                            label="Intake Name"
                            className="py-3"
                            value={intake.intakeName}
                            onChange={(e) =>
                                updateIntake({ intakeName: e.target.value })
                            }
                            disabled={false}
                        />
                        <div className="grid grid-cols-2 gap-3">
                            <DatePicker
                                title="Start Date"
                                className="py-3 shadow-none"
                                value={intake.startDate ?? undefined}
                                onChange={(date) =>
                                    updateIntake({ startDate: date ?? null })
                                }
                                disabled={false}
                            />
                            <DatePicker
                                title="End Date"
                                className="py-3 shadow-none"
                                value={intake.endDate ?? undefined}
                                onChange={(date) =>
                                    updateIntake({ endDate: date ?? null })
                                }
                                disabled={false}
                            />
                        </div>
                        <DatePicker
                            title="Class Start Date"
                            className="py-3 shadow-none w-full"
                            value={intake.classStartDate ?? undefined}
                            onChange={(date) =>
                                updateIntake({ classStartDate: date ?? null })
                            }
                            disabled={false}
                        />
                        <div>
                            <label className="font-medium text-sm text-gray-600 block mb-3">
                                Student Type
                            </label>
                            <div className="flex items-center space-x-6">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id={`initial`}
                                        checked={intake.selectedTypes.initial}
                                        onCheckedChange={(checked) =>
                                            handleCheckboxChange('initial', checked as boolean)
                                        }
                                        disabled={false}
                                    />
                                    <label
                                        htmlFor={`initial`}
                                        className="text-sm text-gray-600 font-medium"
                                    >
                                        Initial
                                    </label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id={`transfer`}
                                        checked={intake.selectedTypes.transfer}
                                        onCheckedChange={(checked) =>
                                            handleCheckboxChange('transfer', checked as boolean)
                                        }
                                        disabled={false}
                                    />
                                    <label
                                        htmlFor={`transfer`}
                                        className="text-sm text-gray-600 font-medium"
                                    >
                                        Transfer
                                    </label>
                                </div>
                            </div>
                        </div>
                        {(intake.selectedTypes.initial || intake.selectedTypes.transfer) && (
                            <div className="border border-gray-200 rounded-lg overflow-hidden">
                                <div className="grid grid-cols-5 gap-4 px-4 py-3 bg-blue-50 text-sm font-semibold text-gray-700">
                                    <div>Student Type</div>
                                    <div>Application Opens</div>
                                    <div>Application Deadline</div>
                                    <div>Enrollment Deadline</div>
                                    <div className='text-center'>Status</div>
                                </div>
                                {intake.selectedTypes.initial && (
                                    <div className="grid grid-cols-5 gap-4 px-4 py-3 border-t border-gray-200 bg-white">
                                        <div className="text-gray-700 font-medium">Initial</div>
                                        <DateMonthCalendar
                                            studentType="initial"
                                            dateField="applicationOpens"
                                            value={intake.dates.initial.applicationOpens}
                                            onDateChange={(studentType, dateField, date) =>
                                                handleDateChange(studentType, dateField, date)
                                            }
                                            disabled={false}
                                        />
                                        <DateMonthCalendar
                                            studentType="initial"
                                            dateField="applicationDeadline"
                                            value={intake.dates.initial.applicationDeadline}
                                            onDateChange={(studentType, dateField, date) =>
                                                handleDateChange(studentType, dateField, date)
                                            }
                                            disabled={false}
                                        />
                                        <DateMonthCalendar
                                            studentType="initial"
                                            dateField="enrollmentDeadline"
                                            value={intake.dates.initial.enrollmentDeadline}
                                            onDateChange={(studentType, dateField, date) =>
                                                handleDateChange(studentType, dateField, date)
                                            }
                                            disabled={false}
                                        />
                                        <div className="flex items-center justify-center">
                                            <EditAction className='w-5 h-5 text-white p-1 rounded-full bg-primaryColor' />
                                        </div>
                                    </div>
                                )}
                                {intake.selectedTypes.transfer && (
                                    <div className="grid grid-cols-5 gap-4 px-4 py-3 border-t border-gray-200 bg-white">
                                        <div className="text-gray-700 font-medium">Transfer</div>
                                        <DateMonthCalendar
                                            studentType="transfer"
                                            dateField="applicationOpens"
                                            value={intake.dates.transfer.applicationOpens}
                                            onDateChange={(studentType, dateField, date) =>
                                                handleDateChange(studentType, dateField, date)
                                            }
                                            disabled={false}
                                        />
                                        <DateMonthCalendar
                                            studentType="transfer"
                                            dateField="applicationDeadline"
                                            value={intake.dates.transfer.applicationDeadline}
                                            onDateChange={(studentType, dateField, date) =>
                                                handleDateChange(studentType, dateField, date)
                                            }
                                            disabled={false}
                                        />
                                        <DateMonthCalendar
                                            studentType="transfer"
                                            dateField="enrollmentDeadline"
                                            value={intake.dates.transfer.enrollmentDeadline}
                                            onDateChange={(studentType, dateField, date) =>
                                                handleDateChange(studentType, dateField, date)
                                            }
                                            disabled={false}
                                        />
                                        <div className="flex items-center"></div>
                                    </div>
                                )}
                            </div>
                        )}
                </div>
            </div>
            </form>
        </div>
    );
});

IntakeForm.displayName = 'IntakeForm';

export default IntakeForm;