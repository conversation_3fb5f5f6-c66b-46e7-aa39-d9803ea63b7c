import React, { useState } from 'react'
import <PERSON><PERSON>ield from '../../SelectField'
import { CheckboxGroup } from '../../CheckboxGroup'
import { EyeIcon } from 'lucide-react'
import EditAction from '@/app/assets/svg/EditAction'
import Modal from '../../Modal'
import ConfigureCampusModal from '../../ConfigureCampusModal'

const UniEnrollementConfigurebyCourse = () => {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    const courseData = {
        title: 'Master of Cloud Computing Applications',
        lastAcademic: 'Grade 12 / HSC / High School',
        minGPA: '60% or Above',
        lectureLanguages: ['English', 'Chinese'],
        courseRank: 3,
        acceptanceRate: '65%',
        intakes: ['Fall', 'Summer', 'Spring'],
        fields: 'Computer Science',
        formats: ['In-Person', 'Online', 'Hybrid'],
        testRequirements: {
            ielts: { overall: 7.5, literacy: 7.5, comprehension: 8.0, conversation: 7.5, production: 7.5 },
            duolingo: { overall: 123 },
            gre: { overall: 323, vr: 158, qr: 168, aw: 4.5 },
        },
        applicationFee: '$1,000.00',
        tuitionFee: '$150',
        additionalRequirements: [
            'A competitive high school GPA, typically around 3.8-4.0 (unweighted).',
            'Strong SAT (1350-1530) or ACT (31-34) scores.',
            'Completion of challenging coursework, including AP/IB classes.',
            'Extracurricular involvement and leadership experience.',
            'Compelling personal essays and strong letters of recommendation.',
        ],
    };
    return (
        <div className='space-y-[24px]'>
            <div className='grid grid-cols-2 gap-6'>
                <SelectField
                    label='Campus'
                    placeholder='Enter Campus'
                    options={[{
                        label: 'Campus 1',
                        value: 'campus1'
                    }, {
                        label: 'Campus 2',
                        value: 'campus2'
                    }]}
                />
                <SelectField
                    label='Program Level'
                    placeholder='Select Program Level'
                    options={[{
                        label: 'Undergraduate',
                        value: 'undergraduate'
                    }, {
                        label: 'Postgraduate',
                        value: 'postgraduate'
                    }]}
                />
            </div>
            {Array.from({ length: 10 }).map((_, index) => (
                <div key={index} className='rounded-2xl border border-tertiary/20 py-[26px] px-[30px] flex justify-between items-center'>
                    <div className="flex items-center gap-3">
                        <input
                            type="checkbox"
                            className="accent-primaryColor w-5 h-5 rounded"
                        />
                        <span className="text-base font-medium text-gray-900">
                            Master of Cloud Computing Applications
                        </span>
                    </div>
                    <div className="flex">
                        
                        <Modal
                            onConfirm={() => setOpenIndex(null)}
                            trigger={
                                <button
                                    className="flex justify-center items-center gap-1 text-primaryColor text-sm font-medium px-2 py-2 hover:bg-primaryOne border rounded-l-lg "
                                >
                                    <EyeIcon className="w-4 h-4" />
                                    View
                                </button>
                            }
                            className='w-full  max-w-[1760px] max-h-[900px] overflow-y-auto'
                        >
                            {() => (
                                <div>
                                    <ConfigureCampusModal data={courseData} />
                                </div>
                            )}
                        </Modal>
                        <button
                            className="flex justify-center items-center gap-1 text-primaryColor text-sm font-medium px-2 py-2 hover:bg-primaryOne border rounded-r-lg border-l-0"
                        >
                            <EditAction className="w-4 h-4" />
                            Edit
                        </button>
                    </div>
                        
                </div>
            ))}
        </div>
    )
}

export default UniEnrollementConfigurebyCourse
