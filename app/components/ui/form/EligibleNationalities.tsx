import React, { forwardRef } from 'react';
import { DropdownOption } from '@/types';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import { Country } from 'country-state-city';
import MultiSelectDropdown from '../../MultiSelectDropdown';
import { useForm, Controller, FieldValues, SubmitHandler } from 'react-hook-form';
import { useAddEligibleCountriesMutation } from '@/lib/redux/api/addUniversityApi';

interface EligibleNationalitiesProps {
    onSubmit?: React.FormEventHandler<HTMLFormElement>;
}

const EligibleNationalities = forwardRef<HTMLFormElement, EligibleNationalitiesProps>(
    ({ onSubmit }, ref) => {
        const [addEligibleCountries, { isLoading, isSuccess, error }] = useAddEligibleCountriesMutation();
        const { handleSubmit, control, reset } = useForm<FieldValues>({
            defaultValues: {
                countries: [],
            },
        });
        const universityId = useSelector((state: RootState) => state.university.universityId);

        const countryOptions: DropdownOption[] = Country.getAllCountries().map((country) => ({
            label: country.name,
            value: country.isoCode,
        }));

        const onSubmitHandler: SubmitHandler<FieldValues> = async (data) => {
            try {
                const payload = {
                    universityId,
                    countryNames: data.countries.map((c: DropdownOption) => c.label),
                    isActive: true,
                };
                await addEligibleCountries(payload).unwrap();
                alert('Eligible countries saved!');
                reset();
            } catch (err) {
                alert('Failed to save eligible countries');
            }
        };

        return (
            <form
                ref={ref}
                onSubmit={onSubmit ? onSubmit : handleSubmit(onSubmitHandler)}
            >
                <div className='flex flex-col gap-6'>
                    <Controller
                        name="countries"
                        control={control}
                        render={({ field }) => (
                            <MultiSelectDropdown
                                label='Countries'
                                showSearch
                                data={countryOptions}
                                value={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />
                </div>
            </form>
        );
    }
);

EligibleNationalities.displayName = 'EligibleNationalities';

export default EligibleNationalities;
