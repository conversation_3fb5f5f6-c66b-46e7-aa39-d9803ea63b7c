import React, { forwardRef } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import { DropdownOption } from '@/types';
import MultiSelectDropdown from '../../MultiSelectDropdown';
import { useForm, Controller, FieldValues, SubmitHandler } from 'react-hook-form';
import { useAddFieldOfStudyMutation } from '@/lib/redux/api/addUniversityApi';

interface FieldOfStudyProps {
    onSubmit?: React.FormEventHandler<HTMLFormElement>;
}

const fieldOptions: DropdownOption[] = [
    { label: 'Physics', value: 'Physics' },
    { label: 'Chemistry', value: 'Chemistry' },
    { label: 'English', value: 'English' },
    { label: 'Mathmetics', value: 'Mathmetics' },
];

const FieldOfStudy = forwardRef<HTMLFormElement, FieldOfStudyProps>(
    ({ onSubmit }, ref) => {
        const [addFieldOfStudy, { isLoading, isSuccess, error }] = useAddFieldOfStudyMutation();
        const { handleSubmit, control, reset } = useForm<FieldValues>({
            defaultValues: {
                fields: [],
            },
        });

        const universityId = useSelector((state: RootState) => state.university.universityId);

        const onSubmitHandler: SubmitHandler<FieldValues> = async (data) => {
            console.log(data)
            try {
                const payload = {
                    universityId,
                    names: data.fields.map((f: DropdownOption) => f.label),
                };
                await addFieldOfStudy(payload).unwrap();
                alert('Fields of study saved!');
                reset();
            } catch (err) {
                alert('Failed to save fields of study');
            }
        };

        return (
            <form
                ref={ref}
                onSubmit={onSubmit ? onSubmit : handleSubmit(onSubmitHandler)}
            >
                <div className='flex flex-col gap-6'>
                    <Controller
                        name="fields"
                        control={control}
                        render={({ field }) => (
                            <MultiSelectDropdown
                                label='Field of Study'
                                showSearch
                                data={fieldOptions}
                                value={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />
                </div>
            </form>
        );
    }
);

FieldOfStudy.displayName = 'FieldOfStudy';

export default FieldOfStudy;

