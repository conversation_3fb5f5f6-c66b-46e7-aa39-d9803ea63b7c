import React, { useState } from 'react';
import <PERSON>Field from '../../SelectField';
import { DatePicker } from '../../DatePicker';
import { Button } from '@/components/ui/button';
import { Edit2, Pencil, Trash2 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import InputFieldWithIcon from '../../InputFieldWithIcon';
import Dollar from '@/app/assets/svg/Dollar';
import { Switch } from '@/components/ui/switch';
import { 
    commissionPaymentMethod, 
    commissionPayoutCycle, 
    currency, 
    paymentFrecuencyData, 
    paymentTerms } from '@/common';
import ToolTip from '@/app/assets/svg/ToolTipIcon';
import Plus from '@/app/assets/svg/plus';
import { useAddCommissionsMutation } from '@/lib/redux/api/addUniversityApi';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion';
import { 
    Too<PERSON><PERSON>, 
    TooltipContent, 
    TooltipProvider, 
    TooltipTrigger 
} from '@/components/ui/tooltip';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' },
];

interface VolumeBasedCommission {
    sl: number;
    students: number;
    commission: string;
}

interface CommissionData {
    id: string;
    name: string;
    programLevel: string;
    commissionType: string;
    commissionValue: number;
    startDate: string;
    endDate: string;
    volumeBasedCommission: VolumeBasedCommission[];
    programLevelEnabled: boolean;
    hasVolumeBasedCommission: boolean;
}

interface GeneralCommissionSettings {
    currency: string;
    paymentFrequency: string;
    paymentTerms: string;
    commissionPaymentMethod: string;
    commissionPayoutCycle: string;
    commissionPeriod: string;
}

interface CommissionFormProps {
    initialData?: CommissionData[];
    initialGeneralSettings?: GeneralCommissionSettings;
    onSubmit?: (data: CommissionData) => void;
    onEdit?: (id: string, data: CommissionData) => void;
    onDelete?: (id: string) => void;
    onGeneralSettingsChange?: (settings: GeneralCommissionSettings) => void;
}

const CommissionForm: React.FC<CommissionFormProps> = ({
    initialData = [],
    initialGeneralSettings = {
        currency: '',
        paymentFrequency: '',
        paymentTerms: '',
        commissionPaymentMethod: '',
        commissionPayoutCycle: '',
        commissionPeriod: '',
    },
    onSubmit,
    onEdit,
    onDelete,
    onGeneralSettingsChange,
}) => {
    // Existing commission data
    const [commissions, setCommissions] = useState<CommissionData[]>(initialData);
    
    // General commission settings (one-time setup)
    const [generalSettings, setGeneralSettings] = useState<GeneralCommissionSettings>(initialGeneralSettings);
    
    // Form state
    const [formData, setFormData] = useState<Partial<CommissionData>>({
        name: '',
        programLevel: '',
        commissionType: '',
        commissionValue: 0,
        startDate: '',
        endDate: '',
        hasVolumeBasedCommission: false,
        programLevelEnabled: true,
        volumeBasedCommission: [],
    });

    // Volume-based commission table state
    const [volumeRows, setVolumeRows] = useState<Array<{
        sl: number;
        students: string;
        commission: string;
        isEditing: boolean;
    }>>([]);

    const [editingCommissionId, setEditingCommissionId] = useState<string | null>(null);

    // Handle general settings changes
    const handleGeneralSettingChange = (field: keyof GeneralCommissionSettings, value: string) => {
        const newSettings = {
            ...generalSettings,
            [field]: value
        };
        setGeneralSettings(newSettings);
        onGeneralSettingsChange?.(newSettings);
    };

    // Handle form input changes
    const handleInputChange = (field: keyof CommissionData, value: any) => {
        // Convert Date objects to string format for form inputs
        let processedValue = value;
        if (value instanceof Date) {
            processedValue = value.toISOString().split('T')[0]; // YYYY-MM-DD format
        }
        
        setFormData(prev => ({
            ...prev,
            [field]: processedValue
        }));
    };

    // Handle volume-based commission toggle
    const toggleVolumeCommission = (checked: boolean) => {
        setFormData(prev => ({
            ...prev,
            hasVolumeBasedCommission: checked
        }));
        
        if (checked && volumeRows.length === 0) {
            setVolumeRows([{ sl: 1, students: '', commission: '', isEditing: true }]);
        }
    };

    // Volume table handlers
    const handleAddVolumeRow = () => {
        setVolumeRows(prev => [
            ...prev,
            { sl: prev.length + 1, students: '', commission: '', isEditing: true },
        ]);
    };

    const handleEditVolumeRow = (index: number) => {
        setVolumeRows(prev => {
            const newRows = [...prev];
            newRows[index].isEditing = true;
            return newRows;
        });
    };

    const handleSaveVolumeRow = (index: number) => {
        setVolumeRows(prev => {
            const newRows = [...prev];
            newRows[index].isEditing = false;
            return newRows;
        });
    };

    const handleRemoveVolumeRow = (index: number) => {
        setVolumeRows(prev => prev.filter((_, i) => i !== index));
    };

    const handleVolumeRowChange = (index: number, key: 'students' | 'commission', value: string) => {
        setVolumeRows(prev => {
            const newRows = [...prev];
            newRows[index][key] = value;
            return newRows;
        });
    };

    // Form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const volumeBasedCommission = volumeRows.map(row => ({
            sl: row.sl,
            students: parseInt(row.students) || 0,
            commission: row.commission,
        }));

        const newCommission: CommissionData = {
            id: editingCommissionId || Date.now().toString(),
            name: formData.name || '',
            programLevel: formData.programLevel || '',
            commissionType: formData.commissionType || '',
            commissionValue: formData.commissionValue || 0,
            startDate: formData.startDate || '',
            endDate: formData.endDate || '',
            hasVolumeBasedCommission: formData.hasVolumeBasedCommission || false,
            programLevelEnabled: formData.programLevelEnabled || true,
            volumeBasedCommission,
        };

        if (editingCommissionId) {
            // Update existing commission
            setCommissions(prev => 
                prev.map(commission => 
                    commission.id === editingCommissionId ? newCommission : commission
                )
            );
            onEdit?.(editingCommissionId, newCommission);
            setEditingCommissionId(null);
        } else {
            // Add new commission
            setCommissions(prev => [...prev, newCommission]);
            onSubmit?.(newCommission);
        }

        // Reset form
        resetForm();
    };

    const resetForm = () => {
        setFormData({
            name: '',
            programLevel: '',
            commissionType: '',
            commissionValue: 0,
            startDate: '',
            endDate: '',
            hasVolumeBasedCommission: false,
            programLevelEnabled: true,
            volumeBasedCommission: [],
        });
        setVolumeRows([]);
    };

    // Edit existing commission
    const handleEditCommission = (id: string) => {
        const commission = commissions.find(c => c.id === id);
        if (commission) {
            setFormData(commission);
            setEditingCommissionId(id);
            setVolumeRows(
                commission.volumeBasedCommission.map((item, index) => ({
                    sl: item.sl,
                    students: item.students.toString(),
                    commission: item.commission,
                    isEditing: false,
                }))
            );
        }
    };

    // Delete commission
    const handleDeleteCommission = (id: string) => {
        setCommissions(prev => prev.filter(c => c.id !== id));
        onDelete?.(id);
        
        // If currently editing this commission, reset form
        if (editingCommissionId === id) {
            resetForm();
            setEditingCommissionId(null);
        }
    };

    return (
        <div className='space-y-8'>
            <div className='grid grid-cols-2 gap-x-3 gap-y-6'>
                <SelectField
                    label='Currency'
                    options={currency}
                    className='py-6'
                    value={generalSettings.currency}
                    onChange={(value) => handleGeneralSettingChange('currency', value)}
                />
                <SelectField
                    label='Payment Frequency'
                    options={paymentFrecuencyData}
                    className='py-6'
                    value={generalSettings.paymentFrequency}
                    onChange={(value) => handleGeneralSettingChange('paymentFrequency', value)}
                />
                <SelectField
                    label='Payment Terms'
                    options={paymentTerms}
                    className='py-6'
                    value={generalSettings.paymentTerms}
                    sublabel='(Due in days after invoice)'
                    onChange={(value) => handleGeneralSettingChange('paymentTerms', value)}
                />
                <SelectField
                    label='Commission Payment Method'
                    options={commissionPaymentMethod}
                    className='py-6'
                    value={generalSettings.commissionPaymentMethod}
                    onChange={(value) => handleGeneralSettingChange('commissionPaymentMethod', value)}
                />
                <SelectField
                    label='Commission Payout Cycle'
                    options={commissionPayoutCycle}
                    className='py-6'
                    value={generalSettings.commissionPayoutCycle}
                    onChange={(value) => handleGeneralSettingChange('commissionPayoutCycle', value)}
                />
                <InputField
                    id='commission_period'
                    label='Commission Period'
                    className='py-6'
                    value={generalSettings.commissionPeriod}
                    onChange={(e) => handleGeneralSettingChange('commissionPeriod', e.target.value)}
                />
            </div>

            {/* Existing Commissions List */}
            {commissions.length > 0 && (
                <div className='w-full max-w-4xl mx-auto'>
                    <h3 className='text-primaryColor font-semibold mb-4'>Existing Commissions</h3>
                    <Accordion type='single' collapsible className='w-full space-y-4'>
                        {commissions.map((commission) => (
                            <AccordionItem
                                key={commission.id}
                                value={commission.id}
                                className='border border-tertiary/15 rounded-[10px] bg-white'
                            >
                                <AccordionTrigger className='p-3.5 hover:no-underline'>
                                    <div className='flex items-center justify-between w-full'>
                                        <div className='flex items-center gap-3.5'>
                                            <span className='text-primaryColor font-medium text-lg'>
                                                {commission.name}
                                            </span>
                                            <div
                                                className='p-1 rounded-full text-primaryColor hover:text-secondaryColor hover:bg-primaryOne border border-primaryTwo'
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleEditCommission(commission.id);
                                                }}
                                            >
                                                <Edit2 className='h-3.5 w-3.5' />
                                            </div>
                                            <div
                                                className='p-1 rounded-full text-[#FF3B30CC] hover:text-red-700 hover:bg-[#FF3B301A] border border-[#FF3B301A]'
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteCommission(commission.id);
                                                }}
                                            >
                                                <Trash2 className='h-3.5 w-3.5' />
                                            </div>
                                        </div>
                                    </div>
                                </AccordionTrigger>

                                <AccordionContent className='px-3.5 pb-3.5'>
                                    <div className='space-y-4 bg-[#FBFCFE] p-4 rounded-[8px] border border-primaryColor/20'>
                                        <div className='grid grid-cols-2 gap-4 text-sm'>
                                            <div>
                                                <span className='font-medium'>Program Level:</span>
                                                <span className='ml-2'>{commission.programLevel}</span>
                                            </div>
                                            <div>
                                                <span className='font-medium'>Commission Type:</span>
                                                <span className='ml-2'>{commission.commissionType}</span>
                                            </div>
                                            <div>
                                                <span className='font-medium'>Commission Value:</span>
                                                <span className='ml-2'>{commission.commissionValue}</span>
                                            </div>
                                            <div>
                                                <span className='font-medium'>Start Date:</span>
                                                <span className='ml-2'>{commission.startDate}</span>
                                            </div>
                                            <div>
                                                <span className='font-medium'>End Date:</span>
                                                <span className='ml-2'>{commission.endDate}</span>
                                            </div>
                                        </div>

                                        {commission.hasVolumeBasedCommission && commission.volumeBasedCommission.length > 0 && (
                                            <div>
                                                <h4 className='font-medium mb-2'>Volume-Based Commission:</h4>
                                                <div className='border border-gray-200 rounded-lg overflow-hidden'>
                                                    <div className='bg-gray-50 grid grid-cols-3 gap-4 p-3 font-medium text-gray-700 text-sm'>
                                                        <div>SL.</div>
                                                        <div>No. of Students</div>
                                                        <div>Commission</div>
                                                    </div>
                                                    {commission.volumeBasedCommission.map((item, index) => (
                                                        <div
                                                            key={index}
                                                            className='grid grid-cols-3 gap-4 p-3 border-t border-gray-200 text-sm'
                                                        >
                                                            <div>{item.sl}</div>
                                                            <div>{item.students}</div>
                                                            <div>{item.commission}</div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                        <div className='flex items-center gap-2'>
                                            <div
                                                className={`w-2 h-2 rounded-full ${
                                                    commission.programLevelEnabled
                                                        ? 'bg-primaryColor'
                                                        : 'bg-grayTwo'
                                                }`}
                                            />
                                            <span className='text-gray-700 text-sm'>
                                                Program-level commission{' '}
                                                <span className='text-primaryColor'>
                                                    {commission.programLevelEnabled ? 'enabled' : 'disabled'}
                                                </span>{' '}
                                                for all courses
                                            </span>
                                        </div>
                                    </div>
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>
                </div>
            )}

            {/* Commission Form */}
            <form onSubmit={handleSubmit} className='space-y-6 bg-[#FBFCFE] p-4 rounded-[8px] border border-primaryColor/20'>
                <div className='border-b border-primaryColor pb-3'>
                    <h3 className='text-primaryColor font-semibold'>
                        {editingCommissionId ? 'Edit Commission' : 'Commission Details'}
                    </h3>
                </div>

                {/* Commission Name */}
                <div className='space-y-2'>
                    <Label
                        htmlFor='commission-name'
                        className='text-grayFive text-sm font-medium'
                    >
                        Commission Name
                    </Label>
                    <Input
                        id='commission-name'
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className='border border-tertiary/20 py-3 px-3.5 rounded-[8px]'
                        placeholder='Enter commission name'
                    />
                </div>

                <SelectField
                    label='Program Level'
                    options={[
                        { value: '3-year', label: '3-Year Undergraduate' },
                        { value: '4-year', label: '4-Year Undergraduate' },
                        { value: '1-year', label: '1-Year Post Graduate' },
                    ]}
                    value={formData.programLevel}
                    onChange={(value) => handleInputChange('programLevel', value)}
                />

                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <SelectField
                        label='Commission Type'
                        options={[
                            { value: 'percentage', label: 'Percentage Based' },
                            { value: 'numerical', label: 'Numerical Based' },
                        ]}
                        className='py-6'
                        value={formData.commissionType}
                        onChange={(value) => handleInputChange('commissionType', value)}
                    />
                    <InputFieldWithIcon
                        label='Commission Value'
                        icon={<Dollar />}
                        className='mt-0.5 py-3 bg-white'
                        value={formData.commissionValue?.toString() || ''}
                        onChange={(e) => handleInputChange('commissionValue', parseFloat(e.target.value) || 0)}
                    />
                    <DatePicker
                        title='Start Date'
                        className='py-3'
                        value={formData.startDate || ''}
                        onChange={(value) => handleInputChange('startDate', value)}
                    />
                    <DatePicker
                        title='End Date'
                        className='py-3'
                        value={formData.endDate || ''}
                        onChange={(value) => handleInputChange('endDate', value)}
                    />
                </div>

                {/* Volume-based commission section */}
                <div className='space-y-4'>
                    <div className='flex items-center mb-4 gap-3'>
                        <Label
                            htmlFor='volume-commission'
                            className='font-medium text-sm text-grayFive block'
                        >
                            Student volume-based commission
                        </Label>
                        <Switch
                            className='border'
                            id='volume-commission'
                            checked={formData.hasVolumeBasedCommission}
                            onCheckedChange={toggleVolumeCommission}
                        />
                    </div>
                    
                    {formData.hasVolumeBasedCommission && (
                        <div className='border border-gray-200 rounded-lg overflow-hidden'>
                            <div className='bg-gray-50 grid grid-cols-4 gap-4 p-4 font-medium text-gray-700'>
                                <div>SL.</div>
                                <div className='flex items-center gap-1'>
                                    No. of Students
                                    <TooltipProvider delayDuration={100}>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <ToolTip />
                                            </TooltipTrigger>
                                            <TooltipContent className='bg-grayFour p-2.5 drop-shadow-5xl'>
                                                <p className='text-white w-[220px] text-[12px] leading-none'>
                                                    Define the top limit of students for this commission.
                                                </p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <div>Commission</div>
                                <div>Actions</div>
                            </div>

                            {volumeRows.map((row, index) => (
                                <div
                                    key={index}
                                    className='grid grid-cols-4 gap-4 p-4 border-t border-gray-200 items-center'
                                >
                                    <div className='text-gray-700'>{index + 1}</div>
                                    <div>
                                        {row.isEditing ? (
                                            <Input
                                                value={row.students}
                                                onChange={(e) =>
                                                    handleVolumeRowChange(index, 'students', e.target.value)
                                                }
                                                placeholder='Enter number'
                                            />
                                        ) : (
                                            <span className='text-gray-700'>{row.students}</span>
                                        )}
                                    </div>
                                    <div>
                                        {row.isEditing ? (
                                            <Input
                                                value={row.commission}
                                                onChange={(e) =>
                                                    handleVolumeRowChange(index, 'commission', e.target.value)
                                                }
                                                placeholder='e.g., 20%'
                                            />
                                        ) : (
                                            <span className='text-gray-700'>{row.commission}</span>
                                        )}
                                    </div>
                                    <div className='flex gap-2'>
                                        {row.isEditing ? (
                                            <Button
                                                type='button'
                                                size='sm'
                                                onClick={() => handleSaveVolumeRow(index)}
                                            >
                                                Save
                                            </Button>
                                        ) : (
                                            <Button
                                                type='button'
                                                size='sm'
                                                variant='outline'
                                                onClick={() => handleEditVolumeRow(index)}
                                            >
                                                <Pencil className='w-4 h-4' />
                                            </Button>
                                        )}
                                        <Button
                                            type='button'
                                            size='sm'
                                            variant='destructive'
                                            onClick={() => handleRemoveVolumeRow(index)}
                                        >
                                            <Trash2 className='w-4 h-4' />
                                        </Button>
                                    </div>
                                </div>
                            ))}
                            
                            <div className='border-t border-grayOne px-3.5 py-3'>
                                <button
                                    type='button'
                                    onClick={handleAddVolumeRow}
                                    className='py-1 px-2 bg-primaryColor text-white flex items-center gap-1.5 rounded-full hover:bg-tertiary transition-colors'
                                >
                                    Add Row
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                {/* Program Level Enabled */}
                <div className='flex items-center gap-3'>
                    <Switch
                        id='program-level'
                        checked={formData.programLevelEnabled}
                        onCheckedChange={(checked) => handleInputChange('programLevelEnabled', checked)}
                    />
                    <Label htmlFor='program-level' className='text-gray-700'>
                        Enable program-level commission for all courses
                    </Label>
                </div>

                {/* Form Actions */}
                <div className='flex gap-3 pt-4'>
                    <Button type='submit' className='bg-primaryThree hover:bg-primaryFour border border-tertiary/20 rounded-full px-4 py-2.5 text-primaryColor text-sm '>
                        {editingCommissionId ? 'Update Commission' : <span className='flex items-center gap-1'><Plus />Add Commission</span>}
                    </Button>
                    {editingCommissionId && (
                        <Button
                            type='button'
                            variant='outline'
                            onClick={() => {
                                resetForm();
                                setEditingCommissionId(null);
                            }}
                        >
                            Cancel
                        </Button>
                    )}
                </div>
            </form>

            
        </div>
    );
};

export default CommissionForm;