import React from 'react'
import FormSubSection from '../../FormSubSection'
import PhoneNumberInput from '../../PhoneNumberInput'
import InputFieldWithIcon from '../../InputFieldWithIcon'
import EmailInbox from '@/app/assets/svg/emailInbox'
import Input<PERSON>ield from '../../InputField'

const AgencyEnrollmentContactForm = () => {
    return (
        <div className='space-y-10'>
            <FormSubSection heading={'Agency Owner Contact Details'}>
                <div className="grid grid-cols-2 gap-6">
                    <InputField
                        id="name"
                        type="text"
                        label="Name"
                    />
                    <PhoneNumberInput
                        label="Contact Number"
                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                    />
                </div>
                <div className='grid grid-cols-2 gap-6'>
                    <PhoneNumberInput
                        label="Alternate Contact Number"
                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                    />
                    <InputFieldWithIcon
                        label="Email"
                        placeholder="Email"
                        icon={<EmailInbox />}
                        type="email"
                        className='mt-1'
                    />
                </div>
            </FormSubSection>
            <FormSubSection heading={'Primary Contact Details'}>
                <div className="grid grid-cols-2 gap-6">
                    <InputField
                        id="primary_person_name"
                        type="text"
                        label="Primary Person Name"
                    />
                    <InputField
                        id="designation"
                        type="text"
                        label="Designation"
                    />
                </div>
                <div className='grid grid-cols-3 gap-6'>
                    <PhoneNumberInput
                        label="Contact Number"
                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                    />
                    <PhoneNumberInput
                        label="Alternate Contact Number"
                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                    />
                    <InputFieldWithIcon
                        label="Email"
                        placeholder="Email"
                        icon={<EmailInbox />}
                        type="email"
                        className='mt-1'
                    />
                </div>
            </FormSubSection>
            <div className='w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5'>
                <button className='px-4 py-2 bg-primaryColor rounded-full text-white font-semibold'>
                    Submit
                </button>
            </div>
        </div>
    )
}

export default AgencyEnrollmentContactForm
