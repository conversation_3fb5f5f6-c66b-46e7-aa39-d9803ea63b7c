'use client';

import React, { useState } from 'react';
import FormSubSection from '../../FormSubSection';
import InputField from '../../InputField';
import SelectField from '../../SelectField';
import MultiSelect from '../../MultiSelect';
import TextAreaField from '../../TextAreaField';
import Plus from '@/app/assets/svg/plus';
import MultiSelectDropdown from '../../MultiSelectDropdown';
import { Label } from '@/components/ui/label';
import { Edit2 } from 'lucide-react';
import { DropdownOption } from '@/types';
import { Checkbox } from '@/components/ui/checkbox';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' },
];

const PopulateData = {
    fos: [
        { label: 'Physics', value: 'Physics' },
        { label: 'Chemistry', value: 'Chemistry' },
        { label: 'English', value: 'English' },
        { label: 'Mathmetics', value: 'Mathmetics' },
    ],
    programLevel : [
        { value: 'undergraduate', label: 'Undergraduate' },
        { value: 'graduate', label: 'Graduate' },
        { value: 'postgraduate', label: 'Postgraduate' },
        { value: 'diploma', label: 'Diploma' },
        { value: 'certificate', label: 'Certificate' },
        { value: 'foundation', label: 'Foundation' },
        { value: 'other', label: 'Other' },
    ],
    intake : [
        { value: 'summer', label: 'Summer' },
        { value: 'fall', label: 'Fall' },
        { value: 'spring', label: 'Spring' },
    ],
    format : [
        { value: 'full-time', label: 'Full-Time' },
        { value: 'part-time', label: 'Part-Time' },
        { value: 'online', label: 'Online' },
    ],

};

interface ScoreBreakdown {
    testOne: string;
    testTwo: string;
    testThree: string;
    testFour: string;
}

interface TestScoreRow {
    id: string;
    test: string;
    overall: number;
    scoreBreakdown: ScoreBreakdown;
    isEditing: boolean;
}

interface Fee {
    id: string;
    name: string;
    tuitionFee: number;
    applicationFee: number;
    effectiveDate: string;
    selected: boolean;
}

const testOptions = ['TOEFL', 'GRE', 'GMAT', 'PTE', 'SAT', 'Duolingo', 'ACT'];

const testModules: Record<string, string[]> = {
    GMAT: ['VR', 'QR', 'AWA', 'IR'],
    GRE: ['VR', 'QR', 'AW', 'Total'],
    TOEFL: ['Reading', 'Listening', 'Speaking', 'Writing'],
    IELTS: ['Reading', 'Listening', 'Speaking', 'Writing'],
    SAT: ['EBRW', 'Math', 'Essay1', 'Essay2'],
    ACT: ['English', 'Math', 'Reading', 'Science'],
    Duolingo: ['Literacy', 'Comprehensive'],
};

const UniEnrollmentCourseForm = () => {
    const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
    const [selectedFOS, setSelectedFOS] = useState<DropdownOption[]>([]);
    const [intake, setIntake] = useState<DropdownOption[]>([]);
    const [rows, setRows] = useState<TestScoreRow[]>([
        {
            id: '1',
            test: 'IELTS',
            overall: 6.5,
            scoreBreakdown: {
                testOne: '6.0',
                testTwo: '6.5',
                testThree: '7.0',
                testFour: '6.5'
            },
            isEditing: false,
        },
        {
            id: '2',
            test: 'TOEFL',
            overall: 90,
            scoreBreakdown: {
                testOne: '22',
                testTwo: '23',
                testThree: '24',
                testFour: '21'
            },
            isEditing: false,
        },
    ]);

    const getModulesForTest = (testType: string): string[] => {
        return (
            testModules[testType] || [
                'Module1',
                'Module2',
                'Module3',
                'Module4',
            ]
        );
    };

        const handleTestChange = (id: string, value: string) => {
            setRows(prev =>
                prev.map(row =>
                row.id === id ? { ...row, test: value } : row
                )
            );
        };

        const handleScoreChange = (
            id: string,
            key: keyof TestScoreRow['scoreBreakdown'],
            value: string
        ) => {
            setRows(prev =>
                prev.map(row =>
                row.id === id
                    ? {
                        ...row,
                        scoreBreakdown: {
                        ...row.scoreBreakdown,
                        [key]: value,
                        },
                    }
                    : row
                )
            );
        };

        const toggleEdit = (id: string) => {
            setRows(prev =>
                prev.map(row =>
                row.id === id
                    ? {
                        ...row,
                        isEditing: !row.isEditing,
                    }
                    : row
                )
            );
        };

    const [fees, setFees] = useState<Fee[]>([
        {
            id: '1',
            name: 'Undergraduate Fee',
            tuitionFee: 100000,
            applicationFee: 150,
            effectiveDate: 'Jan 21, 2026',
            selected: false,
        },
        {
            id: '2',
            name: 'Graduate Fee',
            tuitionFee: 100000,
            applicationFee: 120,
            effectiveDate: 'Jan 21, 2026',
            selected: true,
        },
    ]);

    const [editingId, setEditingId] = useState<string | null>(null);
    const [editValues, setEditValues] = useState<Partial<Fee>>({});

    const handleEdit = (fee: Fee) => {
        setEditingId(fee.id);
        setEditValues(fee);
    };

    const handleSave = () => {
        if (editingId && editValues) {
            setFees(
                fees.map((fee) =>
                    fee.id === editingId ? { ...fee, ...editValues } : fee
                )
            );
        }
        setEditingId(null);
        setEditValues({});
    };

    const handleCancel = () => {
        setEditingId(null);
        setEditValues({});
    };

    const handleInputChange = (
        field: keyof Fee,
        value: string | number | boolean
    ) => {
        setEditValues((prev) => ({ ...prev, [field]: value }));
    };

    const handleCheckboxChange = (id: string) => {
        setFees(
            fees.map((fee) =>
                fee.id === id ? { ...fee, selected: !fee.selected } : fee
            )
        );
    };

    const formatCurrency = (amount: number) => {
        return `$${amount.toLocaleString()}`;
    };

    return (
        <div className="space-y-[30px]">
            <FormSubSection heading={'General Info'}>
                <div className="grid grid-cols-7 gap-6">
                    <div className="col-span-4">
                        <InputField
                            id="course_title"
                            type="text"
                            label="Course Title"
                            className="py-3 "
                        />
                    </div>
                    <div className="col-span-3">
                        <MultiSelectDropdown
                            label="Field of Study"
                            showSearch
                            data={PopulateData.fos}
                            value={selectedFOS}
                            onChange={setSelectedFOS}
                        />
                    </div>
                </div>
                <div className="grid grid-cols-3 gap-6">
                    <SelectField
                        placeholder="Select"
                        label="Program Level"
                        options={PopulateData.programLevel}
                        className="py-5"
                    />
                    <MultiSelectDropdown
                        label="Intake"
                        data={PopulateData.intake}
                        value={intake}
                        onChange={setIntake}
                    />
                    <SelectField
                        label="Format"
                        options={PopulateData.format}
                        className="py-5"
                    />
                </div>
            </FormSubSection>
            <FormSubSection heading="Standardized Test Requirements">
                <div>
                    <Label
                        htmlFor="test"
                        className="font-medium text-sm text-grayFive mb-4 block"
                    >
                        Test & Language Scores
                    </Label>
                    {rows.length > 0 && (
                        <div className="border border-tertiary/20 rounded-[8px] overflow-hidden">
                            {/* Table Header */}
                            <div className="grid grid-cols-8 gap-4 px-3.5 py-3 bg-primaryOne text-sm font-bold text-grayFive place-items-center">
                                <div className='items-start'>
                                    <Checkbox
                                    />
                                </div>
                                <div>Tests</div>
                                <div>Overall</div>
                                <div className="col-span-4 text-center">
                                    Score Breakdown
                                </div>
                                <div>Actions</div>
                            </div>

                            {/* Table Rows */}
                            {rows.map((row) => {
                                const modules = getModulesForTest(row.test);
                                return (

                                    <div
                                        key={row.id}
                                        className="grid grid-cols-8 gap-4 px-3.5 py-3 border-t border-grayOne bg-white text-grayFive"
                                    >
                                        <div className='text-center flex items-center justify-center'>
                                            <Checkbox
                                                checked={row.isEditing}
                                                onCheckedChange={() => toggleEdit(row.id)}
                                            />
                                        </div>
                                        <div className="flex flex-col items-center justify-center gap-2">
                                            {row.isEditing ? (
                                                <select
                                                    value={row.test}
                                                    onChange={(e) =>
                                                        handleTestChange(
                                                            row.id,
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm focus:outline-none"
                                                >
                                                    {testOptions.map(
                                                        (option) => (
                                                            <option
                                                                key={option}
                                                                value={option}
                                                            >
                                                                {option}
                                                            </option>
                                                        )
                                                    )}
                                                </select>
                                            ) : (
                                                <div className="text-sm font-medium">
                                                    {row.test}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2">
                                            {row.isEditing ? (
                                                <input
                                                    type="number"
                                                    value={row.overall || ''}
                                                    onChange={(e) => {
                                                        setRows(prev =>
                                                            prev.map(r =>
                                                                r.id === row.id
                                                                    ? { ...r, overall: parseFloat(e.target.value) || 0 }
                                                                    : r
                                                            )
                                                        );
                                                    }}
                                                    placeholder='Overall'
                                                    className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm focus:outline-none"
                                                />
                                            ) : (
                                                <div className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm font-semibold text-center">
                                                    {row.overall}
                                                </div>
                                            )}
                                        </div>

                                        {/* Score Inputs */}
                                        <div className="flex flex-col gap-2">
                                            {row.isEditing ? (
                                                <input
                                                    type="number"
                                                    value={row.scoreBreakdown.testOne || ''}
                                                    onChange={(e) =>
                                                        handleScoreChange(
                                                            row.id,
                                                            'testOne',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder={modules[0]}
                                                    className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm focus:outline-none"
                                                />
                                            ) : (
                                                <div className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm font-semibold text-center">
                                                    {row.scoreBreakdown.testOne}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2">
                                            {row.isEditing ? (
                                                <input
                                                    type="number"
                                                    value={row.scoreBreakdown.testTwo || ''}
                                                    onChange={(e) =>
                                                        handleScoreChange(
                                                            row.id,
                                                            'testTwo',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder={modules[1]}
                                                    className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm focus:outline-none"
                                                />
                                            ) : (
                                                <div className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm font-semibold text-center">
                                                    {row.scoreBreakdown.testTwo}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2">
                                            {row.isEditing ? (
                                                <input
                                                    type="number"
                                                    value={row.scoreBreakdown.testThree || ''}
                                                    onChange={(e) =>
                                                        handleScoreChange(
                                                            row.id,
                                                            'testThree',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder={modules[2]}
                                                    className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm focus:outline-none"
                                                />
                                            ) : (
                                                <div className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm font-semibold text-center">
                                                    {row.scoreBreakdown.testThree}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2">
                                            {row.isEditing ? (
                                                <input
                                                    type="number"
                                                    value={row.scoreBreakdown.testFour || ''}
                                                    onChange={(e) =>
                                                        handleScoreChange(
                                                            row.id,
                                                            'testFour',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder={modules[3]}
                                                    className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm focus:outline-none"
                                                />
                                            ) : (
                                                <div className="w-full p-1 border border-tertiary/10 rounded-[4px] text-sm font-semibold text-center">
                                                    {row.scoreBreakdown.testFour}
                                                </div>
                                            )}
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center justify-center">
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    toggleEdit(row.id)
                                                }
                                                className="text-primary font-medium text-sm"
                                            >
                                                {row.isEditing ? 'Save' : 'Edit'}
                                            </button>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>
            </FormSubSection>
            <FormSubSection heading="Fees">
                <div className="w-full bg-white rounded-[8px] border border-tertiary/20 overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead>
                                <tr className="border-b border-tertiary/20 bg-primaryOne">
                                    <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">
                                        Fees Category
                                    </th>
                                    <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">
                                        Fee Name
                                    </th>
                                    <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">
                                        <div>
                                            Tuition Fee
                                            <div className="text-xs text-gray-500 font-normal">
                                                (Per semester)
                                            </div>
                                        </div>
                                    </th>
                                    <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">
                                        <div>
                                            Application Fee
                                            <div className="text-xs text-gray-500 font-normal">
                                                (One-time, non-refundable)
                                            </div>
                                        </div>
                                    </th>
                                    <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">
                                        Fees Effective Date
                                    </th>
                                    <th className="text-left py-3 px-4 font-medium text-gray-700 text-sm">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {fees.map((fee, index) => (
                                    <tr
                                        key={fee.id}
                                        className='border-t border-tertiary/20'
                                    >
                                        <td className="py-3 px-4">
                                            <input
                                                type="checkbox"
                                                checked={fee.selected}
                                                onChange={() =>
                                                    handleCheckboxChange(fee.id)
                                                }
                                                className="w-4 h-4 text-blue-600 border-tertiary/20 rounded focus:ring-blue-500"
                                            />
                                        </td>
                                        <td className="py-3 px-4">
                                            {editingId === fee.id ? (
                                                <input
                                                    type="text"
                                                    value={
                                                        editValues.name || ''
                                                    }
                                                    onChange={(e) =>
                                                        handleInputChange(
                                                            'name',
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-full px-2 py-1 border border-tertiary/20 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                />
                                            ) : (
                                                <span className="text-gray-900 text-sm">
                                                    {fee.name}
                                                </span>
                                            )}
                                        </td>
                                        <td className="py-3 px-4">
                                            {editingId === fee.id ? (
                                                <input
                                                    type="number"
                                                    value={
                                                        editValues.tuitionFee ||
                                                        ''
                                                    }
                                                    onChange={(e) =>
                                                        handleInputChange(
                                                            'tuitionFee',
                                                            parseInt(
                                                                e.target.value
                                                            ) || 0
                                                        )
                                                    }
                                                    className="w-full px-2 py-1 border border-tertiary/20 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                />
                                            ) : (
                                                <span className="text-gray-900 text-sm">
                                                    {formatCurrency(
                                                        fee.tuitionFee
                                                    )}
                                                </span>
                                            )}
                                        </td>
                                        <td className="py-3 px-4">
                                            {editingId === fee.id ? (
                                                <input
                                                    type="number"
                                                    value={
                                                        editValues.applicationFee ||
                                                        ''
                                                    }
                                                    onChange={(e) =>
                                                        handleInputChange(
                                                            'applicationFee',
                                                            parseInt(
                                                                e.target.value
                                                            ) || 0
                                                        )
                                                    }
                                                    className="w-full px-2 py-1 border border-tertiary/20 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                />
                                            ) : (
                                                <span className="text-gray-900 text-sm">
                                                    {formatCurrency(
                                                        fee.applicationFee
                                                    )}
                                                </span>
                                            )}
                                        </td>
                                        <td className="py-3 px-4">
                                            {editingId === fee.id ? (
                                                <input
                                                    type="text"
                                                    value={
                                                        editValues.effectiveDate ||
                                                        ''
                                                    }
                                                    onChange={(e) =>
                                                        handleInputChange(
                                                            'effectiveDate',
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-full px-2 py-1 border border-tertiary/20 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                />
                                            ) : (
                                                <span className="text-gray-900 text-sm">
                                                    {fee.effectiveDate}
                                                </span>
                                            )}
                                        </td>
                                        <td className="py-3 px-4">
                                            {editingId === fee.id ? (
                                                <div className="flex gap-2">
                                                    <button
                                                        onClick={handleSave}
                                                        className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                                                    >
                                                        Save
                                                    </button>
                                                    <button
                                                        onClick={handleCancel}
                                                        className="px-3 py-1 bg-gray-400 text-white text-xs rounded hover:bg-gray-500 transition-colors"
                                                    >
                                                        Cancel
                                                    </button>
                                                </div>
                                            ) : (
                                                <button
                                                    onClick={() =>
                                                        handleEdit(fee)
                                                    }
                                                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                                    title="Edit"
                                                >
                                                    <Edit2 size={16} />
                                                </button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div className="grid grid-cols-3 gap-6">
                    <InputField
                        id="last_academic"
                        type="text"
                        label="Last Academic"
                        className="py-3"
                    />
                    <InputField
                        id="minimum_gpa"
                        type="text"
                        label="Minimum GPA"
                        className="py-3"
                    />
                    <MultiSelect
                        options={DemoSelectButtonData}
                        selectedValues={selectedOptions}
                        onChange={(values: any) => setSelectedOptions(values)}
                        label="Lecture Language"
                    />
                </div>
                <div className="grid grid-cols-2 gap-6">
                    <InputField
                        id="course_rank"
                        type="text"
                        label="Course Rank"
                        className="py-3"
                        placeholder="Enter global rank"
                    />
                    <InputField
                        id="acceptance_rate"
                        type="text"
                        label="Acceptance rate"
                        className="py-3"
                        placeholder="Enter acceptance rate"
                    />
                </div>
                <div>
                    <TextAreaField
                        label="Additional Requirements"
                        placeholder="Enter entry requirements"
                    />
                </div>
            </FormSubSection>
            <button className="py-2.5 px-4 bg-primaryThree text-primaryColor flex items-center gap-1.5 w-fit rounded-full">
                <Plus />
                Add More
            </button>
            <div className="w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5">
                <button className="px-4 py-2 bg-primaryColor rounded-full text-white font-semibold">
                    Submit
                </button>
            </div>
        </div>
    );
};

export default UniEnrollmentCourseForm;
