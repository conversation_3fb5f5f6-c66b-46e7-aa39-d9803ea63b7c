import AddPicture from '@/app/assets/svg/AddPicture'
import React from 'react'
import InputField from '../../InputField'
import Plus from '@/app/assets/svg/plus'

const UniEnrollmentAlumniForm = () => {
    return (
        <div className='space-y-[30px]'>
            <div className='grid grid-cols-2 gap-6 w-full'>
                <div className='space-y-[30px] w-full'>
                    <span className='border-b pb-3.5 text-sm font-medium text-grayFour w-full inline-block'>
                        Person 01
                    </span>
                    <div className='p-[31px] bg-primaryOne rounded-full w-fit'>
                        <AddPicture />
                    </div>
                    <InputField
                        id='alumni_name'
                        placeholder='Enter Alumni Name'
                        type='text'
                        label='Name'
                        className='py-3'
                    />
                    <div className='grid grid-cols-2 gap-4'>
                        <InputField
                            id='alumni_name'
                            type='text'
                            label='Organization Name'
                            className='py-3'
                        />
                        <InputField
                            id='designation'
                            type='text'
                            label='Designation'
                            className='py-3'
                        />
                    </div>
                </div>
                <div className='space-y-[30px] w-full'>
                    <span className='border-b pb-3.5 text-sm font-medium text-grayFour w-full inline-block'>
                        Person 02
                    </span>
                    <div className='p-[31px] bg-primaryOne rounded-full w-fit'>
                        <AddPicture />
                    </div>
                    <InputField
                        id='alumni_name'
                        placeholder='Enter Alumni Name'
                        type='text'
                        label='Name'
                        className='py-3'
                    />
                    <div className='grid grid-cols-2 gap-4'>
                        <InputField
                            id='alumni_name'
                            type='text'
                            label='Organization Name'
                            className='py-3'
                        />
                        <InputField
                            id='designation'
                            type='text'
                            label='Designation'
                            className='py-3'
                        />
                    </div>
                </div>
            </div>
            <button className='py-2.5 px-4 bg-primaryThree text-primaryColor flex items-center gap-1.5 w-fit rounded-full'>
                <Plus />
                Add More
            </button>
            <div className='w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5'>
                <button className='px-4 py-2 bg-primaryColor rounded-full text-white font-semibold'>
                    Submit
                </button>
            </div>
        </div>
    )
}

export default UniEnrollmentAlumniForm
