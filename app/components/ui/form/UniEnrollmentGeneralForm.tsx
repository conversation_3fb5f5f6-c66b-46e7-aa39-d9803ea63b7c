'use client';

import React, { useState } from 'react';
import FormSubSection from '../../FormSubSection';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import ImageUploader from '../../ImageUploader';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' }
]

const UniEnrollmentGeneralForm = () => {
    const [scholarshipOption, setScholarshipOption] = useState<string>("");
    const [financialAidOption, setFinancialAidOption] = useState<string>("");
    const [workStudyOption, setWorkStudyOption] = useState<string>("");
    const [paymentPlanOption, setPaymentPlanOption] = useState<string>("");
    const [enrollmentOption, setEnrollmentOption] = useState<string>("");
    const [universityImages, setUniversityImages] = useState<File[]>([]);
    const [prospectusfiles, setProspectusfiles] = useState<File[]>([]);
    const [agrrementImages, setAgreementImages] = useState<File[]>([]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = {
            tuitionFinancialAid: {
                tuitionFeeDiscount: scholarshipOption,
                financialAidAcceptance: financialAidOption,
                scholarshipOpportunity: workStudyOption,
                accommodationStatus: paymentPlanOption
            },
            statusAndAgreements: {
                activeEnrollment: enrollmentOption,
                agreementDocuments: agrrementImages.map(file => ({
                    name: file.name,
                    size: file.size,
                    type: file.type
                }))
            },
            universityFeatureImages: universityImages.map(file => ({
                name: file.name,
                size: file.size,
                type: file.type
            })),
            admissionProspectus: prospectusfiles.map(file => ({
                name: file.name,
                size: file.size,
                type: file.type
            }))
        };

        console.log('=== GENERAL FORM SUBMISSION DATA ===');
        console.log('Complete Form Data:', formData);
        console.log('Raw State Values:', {
            scholarshipOption,
            financialAidOption,
            workStudyOption,
            paymentPlanOption,
            enrollmentOption,
            universityImagesCount: universityImages.length,
            prospectusFilesCount: prospectusfiles.length,
            agreementImagesCount: agrrementImages.length
        });
        console.log('Files Details:', {
            universityImages,
            prospectusfiles,
            agreementImages: agrrementImages
        });
    };

    return (
        <form className='space-y-[30px]' onSubmit={handleSubmit}>
            <FormSubSection heading={'Tuition Financial Aid & Scholarships'}>
                <div className='grid grid-cols-4 gap-6'>
                    <div className='flex flex-col gap-2'>
                        <label className="font-medium text-sm text-grayFive">Tuition Fee Discount</label>
                        <RadioGroup value={scholarshipOption} onValueChange={setScholarshipOption}>
                            <div className='flex gap-6'>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="scholarship_yes" id="scholarship_yes" />
                                    <label htmlFor="scholarship_yes" className="text-sm text-grayFive">Yes</label>
                                </div>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="scholarship_no" id="scholarship_no" />
                                    <label htmlFor="scholarship_no" className="text-sm text-grayFive">No</label>
                                </div>
                            </div>
                        </RadioGroup>
                    </div>
                    
                    <div className='flex flex-col gap-2'>
                        <label className="font-medium text-sm text-grayFive">Financial Aid Acceptance</label>
                        <RadioGroup value={financialAidOption} onValueChange={setFinancialAidOption}>
                            <div className='flex gap-6'>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="financial_aid_yes" id="financial_aid_yes" />
                                    <label htmlFor="financial_aid_yes" className="text-sm text-grayFive">Yes</label>
                                </div>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="financial_aid_no" id="financial_aid_no" />
                                    <label htmlFor="financial_aid_no" className="text-sm text-grayFive">No</label>
                                </div>
                            </div>
                        </RadioGroup>
                    </div>
                    
                    <div className='flex flex-col gap-2'>
                        <label className="font-medium text-sm text-grayFive">Scholarship Opportunity</label>
                        <RadioGroup value={workStudyOption} onValueChange={setWorkStudyOption}>
                            <div className='flex gap-6'>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="work_study_yes" id="work_study_yes" />
                                    <label htmlFor="work_study_yes" className="text-sm text-grayFive">Yes</label>
                                </div>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="work_study_no" id="work_study_no" />
                                    <label htmlFor="work_study_no" className="text-sm text-grayFive">No</label>
                                </div>
                            </div>
                        </RadioGroup>
                    </div>
                    
                    <div className='flex flex-col gap-2'>
                        <label className="font-medium text-sm text-grayFive">Accommodation Status</label>
                        <RadioGroup value={paymentPlanOption} onValueChange={setPaymentPlanOption}>
                            <div className='flex gap-6'>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="payment_plan_yes" id="payment_plan_yes" />
                                    <label htmlFor="payment_plan_yes" className="text-sm text-grayFive">Yes</label>
                                </div>
                                <div className='flex items-center gap-2'>
                                    <RadioGroupItem value="payment_plan_no" id="payment_plan_no" />
                                    <label htmlFor="payment_plan_no" className="text-sm text-grayFive">No</label>
                                </div>
                            </div>
                        </RadioGroup>
                    </div>
                </div>
            </FormSubSection>
            <FormSubSection heading={'Status and Agreements'}>
                <div className='grid grid-cols-2 gap-6'>
                    <ImageUploader
                        label="Agreement Document"
                        multiple={true}
                        maxFiles={5}
                        value={agrrementImages}
                        onChange={(files) => setAgreementImages(files as File[])}
                    />
                    <div className='flex flex-col gap-2'>
                        <label className="font-medium text-sm text-grayFive">Active Enrollment</label>
                        <RadioGroup value={enrollmentOption} onValueChange={setEnrollmentOption}>
                            <div className='flex gap-10 py-3'>
                                <div className='flex items-center gap-4'>
                                    <RadioGroupItem value="enrollment_yes" id="enrollment_yes" />
                                    <label htmlFor="enrollment_yes" className="text-sm text-grayFive">Yes</label>
                                </div>
                                <div className='flex items-center gap-4'>
                                    <RadioGroupItem value="enrollment_no" id="enrollment_no" />
                                    <label htmlFor="enrollment_no" className="text-sm text-grayFive">No</label>
                                </div>
                            </div>
                        </RadioGroup>
                    </div>
                </div>
            </FormSubSection>
            <FormSubSection heading='University Feature Images'>
                <ImageUploader
                    label="University Image Collection"
                    multiple={true}
                    maxFiles={5}
                    value={universityImages}
                    onChange={(files) => setUniversityImages(files as File[])}
                />
            </FormSubSection>
            <FormSubSection heading='Admission Prospectus'>
                <div className='grid grid-cols-2'>
                    <div className='flex items-center w-full gap-6'>    
                        <span className='font-medium'>Prospectus</span>
                        <ImageUploader
                            multiple={true}
                            maxFiles={5}
                            value={prospectusfiles}
                            onChange={(files) => setProspectusfiles(files as File[])}
                            className='w-full'
                        />
                    </div>
                </div>
            </FormSubSection>
            <div className='w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5'>
                <button 
                    type="submit"
                    className='px-4 py-2 bg-primaryColor rounded-full text-white font-semibold'
                >
                    Submit
                </button>
            </div>
        </form>
    );
};

export default UniEnrollmentGeneralForm;
