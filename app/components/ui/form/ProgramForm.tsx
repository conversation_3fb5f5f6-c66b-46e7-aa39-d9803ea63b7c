import Plus from '@/app/assets/svg/plus';
import { DropdownOption } from '@/types';
import { CSS } from '@dnd-kit/utilities';
import InputField from '../../InputField';
import SelectField from '../../SelectField';
import { Edit2, Trash2 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import React, { useEffect, useState, forwardRef } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import EditAction from '@/app/assets/svg/EditAction';
import MultiSelectDropdown from '../../MultiSelectDropdown';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import {
    DndContext,
    closestCenter,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' }
]

interface ScoreBreakdown {
    testOne: string;
    testTwo: string;
    testThree: string;
    testFour: string;
}

interface TestScoreRow {
    id: string;
    test: string;
    overall: number;
    scoreBreakdown: ScoreBreakdown;
}

interface ProgramLevelData {
    programName: string;
    duration: number;

}

const testOptions = [
    'TOEFL',
    'GRE',
    'GMAT',
    'PTE',
    'SAT',
    'Duolingo',
    'ACT'
];

const testModules: Record<string, string[]> = {
    'GMAT': ['VR', 'QR', 'AWA', 'IR'],
    'GRE': ['VR', 'QR', 'AW', 'Total'],
    'TOEFL': ['Reading', 'Listening', 'Speaking', 'Writing'],
    'IELTS': ['Reading', 'Listening', 'Speaking', 'Writing'],
    'SAT': ['EBRW', 'Math', 'Essay1', 'Essay2'],
    'ACT': ['English', 'Math', 'Reading', 'Science'],
    'Duolingo': ['Literacy', 'Comprehensive']
};

interface SortableRowProps {
    step: any;
    listeners: any;
    attributes: any;
    isDragging: boolean;
    transform: any;
    transition: string | undefined;
    sequence: number;
}

function SortableRow({ step, listeners, attributes, isDragging, transform, transition, sequence }: SortableRowProps) {
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    return (
        <div
            {...attributes}
            {...listeners}
            style={style}
            className='grid grid-cols-5 gap-4 place-items-center px-3.5 py-3 border-t border-grayOne bg-white text-grayFive'
        >
            {/* <div className='flex flex-col gap-2'>{step.id}</div> */}
            <div className='flex flex-col gap-2'>{sequence}</div>
            <div className='flex flex-col col-span-3 gap-2'>{step.step}</div>
            <div className='flex flex-col gap-2'>
                <Checkbox />
            </div>
        </div>
    );
}

const ProgramForm = forwardRef<HTMLFormElement>((props, ref) => {
    const [programLevel, setProgramlevel] = useState();
    const [selected] = useState([]);
    const [testRequired, setTestRequired] = useState(true);
    const [rows, setRows] = useState<TestScoreRow[]>([
        {
            id: '1',
            test: 'GMAT',
            overall: 0,
            scoreBreakdown: {
                testOne: '',
                testTwo: '',
                testThree: '',
                testFour: ''
            }
        }
    ]);

    const [programLevelName, setProgramLevelName] = useState('');
    const [duration, setDuration] = useState('');
    const [durationUnit, setDurationUnit] = useState('');
    const [intake, setIntake] = useState<DropdownOption[]>([]);

    const toggleTestVisibility = () => {
        setTestRequired((prev) => !prev);
    };

    const handleTestChange = (rowId: string, selectedTest: string) => {
        setRows((prev) =>
            prev.map((row) => {
                if (row.id === rowId) {
                    const modules = testModules[selectedTest] || ['Module1', 'Module2', 'Module3', 'Module4'];
                    const newScoreBreakdown: ScoreBreakdown = {
                        testOne: '',
                        testTwo: '',
                        testThree: '',
                        testFour: ''
                    };
                    
                    return {
                        ...row,
                        test: selectedTest,
                        scoreBreakdown: newScoreBreakdown,
                        overall: 0
                    };
                }
                return row;
            })
        );
    };

    const handleScoreChange = (
        rowId: string,
        testModule: 'testOne' | 'testTwo' | 'testThree' | 'testFour',
        score: string
    ) => {
        setRows((prev) =>
            prev.map((row) => {
                if (row.id === rowId) {
                    const currentRow = rows.find(r => r.id === rowId);
                    const modules = testModules[currentRow?.test || 'GMAT'];
                    const moduleIndex = testModule === 'testOne' ? 0 : testModule === 'testTwo' ? 1 : testModule === 'testThree' ? 2 : 3;
                    const moduleName = modules[moduleIndex];
                    const updatedScoreBreakdown = {
                        ...row.scoreBreakdown,
                        [testModule]: `${moduleName} ${score}`
                    };
                    return {
                        ...row,
                        scoreBreakdown: updatedScoreBreakdown
                    };
                }
                return row;
            })
        );
    };

    const addNewRow = () => {
        const newRow: TestScoreRow = {
            id: Date.now().toString(),
            test: 'GMAT',
            overall: 0,
            scoreBreakdown: {
                testOne: '',
                testTwo: '',
                testThree: '',
                testFour: ''
            }
        };
        setRows((prev) => [...prev, newRow]);
    };

    const removeRow = (rowId: string) => {
        if (rows.length > 1) {
            setRows((prev) => prev.filter((row) => row.id !== rowId));
        }
    };

    const getModulesForTest = (testType: string): string[] => {
        return testModules[testType] || ['Module1', 'Module2', 'Module3', 'Module4'];
    };

    const getScoreValue = (scoreString: string): string => {
        return scoreString.split(' ')[1] || '';
    };

    const apllicationSteps = [
        {
            id: 1,
            step: 'Collecting Documents'
        },
        {
            id: 2,
            step: 'Applied'
        },
        {
            id: 3,
            step: 'Accepted'
        },
    ]

    const [steps, setSteps] = useState(apllicationSteps);

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 5,
            },
        })
    );

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = {
            programLevelName,
            duration,
            durationUnit,
            intake,
            testRequired,
            testScores: testRequired ? rows : [],
            applicationSteps: steps
        };
        console.log('Form Data:', formData);
    };

    function handleDragEnd(event: any) {
        const { active, over } = event;

        if (active.id !== over?.id) {
            const oldIndex = steps.findIndex((item) => item.id === active.id);
            const newIndex = steps.findIndex((item) => item.id === over?.id);
            setSteps((items) => arrayMove(items, oldIndex, newIndex));
        }
    }

    const handleOverallChange = (rowId: string, value: string) => {
        setRows((prev) =>
            prev.map((row) =>
                row.id === rowId
                    ? { ...row, overall: value === '' ? 0 : parseInt(value) }
                    : row
            )
        );
    };

    return (
        <>
            <form ref={ref} onSubmit={handleSubmit}>
                <div className='flex flex-col gap-6'>
                    <InputField
                        type='text'
                        id='program_level'
                        className='py-3'
                        label='Program Level Name'
                        value={programLevelName}
                        placeholder='Enter program level name'
                        onChange={(e) => setProgramLevelName(e.target.value)}
                    />
                    <div className='grid grid-cols-2 gap-2'>
                        <InputField
                            type='text'
                            id='duration'
                            label='Duration'
                            className='py-3'
                            value={duration}
                            placeholder='Enter Duration'
                            onChange={(e) => setDuration(e.target.value)}
                        />
                        <SelectField
                            options={[
                                { value: 'year', label: 'Year' },
                                { value: 'month', label: 'Month' },
                            ]}
                            className='py-6 mt-[26px]'
                            placeholder='Year/Month'
                            value={durationUnit}
                            onChange={(value) => setDurationUnit(value)}
                        />
                    </div>
                    <MultiSelectDropdown
                        label='Intake'
                        data={[
                            {
                                label:'Summer',
                                value: 'summer'
                            },
                            {
                                label:'Fall',
                                value:'fall'
                            },
                            {
                                label:'Spring',
                                value:'spring'
                            }
                        ]}
                        value={intake}
                        onChange={setIntake}
                    />

                    <div>
                        <div className='flex items-center mb-4 gap-3'>
                            <Label htmlFor='test' className='font-medium text-sm text-grayFive  block'>
                                Test & Language Scores
                            </Label>
                            <Switch className='border' classes='border' id='test' onCheckedChange={toggleTestVisibility} defaultChecked />
                        </div>
                        
                        {testRequired && 
                            rows.length > 0 && (
                                <div className='border border-tertiary/20 rounded-[8px] overflow-hidden'>
                                    
                                    <div className='grid grid-cols-7 gap-4 px-3.5 py-3 bg-primaryOne text-sm font-bold text-grayFive'>
                                        <div>Tests</div>
                                        <div>Overall</div>
                                        <div className='col-span-4 text-center'>Score Breakdown</div>
                                        <div>Actions</div>
                                    </div>
                                    
                                    {rows.map((row) => {
                                        const modules = getModulesForTest(row.test);
                                        return (
                                            <div
                                                key={row.id}
                                                className='grid grid-cols-7 gap-4 px-3.5 py-3 border-t border-grayOne bg-white text-grayFive'
                                            >
                                                <div className='flex flex-col gap-2'>
                                                    <select
                                                        value={row.test}
                                                        onChange={(e) =>
                                                            handleTestChange(row.id, e.target.value)
                                                        }
                                                        className='w-full p-1 border border-tertiary/20 rounded-[4px] text-sm focus:outline-none'
                                                    >
                                                        {testOptions.map((option) => (
                                                            <option key={option} value={option}>
                                                                {option}
                                                            </option>
                                                        ))}
                                                    </select>
                                                </div>

                                                <div className='flex flex-col gap-2'>
                                                    <input
                                                        type='number'
                                                        placeholder='Overall'
                                                        value={row.overall}
                                                        onChange={(e) => handleOverallChange(row.id, e.target.value)}
                                                        className='w-full p-1 border border-tertiary/20 rounded-[4px] text-sm font-semibold text-center'
                                                    />
                                                </div>
                                                
                                                <div className='flex flex-col gap-2'>
                                                    <input
                                                        type='number'
                                                        placeholder={modules[0]}
                                                        value={getScoreValue(row.scoreBreakdown.testOne)}
                                                        onChange={(e) =>
                                                            handleScoreChange(row.id, 'testOne', e.target.value)
                                                        }
                                                        className='w-full p-1 border border-tertiary/20 rounded-[4px] text-sm focus:outline-none'
                                                    />
                                                </div>
                                                
                                                <div className='flex flex-col gap-2'>
                                                    <input
                                                        type='number'
                                                        placeholder= {modules[1]}
                                                        value={getScoreValue(row.scoreBreakdown.testTwo)}
                                                        onChange={(e) =>
                                                            handleScoreChange(row.id, 'testTwo', e.target.value)
                                                        }
                                                        className='w-full p-1 border border-tertiary/20 rounded-[4px] text-sm focus:outline-none'
                                                    />
                                                </div>
                                                
                                                <div className='flex flex-col gap-2'>
                                                    <input
                                                        type='number'
                                                        placeholder={modules[2]}
                                                        value={getScoreValue(row.scoreBreakdown.testThree)}
                                                        onChange={(e) =>
                                                            handleScoreChange(row.id, 'testThree', e.target.value)
                                                        }
                                                        className='w-full p-1 border border-tertiary/20 rounded-[4px] text-sm focus:outline-none'
                                                    />
                                                </div>
                                                
                                                <div className='flex flex-col gap-2'>
                                                    <input
                                                        type='number'
                                                        placeholder={modules[3]}
                                                        value={getScoreValue(row.scoreBreakdown.testFour)}
                                                        onChange={(e) =>
                                                            handleScoreChange(row.id, 'testFour', e.target.value)
                                                        }
                                                        className='w-full p-1 border border-tertiary/20 rounded-[4px] text-sm focus:outline-none'
                                                    />
                                                </div>
                                                
                                                <div className='flex items-center justify-center'>
                                                    <button
                                                        type='button'
                                                        onClick={() => removeRow(row.id)}
                                                        disabled={rows.length === 1}
                                                        className={`w-4 h-4 rounded-full flex items-center justify-center transition-colors ${
                                                            rows.length === 1 
                                                                ? 'bg-gray-300 cursor-not-allowed' 
                                                                : 'bg-[#FF3B30]'
                                                        }`}
                                                        title='Remove row'
                                                    >
                                                        <svg
                                                            className={`w-3 h-3 ${rows.length === 1 ? 'text-gray-500' : 'text-white'}`}
                                                            fill='currentColor'
                                                            viewBox='0 0 20 20'
                                                        >
                                                            <path
                                                                fillRule='evenodd'
                                                                d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
                                                                clipRule='evenodd'
                                                            />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        );
                                    })}
                                    
                                    <div className='border-t border-grayOne px-3.5 py-3'>
                                        <button
                                            type='button'
                                            onClick={addNewRow}
                                            className='py-1 px-2 bg-primaryColor text-white flex items-center gap-1.5 rounded-full hover:bg-tertiary transition-colors'
                                        >
                                            Add
                                        </button>
                                    </div>
                                </div>
                            )
                        }
                    </div>

                    <div>
                        <div className='flex items-center mb-4 gap-3'>
                            <Label htmlFor='test' className='font-medium text-sm text-grayFive  block'>
                                Application Steps
                            </Label>
                            <Button type='button' className='text-primaryColor border border-primaryTwo py-1 px-2 rounded-full'>
                                <EditAction />
                                Edit
                            </Button>
                        </div>
                        <div className='border border-tertiary/20 rounded-[8px] overflow-hidden'>
                            <div className='grid grid-cols-5 place-items-center gap-4 px-3.5 py-3 bg-primaryOne text-sm font-bold text-grayFive'>
                                <div>Sequence</div>
                                <div className='col-span-3'>Step Title</div>
                                <div>Required?</div>
                            </div>

                            <DndContext
                                sensors={sensors}
                                collisionDetection={closestCenter}
                                onDragEnd={handleDragEnd}
                            >
                                <SortableContext
                                    items={steps.map((s) => s.id)}
                                    strategy={verticalListSortingStrategy}
                                >
                                    {steps.map((step, index) => (
                                        <DraggableRow key={step.id} step={step} sequence={index + 1} />
                                    ))}
                                </SortableContext>
                            </DndContext>
                        </div>
                    </div>

                    {/* <button 
                            type='submit'
                            className='py-2.5 px-4 bg-primaryColor text-white flex items-center gap-1.5 w-fit rounded-full hover:bg-tertiary transition-colors'
                        >
                            Save Program
                    </button> */}
                </div>
            </form>
        </>
    );
});

ProgramForm.displayName = 'ProgramForm';

export default ProgramForm;

function DraggableRow({ step, sequence }: { step: { id: number; step: string }, sequence: number }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: step.id });

  return (
    <div ref={setNodeRef}>
      <SortableRow
        step={step}
        attributes={attributes}
        listeners={listeners}
        transform={transform}
        transition={transition}
        isDragging={isDragging}
        sequence={sequence}
      />
    </div>
  );
}