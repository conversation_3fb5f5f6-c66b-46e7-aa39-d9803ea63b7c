import React, { useState } from 'react';
import InputField from '../../InputField';
import SelectField from '../../SelectField';
import { DatePicker } from '../../DatePicker';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import DatePickerWithRange from '../../DatepickerWithRange';
import MultiSelectDropdown from '../../MultiSelectDropdown';
import Plus from '@/app/assets/svg/plus';
import { DropdownOption } from '@/types';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' }
]

const UniEnrollementFeesForm = () => {
    const [intake, setIntake] = useState<DropdownOption[]>([]);
    const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
        from: undefined,
        to: undefined
    });
    const [effectiveDate, setEffectiveDate] = useState<Date | undefined>(undefined);
    const [refundableFee, setRefundableFee] = useState(false);
    const [showInPortal, setShowInPortal] = useState(false);
    const [isActive, setIsActive] = useState(false);
    
    // Form field states
    const [feesTitle, setFeesTitle] = useState('');
    const [programLevel, setProgramLevel] = useState('');
    const [currency, setCurrency] = useState('');
    const [tuitionFee, setTuitionFee] = useState('');
    const [applicationFee, setApplicationFee] = useState('');
    const [applicationFeeUni, setApplicationFeeUni] = useState('');
    const [applicationCharged, setApplicationCharged] = useState('');
    const [paymentDueIn, setPaymentDueIn] = useState('');

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        
        // Collect all form data
        const completeFormData = {
            fees_title: feesTitle,
            program_level: programLevel,
            intake: intake.map(item => item.value),
            dateRange: {
                from: dateRange.from?.toISOString(),
                to: dateRange.to?.toISOString()
            },
            currency,
            tuition_fee: tuitionFee,
            application_fee: applicationFee,
            application_fee_uni: applicationFeeUni,
            application_charged: applicationCharged,
            payment_due_in: paymentDueIn,
            effective_date: effectiveDate?.toISOString(),
            refundable_fee: refundableFee,
            show_in_portal: showInPortal,
            is_active: isActive
        };
        
        console.log('Complete Form Data:', completeFormData);
    }
    
    return (
        <form onSubmit={handleSubmit} className='space-y-[30px]'>
            <div className='flex flex-col gap-6'>
                <div className='grid grid-cols-2 gap-6'>
                    <InputField
                        id="fees_title"
                        placeholder="Enter fee title"
                        type="text"
                        label="Fees Title"
                        value={feesTitle}
                        onChange={(e) => setFeesTitle(e.target.value)}
                        className='py-3'
                    />
                    <SelectField
                        label="Program Level"
                        options={DemoSelectButtonData}
                        value={programLevel}
                        onChange={setProgramLevel}
                        className='py-5'
                    />
                    <MultiSelectDropdown 
                        label="Intake"
                        data={[
                            {
                                label: 'Summer',
                                value: 'summer',
                            },
                            {
                                label: 'Fall',
                                value: 'fall',
                            },
                            {
                                label: 'Spring',
                                value: 'spring',
                            },
                        ]}
                        value={intake}
                        onChange={setIntake}
                    />
                    <DatePickerWithRange
                        label='Applicable Academic Year'
                    />
                </div>
                <div className='grid grid-cols-2 gap-6'>
                    {/* <SelectField
                        label="Currency"
                        options={DemoSelectButtonData}
                        value={currency}
                        onChange={setCurrency}
                        className='py-5'
                    /> */}
                    <InputField
                        id="tuition_fee"
                        placeholder="Enter tuition fee"
                        type="text"
                        label="Tuition Fee"
                        sublabel='(Per semester)'
                        value={tuitionFee}
                        onChange={(e) => setTuitionFee(e.target.value)}
                        className='py-3'
                    />
                    <InputField
                        id="application_fee"
                        placeholder="Enter Application fee"
                        type="text"
                        label="Application Fee "
                        sublabel='(One-time, non-refundable fee)'
                        value={applicationFee}
                        onChange={(e) => setApplicationFee(e.target.value)}
                        className='py-3'
                    />
                </div>
                <div className='grid grid-cols-4 gap-6'>
                    <InputField
                        id="application_fee_uni"
                        placeholder="Enter fee"
                        type="text"
                        label="Application Fee Charged by University"
                        value={applicationFeeUni}
                        onChange={(e) => setApplicationFeeUni(e.target.value)}
                        className='py-3'
                        fieldNote='This is the amount Applygoal must pay per application.'
                    />
                    <InputField
                        id="application_charged"
                        placeholder="Enter fee"
                        type="text"
                        label="Applygoal Charged to student"
                        sublabel='(Application Fee)'
                        value={applicationCharged}
                        onChange={(e) => setApplicationCharged(e.target.value)}
                        className='py-3'
                        fieldNote='This is the amount Student must pay per application.'
                    /> 
                    <InputField
                        id="payment_due_in"
                        placeholder="ex., 15"
                        type="text"
                        sublabel='(Days)'
                        label="Payment Due In"
                        value={paymentDueIn}
                        onChange={(e) => setPaymentDueIn(e.target.value)}
                        className='py-3'
                    />
                    <DatePicker 
                        title= 'Fees Effective Date'
                        className='py-3'
                        value={effectiveDate}
                        onChange={setEffectiveDate}
                    />
                </div>
                <div className='grid grid-cols-3 gap-x-3 gap-y-6 '>
                    <div className="flex items-center space-x-2.5">
                        <Label className='text-sm font-medium text-graySix' htmlFor="refundable-fee">Refundable Fee to Student?</Label>
                        <Switch 
                            className='border border-grayTwo' 
                            id="refundable-fee" 
                            checked={refundableFee}
                            onCheckedChange={setRefundableFee}
                        />
                    </div>
                    <div className="flex items-center space-x-2.5">
                        <Label className='text-sm font-medium text-graySix' htmlFor="show-portal">Show Fee in Student Portal?</Label>
                        <Switch 
                            className='border border-grayTwo' 
                            id="show-portal" 
                            checked={showInPortal}
                            onCheckedChange={setShowInPortal}
                        />
                    </div>
                    <div className="flex items-center space-x-2.5">
                        <Label className='text-sm font-medium text-graySix' htmlFor="is-active">Is this fee currently active?</Label>
                        <Switch 
                            className='border border-grayTwo' 
                            id="is-active" 
                            checked={isActive}
                            onCheckedChange={setIsActive}
                        />
                    </div>
                </div>
            </div>
            <button className='py-2.5 px-4 bg-primaryThree text-primaryColor flex items-center gap-1.5 w-fit rounded-full'>
                <Plus />
                Add More
            </button>
            <div className='w-full py-4 px-[22px] bg-primaryFour border border-primaryColor/20 flex justify-end rounded-[10px] mt-2.5'>
                <button type='submit' className='px-4 py-2 bg-primaryColor rounded-full text-white font-semibold'>
                    Submit
                </button>
            </div>
        </form>
    )
}

export default UniEnrollementFeesForm
