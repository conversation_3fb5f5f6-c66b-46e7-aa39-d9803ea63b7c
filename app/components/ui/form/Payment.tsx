import React, { forwardRef } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import InputField from '../../InputField';
import { useAddBankDetailsMutation } from '@/lib/redux/api/addUniversityApi';
import { useForm, SubmitHandler, FieldValues } from 'react-hook-form';

interface PaymentProps {
    onSubmit?: React.FormEventHandler<HTMLFormElement>;
}

const Payment = forwardRef<HTMLFormElement, PaymentProps>(({ onSubmit }, ref) => {
    const [addBankDetails, { isLoading, isSuccess, error }] = useAddBankDetailsMutation();
    const { register, handleSubmit, reset } = useForm<FieldValues>();
    const universityId = useSelector((state: RootState) => state.university.universityId);

    const onSubmitHandler: SubmitHandler<FieldValues> = async (data) => {
        try {
            const payload = {
                universityId,
                bankName: data.bank_name,
                accountName: data.account_name,
                accountHolderName: data.account_holder_name,
                ibanSwiftCode: data['IBAN/SWIFT_code'],
                taxId: data.tax_id,
                vatNumber: data.vat_number,
            };
            await addBankDetails(payload).unwrap();
            alert('Bank details saved!');
            reset();
        } catch (err) {
            alert('Failed to save bank details');
        }
    };

    return (
        <form
            ref={ref}
            onSubmit={onSubmit ? onSubmit : handleSubmit(onSubmitHandler)}
        >
            <div className='flex flex-col gap-6'>
                <div className='grid grid-cols-2 gap-3'>
                    <InputField
                        id="bank_name"
                        placeholder="Insert Bank Name"
                        type="text"
                        label="Bank Name"
                        className='py-3'
                        {...register('bank_name')}
                    />
                    <InputField
                        id="account_number"
                        placeholder="A/C number"
                        type="text"
                        label="Account Number"
                        className='py-3'
                        {...register('account_name')}
                    />
                    <InputField
                        id="account_holder_name"
                        placeholder="A/C name"
                        type="text"
                        label="Account Holder Name"
                        className='py-3'
                        {...register('account_holder_name')}
                    />
                    <InputField
                        id="IBAN/SWIFT_code"
                        placeholder="code"
                        type="text"
                        label="IBAN/SWIFT Code"
                        className='py-3'
                        {...register('IBAN/SWIFT_code')}
                    />
                    <InputField
                        id="tax_id"
                        placeholder="Enter Tax ID"
                        type="text"
                        label="Tax ID"
                        className='py-3'
                        {...register('tax_id')}
                    />
                    <InputField
                        id="vat_number"
                        placeholder="Enter VAT number"
                        type="text"
                        label="VAT Number"
                        className='py-3'
                        {...register('vat_number')}
                    />
                </div>
            </div>
        </form>
    );
});

Payment.displayName = 'Payment';

export default Payment;
