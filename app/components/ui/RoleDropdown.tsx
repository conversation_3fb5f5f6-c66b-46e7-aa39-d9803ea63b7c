'use client'

import * as React from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

type MenuItem = {
    icon: React.ReactNode
    label: string
    description: string
    onClick: () => void
    separator?: boolean
    children?: MenuItem[]
}

type RoleDropdownProps = {
    menuItems: MenuItem[]
    triggerIcon?: React.ReactNode
}

const RoleDropdown: React.FC<RoleDropdownProps> = ({
    menuItems,
    triggerIcon,
}) => {
    const renderMenuItem = (item: MenuItem, index: number) => (
        <React.Fragment key={index}>
            <DropdownMenuItem className='gap-2 items-start cursor-pointer p-2 hover:bg-primaryOne' onClick={item.onClick}>
                <div>
                    {item.icon}
                </div>
                <div className='space-y-1.5'>
                    <p className="font-normal text-sm leading-none text-grayFive">{item.label}</p>
                    <p className="text-[10px] font-normal leading-none text-grayThree">{item.description}</p>
                </div>
            </DropdownMenuItem>
            {item.children?.map((child, childIndex) => (
                <DropdownMenuItem 
                    key={childIndex}
                    className='gap-2 items-start cursor-pointer p-2 hover:bg-primaryOne pl-8' 
                    onClick={child.onClick}
                >
                    <div>
                        {child.icon}
                    </div>
                    <div className='space-y-1.5'>
                        <p className="font-normal text-sm leading-none text-grayFive">{child.label}</p>
                        <p className="text-[10px] font-normal leading-none text-grayThree">{child.description}</p>
                    </div>
                </DropdownMenuItem>
            ))}
            {item.separator && <DropdownMenuSeparator />}
        </React.Fragment>
    )

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <button>
                    {triggerIcon}
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="-ml-60 space-y-1 p-3 rounded-[6px] bg-white drop-shadow-[0_1px_4px_rgba(0,0,0,0.25)] w-[270px]">
                {menuItems.map((item, index) => renderMenuItem(item, index))}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export default RoleDropdown
