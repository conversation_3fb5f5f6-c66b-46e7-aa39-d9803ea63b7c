import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Avatar from '@/app/assets/img/Avatar.png';
import PersonCheck from '@/app/assets/svg/PersonCheck';
import ClockHours from '@/app/assets/svg/ClockHours';
import FullTimeBadge from '@/app/assets/svg/FullTimeBadge';
import WorkOnsite from '@/app/assets/svg/WorkOnsite';

const TeamMembersCard = () => {
    return (
        <div className='bg-white rounded-[14px] pt-8 pb-5 px-5 inline-block space-y-4'>
            <div className='flex flex-col justify-center items-center space-y-4'>
                <div>
                    <Image src={Avatar} width={64} height={64} alt='d' />
                </div>
                <div className='text-center space-y-1.5'>
                    <h4 className='font-semibold text-lg leading-none text-graySix'><PERSON></h4>
                    <span className='font-normal text-sm leading-none text-grayFive'>Lead Counselor</span>
                    <div className='flex gap-1 items-center justify-center'>
                        <span className='font-normal text-sm leading-none text-grayFive'>ID:</span>
                        <span className='font-medium text-sm leading-none text-grayFive'>03</span>
                    </div>
                </div>
                <div className='flex gap-2.5'>
                    <div className='flex gap-2 items-center'>
                        <PersonCheck />
                        <p className='font-normal text-xs leading-none text-graySix'>Joined at 22 Oct, 2023</p>
                    </div>
                    <div className='flex gap-2 items-center'>
                        <ClockHours />
                        <p className='font-normal text-xs leading-none text-graySix'>48hr/week</p>
                    </div>
                </div>
                <div className='flex gap-2.5'>
                    <div className='flex gap-2 items-center'>
                        <FullTimeBadge />
                        <p className='font-normal text-xs leading-none text-graySix'>Full-time</p>
                    </div>
                    <div className='flex gap-2 items-center'>
                        <WorkOnsite />
                        <p className='font-normal text-xs leading-none text-graySix'>On-site</p>
                    </div>
                </div>
                <div className='rounded-md p-2 bg-primaryOne w-full text-center cursor-pointer'>
                    <Link 
                        href={'/'}
                        className='font-semibold text-sm leading-[18px] text-primaryColor w-full' 
                    >
                        See Details
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default TeamMembersCard