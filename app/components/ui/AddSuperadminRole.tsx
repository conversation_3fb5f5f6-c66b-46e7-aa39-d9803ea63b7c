'use client'

import * as React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';
import Avatar from '../../assets/img/avatar.png';
import AddCircle from '../../assets/svg/AddCircle';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

const adminLists = [
    {
      value: '<PERSON>',
      label: '<PERSON>',
    },
    {
      value: '<PERSON>',
      label: '<PERSON>',
    },
    {
      value: '<PERSON><PERSON><PERSON>',
      label: '<PERSON><PERSON><PERSON>',
    },
    {
      value: '<PERSON>',
      label: '<PERSON>',
    },
    {
      value: '<PERSON> Don<PERSON>',
      label: '<PERSON> Don<PERSON>',
    },
];

const AddSuperadminRole = () => {
    const [open, setOpen] = React.useState(false)
    const [value, setValue] = React.useState('')

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <button
                    role='combobox'
                    aria-expanded={open}
                >
                    <AddCircle />
                </button>
            </PopoverTrigger>
            <PopoverContent className='w-[300px] p-0'>
                <Command>
                    <CommandInput placeholder='Search' className='h-9 placeholder:font-medium placeholder:text-xs placeholder:leading-[18px] placeholder:text-grayThree' />
                    <CommandList>
                        <CommandEmpty>No admin found.</CommandEmpty>
                        <CommandGroup>
                            {adminLists.map((admin) => (
                                <CommandItem
                                    key={admin.value}
                                    className='p-2 text-sm font-normal leading-3.5 leading-none text-grayFive'
                                    value={admin.value}
                                    onSelect={(currentValue) => {
                                        setValue(currentValue === value ? '' : currentValue)
                                        setOpen(false)
                                    }}
                                >
                                    <div>
                                        <Image src={Avatar} alt="avatar" className='w-6 h-6' />
                                    </div>
                                    {admin.label}
                                <Check
                                    className={cn(
                                    'ml-auto text-primaryColor',
                                    value === admin.value ? 'opacity-100' : 'opacity-0'
                                    )}
                                />
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )};

export default AddSuperadminRole;