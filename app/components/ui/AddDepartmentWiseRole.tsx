'use client'

import * as React from 'react';
import DepartmentProfile from '../../assets/svg/DepartmentProfile';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';
import Avatar from '../../assets/img/avatar.png';
import AddCircle from '../../assets/svg/AddCircle';
import Cookies from 'js-cookie';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

const adminListsWithDepartment = [
    {
      name: 'HR',
    },
    {
      name: 'Accounts',
      employees: [
        {
            value: '<PERSON>',
            label: '<PERSON>',
        },
        {
            value: '<PERSON>',
            label: '<PERSON>',
        },
        {
            value: '<PERSON><PERSON><PERSON> <PERSON>',
            label: '<PERSON><PERSON><PERSON>',
        },
    ]},
    {
      name: 'Application',
    }
];

interface AddDepartmentWiseRoleProps {
    roleName: string;
    onRoleAdded?: (roleName: string, employeeName: string) => void;
}

const AddDepartmentWiseRole: React.FC<AddDepartmentWiseRoleProps> = ({ roleName, onRoleAdded }) => {
    const [open, setOpen] = React.useState(false)
    const [selectedDepartment, setSelectedDepartment] = React.useState('')
    const [value, setValue] = React.useState('')

    const handleEmployeeSelect = (employeeName: string) => {
        // Get existing roles from cookies
        const existingRoles = Cookies.get('selectedRoles');
        let rolesArray = existingRoles ? JSON.parse(existingRoles) : [];
        
        // Add new role-employee combination
        const newRoleEntry = {
            roleName: roleName,
            employeeName: employeeName,
            department: selectedDepartment,
            timestamp: new Date().toISOString()
        };
        
        // Check if this combination already exists
        const exists = rolesArray.some((role: any) => 
            role.roleName === roleName && role.employeeName === employeeName
        );
        
        if (!exists) {
            rolesArray.push(newRoleEntry);
            // Save back to cookies
            Cookies.set('selectedRoles', JSON.stringify(rolesArray), { expires: 7 }); // Expires in 7 days
            
            // Call the callback if provided
            if (onRoleAdded) {
                onRoleAdded(roleName, employeeName);
            }
        }
        
        setValue(employeeName);
        setOpen(false);
    };

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <button
                    role='combobox'
                    aria-expanded={open}
                >
                    <AddCircle />
                </button>
            </PopoverTrigger>
            <PopoverContent className='w-[350px] p-0'>
                <Command>
                    <CommandInput 
                        placeholder='Search' 
                        className='h-9 placeholder:font-medium placeholder:text-xs placeholder:leading-[18px] placeholder:text-grayThree' 
                        onValueChange={(value) => {
                            setValue(value)
                        }}
                    />
                    <CommandEmpty>No department found.</CommandEmpty>
                    <CommandList>
                    {/* <CommandEmpty>No department found.</CommandEmpty> */}

                        <CommandGroup className=''>
                            {adminListsWithDepartment.map((admin) => (
                                <React.Fragment key={admin.name}>
                                 <div 
                                     onClick={() => setSelectedDepartment(selectedDepartment === admin.name ? '' : admin.name)} 
                                     className={`cursor-pointer flex justify-between items-center p-2 ${selectedDepartment === admin.name ? 'bg-primaryOne rounded-t-[4px]' : ''}`}
                                 >
                                    <div className='flex gap-2 items-center'>
                                        <DepartmentProfile />
                                        <div className='flex flex-col'>
                                            <span className='text-[10px] font-normal leading-none text-grayThree'>Department</span>
                                            <span className='text-sm font-normal leading-none text-grayFive'>{admin.name}</span>
                                        </div>
                                    </div>
                                    <ChevronDown className={cn(
                                        'w-[18px] h-[18px] text-grayFive transition-transform duration-200',
                                        selectedDepartment === admin.name ? 'rotate-180' : ''
                                    )} />
                                </div>
                                {selectedDepartment === admin.name && (
                                    <div className='bg-primaryOne pb-2 pr-2 rounded-b-[4px]'>
                                        {admin.employees && admin.employees.length > 0 ? (
                                            admin.employees.map((employee, index) => (
                                                <CommandItem
                                                    key={index}
                                                    className={`data-[selected=true]:bg-none bg-primaryThree ml-10 p-2 text-sm font-normal leading-3.5 leading-none text-grayFive ${index === 0 ? '' : 'mt-1.5'}`}
                                                    value={employee.value}
                                                    onSelect={(currentValue) => {
                                                        handleEmployeeSelect(employee.label);
                                                    }}
                                                >
                                                    {employee.label}
                                                    <Check
                                                        className={cn(
                                                        'ml-auto text-primaryColor',
                                                        value === employee.value ? 'opacity-100' : 'opacity-0'
                                                        )}
                                                    />
                                                </CommandItem>
                                            ))
                                        ) : (
                                            <div className='p-2 text-sm font-normal leading-3.5 leading-none text-grayThree'>
                                                No employees available
                                            </div>
                                        )}
                                    </div>
                                )}
                                </React.Fragment>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )};

export default AddDepartmentWiseRole