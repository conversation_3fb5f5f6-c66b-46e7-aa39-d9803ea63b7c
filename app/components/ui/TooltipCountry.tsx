'use client';

import { useState } from 'react';
import { TooltipCountryProps } from '@/types';
import TooltipDirection from '@/app/assets/svg/TooltipDirection';

const TooltipCountry: React.FC<TooltipCountryProps> = ({
    logo,
    label
}) => {
    const [showTooltip, setShowTooltip] = useState(false);
    const handleMouseEnter = () => {
        setShowTooltip(true);
    };
    const handleMouseLeave = () => {
        setShowTooltip(false);
    };

    return (
        <div
            className='relative inline-block cursor-pointer'
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
        >
            {logo}
            {showTooltip && (
                <div className='absolute bottom-0 left-1/2 -translate-x-1/2'>
                    <div className='absolute bottom-6 left-1/2 -translate-x-1/2'>
                        <TooltipDirection />
                    </div>
                    <div className='absolute bottom-8 left-1/2 -translate-x-1/2 bg-secondaryColor rounded-[8px] py-2 px-3'>
                        <p className='text-white w-[175px] text-xs leading-[18px] font-semibold text-center'>
                            {label}
                        </p>
                    </div>
                </div>
             )}

        </div>
    )
}

export default TooltipCountry
