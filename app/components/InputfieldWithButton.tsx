import React, {useState} from 'react';
import { InputFieldWithButtonProps } from '@/types';

const InputfieldWithButton: React.FC<InputFieldWithButtonProps> = ({ 
    name, 
    setValue, 
    getValues,
    placeholder 
}) => {
    const [inputValue, setInputValue] = useState('');

    const handleAdd = () => {
        if (inputValue.trim()) {
            const currentValues = getValues(name) || [];
            const updatedValues = [...currentValues, inputValue];
            setValue(name, updatedValues, { shouldValidate: true });
            setInputValue('');
        }
    };
    return (
        <div className='mt-[25px] w-full max-w-full flex items-center placeholder:font-normal placeholder:text-base placeholder:text-grayTwo border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 rounded-lg overflow-hidden'>
            <input
                type='text'
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={placeholder || 'Enter value'}
                className='flex-1 min-w-0 px-2 py-1 outline-none text-gray-700 placeholder-grayTwo placeholder:text-base placeholder:font-normal'
            />
            <button
                type='button'
                onClick={handleAdd}
                className='ml-2 py-2.5 border-l border-tertiary border-opacity-20 px-3.5 bg-primaryThree font-medium text-base leading-6 rounded-r-lg text-primaryColor flex-shrink-0'
            >
                Add
            </button>
        </div>
    )
}

export default InputfieldWithButton