'use client';

import { useState } from 'react';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { format, isValid } from 'date-fns';
import { Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { PopoverClose } from '@radix-ui/react-popover';

interface DateValue {
    date: Date | null;
}

interface DateMonthCalendarProps {
    studentType: 'initial' | 'transfer';
    dateField:
        | 'applicationOpens'
        | 'applicationDeadline'
        | 'enrollmentDeadline';
    value: DateValue;
    onDateChange: (
        studentType: 'initial' | 'transfer',
        dateField:
            | 'applicationOpens'
            | 'applicationDeadline'
            | 'enrollmentDeadline',
        selectedDate: Date | null
    ) => void;
    disabled?: boolean
}

const DateMonthCalendar = ({
    studentType,
    dateField,
    value,
    onDateChange,
    disabled = false
}: DateMonthCalendarProps) => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const [selectedMonth, setSelectedMonth] = useState<number>(
        value.date?.getMonth() ?? today.getMonth()
    );

    const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    const daysInMonth = new Date(currentYear, selectedMonth + 1, 0).getDate();
    const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

    const handleSelectDate = (day: number) => {
        const newDate = new Date(currentYear, selectedMonth, day);
        onDateChange(studentType, dateField, newDate);
    };

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline" className={`w-full px-1.5 text-sm text-wrap justify-center items-center ${value.date ? 'text-graySix': 'text-grayTwo'}`}>
                    <Calendar />
                    <p>
                        {value.date && isValid(value.date) ? format(value.date, 'MMMM d') : 'MM/DD'}
                    </p>
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-72">
                <div className="mb-4">
                    <Select
                        value={selectedMonth.toString()}
                        onValueChange={(val) => setSelectedMonth(parseInt(val))}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select month" />
                        </SelectTrigger>
                        <SelectContent>
                            {months.map((month, idx) => (
                                <SelectItem key={month} value={idx.toString()}>
                                    {month}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="grid grid-cols-7 gap-2 text-center text-sm">
                    {days.map((day) => (
                        <button
                            key={day}
                            onClick={() => handleSelectDate(day)}
                            className={`w-8 h-8 rounded-full transition ${
                                value.date?.getDate() === day &&
                                value.date?.getMonth() === selectedMonth
                                    ? 'bg-blue-600 text-white'
                                    : 'hover:bg-gray-100 text-gray-700'
                            }`}
                            disabled={disabled}
                        >
                            {day}
                        </button>
                    ))}
                </div>

                <div className='flex justify-end gap-2 mt-4'>
                    <PopoverClose className=" border py-1.5 px-3.5 rounded-xl">
                        Cancel
                    </PopoverClose>
                </div>
            </PopoverContent>
        </Popover>
    );
};

export default DateMonthCalendar;
export type { DateValue };
