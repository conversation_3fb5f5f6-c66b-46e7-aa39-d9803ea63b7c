import React from 'react';
import Switcher from './Switcher';
import { NotificationsProps } from '@/types';

const Notifications: React.FC<NotificationsProps> = ({
    title,
    onClick,
    description,
}) => {
    return (
        <div className='py-4 flex justify-between'>
            <div className=''>
                <h3 className='font-medium text-base leading-5 text-graySix'>{title}</h3>
                <p className='mt-1.5 font-normal text-sm leading-4 text-grayFour'>{description}</p>
            </div>
            <Switcher classes='bg-grayTwo' className='border border-grayTwo' />
        </div>
    )
}

export default Notifications