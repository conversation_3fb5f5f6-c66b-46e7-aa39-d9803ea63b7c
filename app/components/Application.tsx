'use client'

import React, { useState, useImper<PERSON><PERSON>andle, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox'
// import { 
//     ArrowUpDown, 
//     ChevronDown, 
//     MoreHorizontal 
// } from 'lucide-react'
// import {
//   DropdownMenu,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuTrigger,
//   DropdownMenuContent,
//   DropdownMenuSeparator,
//   DropdownMenuCheckboxItem,
// } from '@/components/ui/dropdown-menu'
import Link from 'next/link';
import Image from 'next/image';
import Heading from './Heading';
// import { FormData } from '@/types';
// import { v4 as uuidv4 } from 'uuid';
// import React, { useState } from 'react';
// import NoteTextarea from './NoteTextarea';
// import { Input } from '@/components/ui/input';
import InformationBox from './InformationBox';
import Canada from '@/app/assets/svg/canada';
import { But<PERSON> } from '@/components/ui/button';
import ArrowUp from '@/app/assets/svg/arrowUp';
import SectionLayout from './layout/SectionLayout';
import AddNewFieldButton from './AddNewFieldButton';
import { Separator } from '@/components/ui/separator';
import ArrowDown from '@/app/assets/svg/arrowDown';
import CourseSearch from '@/app/assets/svg/courseSearch';
// import SelectAndSearchCombobox from './SelectAndSearchCombobox';
import { 
    useForm, 
    SubmitHandler, 
    Controller  
} from 'react-hook-form';
import AddDocumentOutline from '@/app/assets/svg/add-document-outline';
import AddApplication from '@/app/assets/svg/add-application-white.svg';
import HarvardUniversity from '../assets/svg/HarvardUniversity';
import { paidApplicationStatus } from '@/common';
import { 
    Select, 
    SelectItem, 
    SelectValue, 
    SelectContent, 
    SelectTrigger
} from '@/components/ui/select';
import {
    Accordion,
    AccordionItem,
    AccordionContent,
    AccordionTrigger,
} from '@/components/ui/accordion';  
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';

import Trash from '@/app/assets/svg/trash'
import SelectAndSearchCombobox from './SelectAndSearchCombobox';
import NoteTextarea from './NoteTextarea';
import { ApplicationRef } from '@/types';
import { useApplicationData } from '@/hooks/useApplicationData';
import { useSelector } from 'react-redux';
import { useGetUniversitiesQuery } from '@/lib/redux/api/studentApi';

type Payment = {
    id: string;
    appId: number;
    university: string;
    program: string;
    country: React.ReactNode;
    eslStartDate: string;
    intake: string;
    amount: number;
    action: React.ReactNode;
};

const data: Payment[] = [
    {
      id: '1',
      appId: 262,
      university: 'Massachusetts Institute of Technology (MIT)',
      program: '2 year Undergraduate Diploma',
      country: <Canada />,
      eslStartDate: 'N/A',
      intake: 'Sept 2024',
      amount: 150,
      action: <Button variant='ghost' size='sm'> <Trash /> </Button>,
    },
    {
      id: '2',
      appId: 159,
      university: 'University of California, Berkeley (UCB)',
      program: '3 year Undergraduate Diploma',
      country: <Canada />,
      eslStartDate: 'N/A',
      intake: 'Jan 2025',
      amount: 92,
      action: <Button variant='ghost' size='sm'> <Trash /> </Button>,
    },
    {
      id: '3',
      appId: 237,
      university: 'California Institute of Technology (Caltech)',
      program: `4 year Bachelor's Degree`,
      country: <Canada />,
      eslStartDate: 'N/A',
      intake: 'Mar 2025',
      amount: 23,
      action: <Button variant='ghost' size='sm'> <Trash /> </Button>,
    }
];
     
// const frameworks = [
//     {
//         value: 'next.js',
//         label: 'Next.js'
//     },
//     {
//         value: 'sveltekit',
//         label: 'SvelteKit'
//     },
//     {
//         value: 'nuxt.js',
//         label: 'Nuxt.js'
//     },
//     {
//         value: 'remix',
//         label: 'Remix'
//     },
//     {
//         value: 'astro',
//         label: 'Astro'
//     },
//     {
//         value: 'laravel',
//         label: 'laravel'
//     }
// ];
const Applications = [
    {
        appId: '234566',
        universityName: 'California Institute of Technology (Caltech)',
        universityLogo: HarvardUniversity,
        program: '2 year Undergraduate Diploma',
        country: HarvardUniversity,
        eslStartDate: 'N/A',
        intake: 'Oct 2024',
        Requirements: 'Refund',
        status: [
            {
                statusBg: '#1E62E0',
                status: 4,
            },
            {
                statusBg: '#78A1EC',
                status: 4,
            },
            {
                statusBg: '#F2A735',
                status: 4,
            },
            {
                statusBg: '#9FA0A6',
                status: 4,
            }
        ]
    },
    {
        appId: '234566',
        universityName: 'California Institute of Technology (Caltech)',
        universityLogo: HarvardUniversity,
        program: '2 year Undergraduate Diploma',
        country: HarvardUniversity,
        eslStartDate: 'N/A',
        intake: 'Oct 2024',
        Requirements: 'Refund',
        status: [
            {
                statusBg: '#1E62E0',
                status: 4,
            },
            {
                statusBg: '#78A1EC',
                status: 4,
            },
            {
                statusBg: '#F2A735',
                status: 4,
            },
            {
                statusBg: '#9FA0A6',
                status: 4,
            }
        ]
    },
    {
        appId: '234566',
        universityName: 'California Institute of Technology (Caltech)',
        universityLogo: HarvardUniversity,
        program: '2 year Undergraduate Diploma',
        country: HarvardUniversity,
        eslStartDate: 'N/A',
        intake: 'Oct 2024',
        Requirements: 'Refund',
        status: [
            {
                statusBg: '#1E62E0',
                status: 4,
            },
            {
                statusBg: '#78A1EC',
                status: 4,
            },
            {
                statusBg: '#F2A735',
                status: 4,
            },
            {
                statusBg: '#9FA0A6',
                status: 4,
            }
        ]
    },
    {
        appId: '234566',
        universityName: 'California Institute of Technology (Caltech)',
        universityLogo: HarvardUniversity,
        program: '2 year Undergraduate Diploma',
        country: HarvardUniversity,
        eslStartDate: 'N/A',
        intake: 'Oct 2024',
        Requirements: 'Refund',
        status: [
            {
                statusBg: '#1E62E0',
                status: 4,
            },
            {
                statusBg: '#78A1EC',
                status: 4,
            },
            {
                statusBg: '#F2A735',
                status: 4,
            },
            {
                statusBg: '#9FA0A6',
                status: 4,
            }
        ]
    },
    {
        appId: '234566',
        universityName: 'California Institute of Technology (Caltech)',
        universityLogo: HarvardUniversity,
        program: '2 year Undergraduate Diploma',
        country: HarvardUniversity,
        eslStartDate: 'N/A',
        intake: 'Oct 2024',
        Requirements: 'Refund',
        status: [
            {
                statusBg: '#1E62E0',
                status: 4,
            },
            {
                statusBg: '#78A1EC',
                status: 4,
            },
            {
                statusBg: '#F2A735',
                status: 4,
            },
            {
                statusBg: '#9FA0A6',
                status: 4,
            }
        ]
    }
];
const tableHead = ['AppID', 'University', 'Program', 'Intake', 'Country', 'ESL Start Date', 'Requirements', 'Status'];
const unpaidTableHead = ['AppID', 'University', 'Program', 'Country', 'ESL Start Date', 'Intake', 'Amount', 'Action'];

interface ApplicationField {
    id: number;
}

// Application component interfaces
interface ApplicationProps {
    studentId?: string | number;
    initialData?: any;
    onSave?: (data: any) => void;
}

const Application = React.forwardRef<ApplicationRef, ApplicationProps>(({ studentId, initialData, onSave }, ref) => {
    // Custom hook for application data management
    const {
        saveApplicationData,
        fetchApplicationData,
        applicationData: fetchedApplicationData,
        isLoading: isApplicationDataLoading
    } = useApplicationData(studentId);

    // Redux state
    const storedApplicationData = useSelector((state: any) => state.student.applicationData);

    // Fetch universities data
    const { data: universitiesData, isLoading: isUniversitiesLoading, error: universitiesError } = useGetUniversitiesQuery();
    

    const [addAnotherApplicationField, setAddAnotherApplicationField] = useState<ApplicationField[]>([
        { id: 0 }
    ]);

    // State for dynamic dropdown options
    const [universityOptions, setUniversityOptions] = useState<{ label: string; value: string }[]>([]);
    const [countryOptions, setCountryOptions] = useState<{ [key: string]: { label: string; value: string }[] }>({});
    const [campusOptions, setCampusOptions] = useState<{ [key: string]: { label: string; value: string }[] }>({});
    const [programOptions, setProgramOptions] = useState<{ [key: string]: { label: string; value: string }[] }>({});
    const [intakeOptions, setIntakeOptions] = useState<{ [key: string]: { label: string; value: string }[] }>({});
    const [courseOptions, setCourseOptions] = useState<{ [key: string]: { label: string; value: string }[] }>({});

    // Process universities data when available
    useEffect(() => {
        if (universitiesData?.status === 'success' && universitiesData.data) {
            // Format universities for dropdown
            const universities = universitiesData.data.universities.map((uni: any) => ({
                label: uni.name,
                value: uni.id.toString()
            }));
            setUniversityOptions(universities);

            // Pre-process all countries, campuses, programs, intakes, and courses
            const countries: { [key: string]: { label: string; value: string }[] } = {};
            const campuses: { [key: string]: { label: string; value: string }[] } = {};
            const programs: { [key: string]: { label: string; value: string }[] } = {};
            const intakes: { [key: string]: { label: string; value: string }[] } = {};
            const courses: { [key: string]: { label: string; value: string }[] } = {};

            universitiesData.data.forEach((uni: any) => {
                // Countries for each university
                countries[uni.id] = uni.countries?.map((country: any) => ({
                    label: country.name,
                    value: country.id.toString()
                })) || [];

                uni.countries?.forEach((country: any) => {
                    // Campuses for each country
                    campuses[country.id] = country.campuses?.map((campus: any) => ({
                        label: campus.name,
                        value: campus.id.toString()
                    })) || [];

                    country.campuses?.forEach((campus: any) => {
                        // Programs for each campus
                        programs[campus.id] = campus.programs?.map((program: any) => ({
                            label: program.name,
                            value: program.id.toString()
                        })) || [];

                        campus.programs?.forEach((program: any) => {
                            // Intakes for each program
                            intakes[program.id] = program.intakes?.map((intake: any) => ({
                                label: `${intake.name} (${intake.startDate})`,
                                value: intake.id.toString()
                            })) || [];

                            // Courses for each program
                            courses[program.id] = program.courses?.map((course: any) => ({
                                label: `${course.name} (${course.duration})`,
                                value: course.id.toString()
                            })) || [];
                        });
                    });
                });
            });

            setCountryOptions(countries);
            setCampusOptions(campuses);
            setProgramOptions(programs);
            setIntakeOptions(intakes);
            setCourseOptions(courses);
        }
    }, [universitiesData]);

    // Fetch application data when studentId is available
    useEffect(() => {
        if (studentId && !storedApplicationData) {
            fetchApplicationData();
        }
    }, [studentId, storedApplicationData, fetchApplicationData]);

    // Populate form when fetched data is available
    useEffect(() => {
        if (fetchedApplicationData && Array.isArray(fetchedApplicationData)) {
            // Update form fields with fetched data
            const formattedApplications = fetchedApplicationData.map((app: any) => ({
                university: app.universityId?.toString() || '',
                country: app.universityCountryId?.toString() || '',
                campus: app.universityCountryCampus?.toString() || '',
                program: app.programId?.toString() || '',
                intake: app.intakeId?.toString() || '',
                course: app.courseId?.toString() || '',
                note: app.note || ''
            }));

            setValue('applications', formattedApplications);

            // Update the field state to match the number of applications
            setAddAnotherApplicationField(
                formattedApplications.map((_, index) => ({ id: index }))
            );
        }
    }, [fetchedApplicationData]);

    // Helper functions to get options based on selections
    const getCountryOptionsForUniversity = (universityId: string) => {
        return countryOptions[universityId] || [];
    };

    const getCampusOptionsForCountry = (countryId: string) => {
        return campusOptions[countryId] || [];
    };

    const getProgramOptionsForCampus = (campusId: string) => {
        return programOptions[campusId] || [];
    };

    const getIntakeOptionsForProgram = (programId: string) => {
        return intakeOptions[programId] || [];
    };

    const getCourseOptionsForProgram = (programId: string) => {
        return courseOptions[programId] || [];
    };

    const handleAddField = () => {
        setAddAnotherApplicationField((prev: any) => [
            ...prev,
            { id: prev.length},
        ]);
    };

    const {
        register,
        control,
        getValues,
        setValue,
        watch,
        handleSubmit,
        formState: { errors }
        } = useForm({
        defaultValues: {
            applications: [
            {
                university: '',
                country: '',
                program: '',
                campus: '',
                intake: '',
                course: '',
                note: ''
            }
            ]
        }
    });

    // Watch form values to update dependent dropdowns
    const watchedApplications = watch('applications');

    const AcademicData = [
        {
            title: 'Student Type',
            info: ['International Student']
        },
        {
            title: 'Spouse/ Dependents',
            info: ['Yes']
        },
        {
            title: 'Preferred Subjects',
            info: [
                '1. Human Resource Management', 
                '2. Accounting',
                '3. Mathematics',
                '4. Others'
            ]
        },
        {
            title: 'Preferred Country',
            info: ['USA']
        },
    ]

    function removeApplicationField(index: number): void {
        setAddAnotherApplicationField((prev) => {
            const newFields = prev.filter((_, i) => i !== index);
            const currentApplications = getValues('applications');
            const updatedApplications = currentApplications.filter((_, i) => i !== index);
            setValue('applications', updatedApplications);
            return newFields;
        });
    }

    // Form submission handler


    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        saveForm: async () => {
            try {
                if (!studentId) {
                    return { success: false, error: "Student ID is required" };
                }

                const formData = getValues();
                const applications = formData.applications || [];

                if (applications.length === 0) {
                    return { success: false, error: "At least one application is required" };
                }

                // Use the custom hook to save application data
                const result = await saveApplicationData({ applications });

                if (result.success && onSave) {
                    onSave(result.data);
                }

                return result;
            } catch (error: any) {
                console.error('Error saving applications:', error);
                const errorMessage = error?.message || "An unexpected error occurred";
                return { success: false, error: errorMessage };
            }
        },
    }));

    return (
        <>
            <div className='my-5 rounded-[20px] py-[60px] text-center bg-white drop-shadow-[0_2px_2px_rgba(0, 0, 0, 0.05)]'>
                <p className='font-bold text-[28px] leading-[34px] text-graySix'>Discover Courses, Pursue Passion!</p>

                <div className='inline-block'>
                    <Link 
                        href={'/course-finder'} 
                        className='bg-primaryColor flex gap-3 mt-10 rounded-[50px] py-4 px-[22px] border border-[#1E62E0] border-opacity-20'
                    >
                        <CourseSearch />
                        <span className='font-semibold text-base leading-[20px] text-white'>Search courses</span>
                    </Link>
                </div>
            </div>
            <Accordion type='single' collapsible>
                <AccordionItem 
                    className='bg-white rounded-2xl px-4 drop-shadow-[0_2px_5px_rgba(0, 0, 0, 0.05)]' 
                    value='item-1'
                >
                    <AccordionTrigger
                        className='font-semibold text-base leading-[20px] text-graySix justify-between hover:underline-0 hover:no-underline'
                        openIcon={ArrowUp}
                        closeIcon={ArrowDown}
                    >
                        I know, where to apply.
                    </AccordionTrigger>
                    <AccordionContent>
                        <Separator className='border-b border-tertiary border-opacity-20' />
                        <div className=''>
                            <h3 className='pt-[30px] font-bold text-xl leading-[25px] text-graySix'>Create Application</h3>

                            {addAnotherApplicationField.map((field: any, index: number) => (
                            <div
                                key={field.id}
                                className={`pt-[30px] ${index > 0 ? 'relative border-t-2 border-dotted border-gray-300 mt-10' : ''}`}
                            >
                                {/* Header with application number and remove icon */}
                                {index > 0 && (
                                <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold text-primaryColor">
                                    Application {index + 1}
                                </h3>
                                <div className="relative group inline-block">
                                    <button
                                        type="button"
                                        onClick={() => removeApplicationField(index)}
                                        className="text-red-500 hover:text-red-700 text-sm"
                                    >
                                        ❌
                                    </button>
                                    <span className="absolute bottom-full mb-1 -left-1/2 -translate-x-1/2 hidden group-hover:block text-xs text-red-600 bg-white px-1 rounded shadow-sm">
                                        Delete
                                    </span>
                                    </div>
                                </div>)}

                                {/* Application form fields */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <Controller
                                    name={`applications.${field.id}.university`}
                                    control={control}
                                    render={({ field: formField }) => (
                                    <SelectAndSearchCombobox
                                        options={universityOptions}
                                        label="University"
                                        selectedValue={formField.value}
                                        onChange={(value) => {
                                            formField.onChange(value);
                                            // Clear dependent fields when university changes
                                            setValue(`applications.${field.id}.country`, '');
                                            setValue(`applications.${field.id}.campus`, '');
                                            setValue(`applications.${field.id}.program`, '');
                                            setValue(`applications.${field.id}.intake`, '');
                                            setValue(`applications.${field.id}.course`, '');
                                        }}
                                    />
                                    )}
                                />

                                <Controller
                                    name={`applications.${field.id}.country`}
                                    control={control}
                                    render={({ field: formField }) => {
                                        const currentUniversity = watchedApplications?.[field.id]?.university;
                                        const countryOpts = currentUniversity ? getCountryOptionsForUniversity(currentUniversity) : [];
                                        return (
                                        <SelectAndSearchCombobox
                                            options={countryOpts}
                                            label="Country"
                                            type="select"
                                            selectedValue={formField.value}
                                            onChange={(value) => {
                                                formField.onChange(value);
                                                // Clear dependent fields when country changes
                                                setValue(`applications.${field.id}.campus`, '');
                                                setValue(`applications.${field.id}.program`, '');
                                                setValue(`applications.${field.id}.intake`, '');
                                                setValue(`applications.${field.id}.course`, '');
                                            }}
                                        />
                                        );
                                    }}
                                />

                                <Controller
                                    name={`applications.${field.id}.campus`}
                                    control={control}
                                    render={({ field: formField }) => {
                                        const currentCountry = watchedApplications?.[field.id]?.country;
                                        const campusOpts = currentCountry ? getCampusOptionsForCountry(currentCountry) : [];
                                        return (
                                        <SelectAndSearchCombobox
                                            options={campusOpts}
                                            label="Campus"
                                            type="select"
                                            selectedValue={formField.value}
                                            onChange={(value) => {
                                                formField.onChange(value);
                                                // Clear dependent fields when campus changes
                                                setValue(`applications.${field.id}.program`, '');
                                                setValue(`applications.${field.id}.intake`, '');
                                                setValue(`applications.${field.id}.course`, '');
                                            }}
                                        />
                                        );
                                    }}
                                />

                                <Controller
                                    name={`applications.${field.id}.program`}
                                    control={control}
                                    render={({ field: formField }) => {
                                        const currentCampus = watchedApplications?.[field.id]?.campus;
                                        const programOpts = currentCampus ? getProgramOptionsForCampus(currentCampus) : [];
                                        return (
                                        <SelectAndSearchCombobox
                                            options={programOpts}
                                            label="Program"
                                            type="select"
                                            selectedValue={formField.value}
                                            onChange={(value) => {
                                                formField.onChange(value);
                                                // Clear dependent fields when program changes
                                                setValue(`applications.${field.id}.intake`, '');
                                                setValue(`applications.${field.id}.course`, '');
                                            }}
                                        />
                                        );
                                    }}
                                />

                                <Controller
                                    name={`applications.${field.id}.intake`}
                                    control={control}
                                    render={({ field: formField }) => {
                                        const currentProgram = watchedApplications?.[field.id]?.program;
                                        const intakeOpts = currentProgram ? getIntakeOptionsForProgram(currentProgram) : [];
                                        return (
                                        <SelectAndSearchCombobox
                                            options={intakeOpts}
                                            label="Intake"
                                            type="select"
                                            selectedValue={formField.value}
                                            onChange={formField.onChange}
                                        />
                                        );
                                    }}
                                />

                                <Controller
                                    name={`applications.${field.id}.course`}
                                    control={control}
                                    render={({ field: formField }) => {
                                        const currentProgram = watchedApplications?.[field.id]?.program;
                                        const courseOpts = currentProgram ? getCourseOptionsForProgram(currentProgram) : [];
                                        return (
                                        <SelectAndSearchCombobox
                                            options={courseOpts}
                                            label="Course"
                                            type="select"
                                            selectedValue={formField.value}
                                            onChange={formField.onChange}
                                        />
                                        );
                                    }}
                                />
                                </div>

                                <div className="mt-6">
                                <NoteTextarea {...register(`applications.${field.id}.note`)} />
                                </div>
                            </div>
                            ))}

                            
                            <AddNewFieldButton 
                                onClick={handleAddField}
                                title='Add Another Application'
                            />
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
            {/* <SectionLayout heading={'My Information'}>
                <h2 className='font-semibold text-xl text-graySix'>Academic</h2>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {AcademicData.map((data, index) => (
                        <InformationBox key={index} data={data} />
                    ))}
                </div>
            </SectionLayout>
            <div className='flex justify-between pt-10 pb-5'>
                <Heading level='h2'>
                    Unpaid Applications
                </Heading>
                <Button className='py-2.5 px-5 rounded-[50px] bg-primaryColor text-white md:text-sm text-xs leading-[17px] font-semibold'>
                    <AddDocumentOutline />
                    <span>Add Application</span>
                </Button>
            </div>
            <SectionLayout>
                <div className='w-full'>
                    <div className='rounded-[20px]'>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>
                                        <Checkbox className='w-3.5 h-3.5 rounded border-[0.67px] border-primaryColor' />
                                    </TableHead>
                                    {unpaidTableHead.map((th, index) => (
                                        <TableHead 
                                            key={index} 
                                            className='font-bold text-xs leading-5 tracking-[0.4px] text-grayFive'
                                        >
                                            {th}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {data.length > 0 ? (
                                    data.map((row) => (
                                        <TableRow key={row.id}>
                                            <TableCell className='w-[60px]'>
                                                <Checkbox className='w-3.5 h-3.5 rounded border-[0.67px] border-primaryColor' />
                                            </TableCell>
                                            <TableCell className='font-normal text-xs tracking-[0.25px] text-graySix w-[80px]'>{row.appId}</TableCell>
                                            <TableCell className='font-normal text-xs tracking-[0.25px] text-graySix w-[360px]'>{row.university}</TableCell>
                                            <TableCell className='font-normal text-xs tracking-[0.25px] text-graySix w-[232px]'>{row.program}</TableCell>
                                            <TableCell className='font-normal text-xs tracking-[0.25px] text-graySix w-[120px]'>{row.country}</TableCell>
                                            <TableCell className='w-[130px]'>
                                                <Select defaultValue={row.eslStartDate}>
                                                    <SelectTrigger className='w-[120px] h-0 font-normal text-xs text-[#1E62E0] rounded-[50px] border-[0.5px] border-[#1E62E0] py-3 border-opacity-30 px-2.5'>
                                                        <SelectValue className='' placeholder='Select intake' />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value='N/A'>N/A</SelectItem>
                                                        <SelectItem className='' value='Sept 2024'>Sept 2024</SelectItem>
                                                        <SelectItem value='Jan 2025'>Jan 2025</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </TableCell>
                                            <TableCell className='w-[130px]'>
                                                <Select defaultValue={row.intake}>
                                                    <SelectTrigger className='w-[120px] h-0 font-normal text-xs text-[#FF3B30] rounded-[50px] border-[0.25px] border-graySix py-3 px-2.5'>
                                                        <SelectValue className='' placeholder='Select intake' />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem className='' value='Sept 2024'>Sept 2024</SelectItem>
                                                        <SelectItem value='Jan 2025'>Jan 2025</SelectItem>
                                                        <SelectItem value='Mar 2025'>Mar 2025</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </TableCell>
                                            <TableCell className='font-normal text-xs tracking-[0.25px] text-graySix w-[110px]'>
                                                {new Intl.NumberFormat('en-US', {
                                                    style: 'currency',
                                                    currency: 'USD',
                                                }).format(row.amount)}
                                            </TableCell>
                                            <TableCell className='w-[58px]'>{row.action}</TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                <TableRow>
                                    <TableCell colSpan={9} className='text-center'>
                                    No results.
                                    </TableCell>
                                </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </SectionLayout>
            <div className='flex flex-col md:flex-row justify-between py-6'>
                <h2 className='font-semibold text-xl text-graySix'>Paid Applications</h2>
                <div className='flex md:mt-0 mt-2 gap-1.5 md:gap-5'>
                    {paidApplicationStatus.map((status, index) => (
                        <div 
                            key={index} 
                            className='flex gap-2 items-center'
                        >
                            <span 
                                className='w-[18px] h-1 rounded-[100px]'
                                style={{ backgroundColor: status.statusBarColor }}
                            >
                            </span>
                            <span className='font-normal text-grayFour text-xs leading-4'>{status.status}</span>
                        </div>
                    ))}
                </div>
            </div>
            <SectionLayout className={'!p-0'}>
                <Table>
                    <TableHeader>
                        <TableRow>
                            {tableHead.map((th, index) => (
                                <TableHead 
                                    key={index} 
                                    className='px-6 py-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                >
                                    {th}
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {Applications.map((application, index) => (
                            <TableRow className='hover:bg-primaryFour' key={index}>
                                <TableCell 
                                    className='h-0 px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    {application.appId}
                                </TableCell>
                                <TableCell 
                                    className='flex gap-2.5 h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    <application.country />
                                    <p 
                                        className='font-normal tracking-[0.25px] text-xs leading-5 text-graySix'
                                    >
                                        {application.universityName}
                                    </p> 
                                </TableCell>
                                <TableCell 
                                    className='h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    {application.program}
                                </TableCell>
                                <TableCell 
                                    className='h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    {application.intake}
                                </TableCell>
                                <TableCell 
                                    className='h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    <application.country />
                                </TableCell>
                                <TableCell 
                                    className='h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    {application.eslStartDate}
                                </TableCell>
                                <TableCell 
                                    className='h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    {application.Requirements}
                                </TableCell>
                                <TableCell 
                                    className='h-0 px-6 py-4 font-normal text-xs tracking-[0.4px] text-graySix'
                                >
                                    <div className='flex gap-4'>
                                        {application.status.map((item, index) => (
                                            <span 
                                                className='flex justify-center items-center w-5 h-5 border rounded-[10px] py-[5px] px-1.5' 
                                                key={index}
                                                style={{ borderColor: item.statusBg, color: item.statusBg }}
                                            >
                                                {item.status}
                                            </span>
                                        ))}
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </SectionLayout> */}
        </>
    );
});

Application.displayName = 'Application';

export default Application;