// 'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Direction from '../../assets/svg/Direction';
import ArrowRight from '../../assets/svg/ArrowRight';
import {webinar, onGoingApplications } from '@/common';
import StatCardLayout from '../StatCardLayout';
import StatInfoCardBig from '../StatInfoCardBig';
import ApplyGoalWhite from '../../assets/svg/ApplyGoalWhite.svg';
import GlobalStudent from '@/app/assets/svg/global-student.svg';
import EnrollWebinarCard from '../EnrollWebinarCard';
import ApplicationStatus from '../ApplicationStatus';
import BannerNotification from '../BannerNotification';
import DashboardLayout from '../layout/DashboardLayout';
import { MultipleLineChart } from '../MultipleLineChart';
import WideNotificationBar from '../WideNotificationBar';
import GlobalUniversities from '@/app/assets/svg/global-universities.svg';
import BannerNotificationIcon from '../../assets/svg/BannerNotificationIcon';
import StudentAgencyDashboardBanner from '../../assets/svg/StudentAgencyDashboardBanner';

const AgencyStudent = () => {
    return (
        <DashboardLayout>
            <div>
                <WideNotificationBar
                    title="Short Summary of What the User Needs to Know"
                    message="Additional information and, optionally, any next steps the user should expect."
                />
            </div>
            <section className=' mt-10 bg-white rounded-[24px] px-[50px] py-16 flex items-center justify-between drop-shadow-[2_2px_12px_rgba(55,125,255,0.04)]'>
                <div className='relative'>
                    <h1>
                        <span className='uppercase font-outline-color-4 font-outline-4 text-white text-[64px] leading-none font-bold'>
                            YOUR DREAM TO
                        </span> <br />
                        <p className='text-primaryColor uppercase font-outline-color-4 font-outline-4 text-[64px] leading-none font-bold'>
                            STUDY ABROAD {' '}
                            <span className='uppercase font-outline-color-4 font-outline-4 text-white text-[64px] leading-none font-bold'>STARTS HERE.</span>
                        </p> 
                        <p className='uppercase font-outline-color-4 font-outline-4 text-white text-[64px] leading-none font-bold'>
                           ACHIEVE IT WITH {' '}
                            <span className='text-primaryColor uppercase font-outline-color-4 font-outline-4 text-[64px] leading-none font-bold'>FICC!</span>
                        </p> 
                    </h1>
                    <div className='absolute -right-10 -bottom-10'>
                        <Direction />
                    </div>
                </div>
                <div>
                    <StudentAgencyDashboardBanner />
                </div>
            </section>

            <div className='mt-10'>
                <BannerNotification
                    CompanyLogo={ApplyGoalWhite}
                    description={`'Fast-Track Your Student Visa: Join Our Free Webinar!'`}
                    BannerImage={<BannerNotificationIcon />}
                />
            </div>
            <div className='flex gap-6 mt-10 pb-12'>
                <div className='w-[80%] space-y-10'>
                    <StatCardLayout>
                        <div className='py-4 px-6 space-y-[22px]'>
                            <div className='flex justify-between items-center'>
                                <h2 className='md:text-2xl text-base md:leading-[29px] leading-5 font-bold text-primaryColor'>Your Ongoing Applications</h2>
                                <Link href={'/all-applications'} className='font-semibold md:text-sm text-[10px] md:leading-[17px] leading-[14px] text-primaryColor inline-flex items-center gap-2'>
                                    <span>View all</span>
                                    <ArrowRight />
                                </Link>
                            </div>
                            <div>
                                <ul className='space-y-1.5'>
                                    {onGoingApplications.map((app, index) => (
                                        <li
                                            key={index}
                                            className=' py-2.5 flex space-x-4'
                                        >
                                            <Image
                                                src={app.logo}
                                                alt={`${app.university} logo`}
                                                className='w-12 h-12 rounded-full'
                                            />
                                            <div className='grid md:grid-cols-4 grid-cols-1 gap-3 items-center w-full'>
                                                <div className='md:col-span-2 space-y-1.5'>

                                                    <h3 className='text-base leading-[18px] font-semibold text-graySix'>
                                                        {app.title}
                                                    </h3>
                                                    <p className='text-sm leading-[18px] font-normal text-graySix'>{app.university}</p>
                                                    <p className='text-xs leading-[15px] font-normal text-grayFour'>{app.location}</p>
                                                </div>
                                                <div className='flex md:justify-center justify-start'>
                                                    <p className='text-[13px] leading-4 font-medium text-grayFour text-start'>Created on: <br />{app.createdOn}</p>
                                                </div>
                                                <span className={`text-[13px] md:pl-10 flex justify-start leading-4 font-semibold text-primaryColor md:text-end text-start `}>
                                                    <ApplicationStatus status={app.status} />
                                                </span>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </StatCardLayout>
                    <StatCardLayout>
                        <MultipleLineChart />
                    </StatCardLayout>
                </div>
                <div className='w-[20%] space-y-6'>
                    <StatCardLayout>
                        <EnrollWebinarCard
                            totalEnrollee = {webinar[0].totalEnrollee}
                            title={webinar[0].title}
                            imageSrc={webinar[0].bannerImage}
                            date={webinar[0].date}
                            startTime={webinar[0].startTime}
                            endTime={webinar[0].endTime}
                            enrolled={webinar[0].enrollee}
                        />
                    </StatCardLayout>
                    <div>
                        <StatCardLayout className='h-full'>
                            <StatInfoCardBig
                                imageSrc={GlobalStudent}
                                description='Global Students'
                                number='3345'
                            />
                        </StatCardLayout>
                    </div>
                    
                    <div>
                        <StatCardLayout className='h-full'>
                            <StatInfoCardBig
                                imageSrc={GlobalUniversities}
                                description='Global Universities'
                                number='27'
                            />
                        </StatCardLayout>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
};

export default AgencyStudent;