import React from 'react';
import { <PERSON><PERSON>hart<PERSON> } from '@/app/components/PieCharts';
import UpcomingEvent from '@/app/components/UpcomingEvent';
import EventsIcon from '@/app/assets/svg/EventsIcon.svg';
import OnlineIcon from '@/app/assets/svg/OnlineIcon.svg';
import StatCardLayout from '@/app/components/StatCardLayout';
import MultipleBarChart from '@/app/components/MultipleBarChart';
import CustomFunnelChart from '@/app/components/CustomFunnelChart';
import StatInfoCardSmall from '@/app/components/StatInfoCardSmall';
import VerticalTabLayout from '@/app/components/VerticalTabLayout';
import OfficeVisitIcon from '@/app/assets/svg/OfficeVisitIcon.svg';
import AssociatesIcon from '@/app/assets/svg/AssociatesIcon.svg';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { MultipleLineChart } from '@/app/components/MultipleLineChart';
import { AreaChartGradient } from '@/app/components/AreaChartGradient';
import CounselorActivities from '@/app/components/CounselorActivities';
import CounselorBarChartIcon from '@/app/assets/svg/counselor-bar-chart-icon';
import StatCardVisitorIcon from '@/app/assets/svg/stat-card-visitor-icon.svg';
import StatCardStudentIcon from '@/app/assets/svg/stat-card-student-icon.svg';
import StatCardAcceptedIcon from '@/app/assets/svg/stat-card-accepted-icon.svg';
import StatCardRejectedIcon from '@/app/assets/svg/stat-card-rejected-icon.svg';
import StatCardApplicationIcon from '@/app/assets/svg/stat-card-application-icon.svg';
import { BarChartDataCounselor, CounselorBarChartData, LeadConversionInfoData, LeadDemographicData, UniversityOnboardingData } from '@/common';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { SingleBarChart } from '@/app/components/SingleBarChart';
import { DonutChart } from '@/app/components/DonutChart';
import { ChartConfig } from '@/components/ui/chart';
import DatepickerWithRange from '@/app/components/DatepickerWithRange';


const chartConfig = {
    application: {
        label: 'Applications',
        color: '#184EB3',
    },
    accepted: {
        label: 'Accepted',
        color: '#1E62E0',
    },
    rejected: {
        label: 'Rejected',
        color: '#E7EFFF',
    },
};

const PieChartData = [
    { status: "New", value: 96, fill: "#1E62E0" },
    { status: "Contacted", value: 90, fill: "#009CDE" },
    { status: "Follow-up", value: 65, fill: "#144296" },
    { status: "Converted", value: 45, fill: "#80ACFF" },
    { status: "Lost", value: 20, fill: "#537FF1" },
];

  const LeadsChartConfig = [
    { 
        dataKey: "Leads", 
        label: "Leads", 
        color: "#1E62E0", 
        strokeWidth: 5 
    },
];

const LeadDemoChartConfig = {
    agency: { 
        label: 'Category', 
        color: '#1E62E0', 
        name: 'Agency Name' 
    }
}

const revenueChartConfig = {
    value: {
        label: 'Value',
    },
    'Overall Revenue': {
        label: 'Overall Revenue',
        color: '#D2E3FC',
    },
    'Net Profit': {
        label: 'Net Profit',
        color: '#1E62E0',
    },
} satisfies ChartConfig;

const revenueData = {
    'This Year': [
        { category: "Male", value: 1000, fill: "#E3E7FC" },
        { category: "Female", value: 600, fill: "#1E62E0" },
        // { category: "Service Charge", value: 400, fill: "#FF6384" },
    ],
    'This Month': [
        { category: "Male", value: 800, fill: "#E3E7FC" },
        { category: "Female", value: 500, fill: "#1E62E0" },
        // { category: "Service Charge", value: 300, fill: "#FF6384" },
    ],
    // Add more ranges as needed
};
const socialLeadsData = {
    'This Year': [
        { category: "Website", value: 60, fill: "#1E62E0" },
        { category: "Social Media Ads", value: 25, fill: "#144296" },
        { category: "Other", value: 15, fill: "#80ACFF" },
    ],
    'This Month': [
        { category: "Website", value: 40, fill: "#1E62E0" },
        { category: "Social Media Ads", value: 25, fill: "#144296" },
        { category: "Other", value: 35, fill: "#80ACFF" },
    ],
    // Add more ranges as needed
};

const options = ['This Year', 'This Month'];
const LeadsOptions = ['This Year', 'This Month'];

const Councelor = () => {
    const general = () => {
        return (
            <>
            <div className='flex flex-col gap-10 py-10'>
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={StatCardVisitorIcon}
                            description='Visitor'
                            number={523}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={StatCardStudentIcon}
                            description='Students'
                            number={120}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={StatCardApplicationIcon}
                            description='Applications'
                            number={134}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={StatCardAcceptedIcon}
                            description='Accepted'
                            number={96}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={StatCardRejectedIcon}
                            description='Rejected'
                            number={54}
                        />
                    </StatCardLayout>
                </div>
                <StatCardLayout>
                    <MultipleBarChart 
                        title='Students' 
                        icon={<CounselorBarChartIcon />}
                        chartConfig={chartConfig} 
                        data={BarChartDataCounselor} 
                    />
                </StatCardLayout>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <CounselorActivities />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <UpcomingEvent />
                    </StatCardLayout>
                </div>
            </div>
            </>
        )
    };

    const leads = () => {
        return (
            <div className='flex flex-col gap-10 py-10'>
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={OfficeVisitIcon}
                            description='Office Visit'
                            number={120}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={OnlineIcon}
                            description='Online'
                            number={134}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={AssociatesIcon}
                            description='Associates'
                            number={40}
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <StatInfoCardSmall
                            imageSrc={EventsIcon}
                            description='Events'
                            number={34}
                        />
                    </StatCardLayout>
                </div>
                <div className=' grid grid-cols-2 gap-6'>
                    <StatCardLayout className='h-full rounded-[20px]'>
                        <PieCharts 
                            title="Lead Status" 
                            data={PieChartData}
                            showLegendLayout='right'
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        {/* <MultipleLineChart /> */}
                        <AreaChartGradient 
                            title='Lead Conversion Info'
                            data={LeadConversionInfoData}
                            lines={LeadsChartConfig}
                            xAxisKey='month'
                            showLegends = {false}
                            xAxisHeight={60}
                            chartContainerClassName='max-h-[380px]'
                        />
                    </StatCardLayout>
                    <StatCardLayout>
                        <CustomFunnelChart />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <DonutChart 
                            chartConfig={revenueChartConfig}
                            data={socialLeadsData['This Year']}
                            title="Social Leads"
                            legendPosition='right'
                            chartClassName='max-h-[340px]'
                            innerCircleRedius={75}
                        />
                    </StatCardLayout>
                </div>

                <div className=' grid grid-cols-3 gap-6'>
                    <StatCardLayout className='col-span-2'>
                        <SingleBarChart 
                            chartConfig={LeadDemoChartConfig} 
                            data={LeadDemographicData} 
                            title='Lead Demographics'
                            chartHeightClass='max-h-[340px]'
                            xAxisLabelAngle={-45}
                            showLegend = {true}
                            xAxisHeight={100}
                            xAxisTickMargin={10}
                        />
                    </StatCardLayout>
                    <StatCardLayout className='col-span-1 h-full'>
                        <DonutChart 
                            chartConfig={revenueChartConfig}
                            data={revenueData['This Year']} // Use a specific dataset directly
                            title="Revenue Breakdown"
                            legendPosition='bottom'
                            chartClassName='h-[315px]'
                            innerCircleRedius={55}
                        />
                    </StatCardLayout>
                </div>
                
            </div>
        )
    };

    const tabContentsCounselor = [
        {
            value: 'general',
            label: 'General',
            content: general
        },
        {
            value: 'leads',
            label: 'Leads',
            content: leads
        }
    ];

    const tabContents = tabContentsCounselor.map((tab) => ({
        value: tab.value,
        children: <tab.content />,
    }));

    return (
        <DashboardLayout>
            <div className="flex flex-col gap-10 py-5">
                {/* Title Section (NOT sticky) */}
                <div className="sticky top-0 z-20 flex justify-between items-center bg-primaryOne">
                    <h1 className=" text-[28px] leading-[34px] font-bold text-graySix">
                        Welcome Josh!
                    </h1>
                    <div className=" py-4" >
                        <DatepickerWithRange />
                    </div>
                </div>
                <div>
                    <VerticalTabLayout 
                        tabMenu={tabContentsCounselor}
                        tabContents={tabContents}
                        defaultValue='general'
                        tabListClassName='!p-0 max-w-[1193px] md:!justify-start sticky top-[70px] z-10 '
                        tabContentClassName = 'pb-[96px]'
                        tabTriggerClassName=''
                    />
                </div>
            </div>
        </DashboardLayout>
    );
};

export default Councelor;
