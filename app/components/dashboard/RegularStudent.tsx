'use client';
import UniversityList from '../UniversityList';

import Link from 'next/link';
import Image from 'next/image';
import AddIcon from '@/app/assets/svg/add-icon.svg';
import UpcomingEvent from '../UpcomingEvent';
import { webinar, onGoingApplications } from '@/common';
import StatCardLayout from '../StatCardLayout';
import ArrowRight from '@/app/assets/svg/arrow-right.svg';
import StatInfoCardBig from '../StatInfoCardBig';
import Applications from '@/app/assets/svg/Applications.svg';
import StatInfoCardSmall from '../StatInfoCardSmall';
import EnrollWebinarCard from '../EnrollWebinarCard';
import ApplicationStatus from '../ApplicationStatus';
import GlobalStudent from '@/app/assets/svg/global-student.svg';
import DashboardLayout from '../layout/DashboardLayout';
import { MultipleLineChart } from '../MultipleLineChart';
import FavoriteUniversity from '@/app/assets/svg/favorite-university.svg';
import GlobalUniversities from '@/app/assets/svg/global-universities.svg';
import ProfileCompleteProgress from '../ProfileCompleteProgress';
import StudentIndividualBanner from '../StudentIndividualBanner';
import SuggestedUniversities from '@/app/assets/svg/suggested-universities.svg';
import StudentIndividualBannerIcon from '@/app/assets/svg/StudentIndividualBannerIcon';
import { usePermissions } from '@/lib/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';

const RegularStudent = () => {
    const userActions = useSelector((state: RootState) => state.auth.user || []);
    const permission = usePermissions();
    const hasApplications = onGoingApplications.length > 0 ;
    const totalApplications = 0;
    return (
        <>
            <DashboardLayout>
                <div className='flex flex-col'>
                    <div className='pt-5 pb-10'>
                        {permission.hasPermission('Create') && (
                            <StudentIndividualBanner
                                date='September 4, 2023'
                                description={`'Fast-Track Your Student Visa: Join Our Free Webinar!'`}
                                heading='Lee'
                                BannerImage={<StudentIndividualBannerIcon />}
                            />
                        )}
                    </div>
                    <div className='grid md:grid-cols-4 grid-cols-1 gap-6'>
                        <div className='flex flex-col gap-6 md:col-span-3 col-span-1'>
                            <div className={`grid md:grid-flow-col md:auto-cols-fr gap-6`}>
                                {totalApplications === 0 && (
                                    <StatCardLayout>
                                        <Link href={'/profile?tab=application'} className='flex py-[25px] gap-1.5 justify-center items-center'>
                                            <Image src={AddIcon} alt='add icon' height={24} width={24} />
                                            <p className='text-tertiary text-base leading-5 font-semibold p-2.5'>
                                                Create Your Application
                                            </p>
                                        </Link>
                                    </StatCardLayout>
                                )}
                                {totalApplications > 0 && (
                                    <StatCardLayout>
                                        <StatInfoCardSmall
                                            imageSrc={Applications}
                                            description='Applications'
                                            number={totalApplications}
                                        />
                                    </StatCardLayout>
                                )}
                                <StatCardLayout>
                                    <StatInfoCardSmall
                                        imageSrc={FavoriteUniversity}
                                        description='Favorite Universities'
                                        number={4}
                                        link='favourite-universities'
                                    />
                                </StatCardLayout>
                                <StatCardLayout>
                                    <StatInfoCardSmall
                                        imageSrc={SuggestedUniversities}
                                        description='Suggested Courses'
                                        number={20}
                                        link='suggested-courses'
                                    />
                                </StatCardLayout>
                            </div>
                            <div className='h-full'>
                                {hasApplications ? (
                                    <StatCardLayout className='h-full'>
                                        <div className='py-4 px-6 space-y-[22px]'>
                                            <div className='flex justify-between items-center'>
                                                <h2 className='md:text-2xl text-base md:leading-[29px] leading-5 font-bold text-primaryColor'>Your Ongoing Applications</h2>
                                                <Link href={'/all-applications'} className='font-semibold md:text-sm text-[10px] md:leading-[17px] leading-[14px] text-primaryColor inline-flex items-center gap-2'>
                                                    <span>View all</span>
                                                    <Image
                                                        src={ArrowRight}
                                                        alt='arrow right'
                                                    />
                                                </Link>
                                            </div>
                                            <div>
                                                <ul className='space-y-1.5'>
                                                    {onGoingApplications.map((app, index) => (
                                                    <li
                                                        key={index}
                                                        className=' py-2.5 flex space-x-4'
                                                    >
                                                        <Image
                                                            src={app.logo}
                                                            alt={`${app.university} logo`}
                                                            className='w-12 h-12 rounded-full'
                                                        />
                                                        <div className='grid md:grid-cols-4 grid-cols-1 gap-3 items-center w-full'>
                                                            <div className='md:col-span-2 space-y-1.5'>
                                                                
                                                                <h3 className='text-base leading-[18px] font-semibold text-graySix'>
                                                                    {app.title}
                                                                </h3>
                                                                <p className='text-sm leading-[18px] font-normal text-graySix'>{app.university}</p>
                                                                <p className='text-xs leading-[15px] font-normal text-grayFour'>{app.location}</p>
                                                            </div>
                                                            <div className='flex md:justify-center justify-start'>
                                                                <p className='text-[13px] leading-4 font-medium text-grayFour text-start'>Created on: <br />{app.createdOn}</p>
                                                            </div>
                                                            <span className={`text-[13px] md:pl-10 flex justify-start leading-4 font-semibold text-primaryColor md:text-end text-start `}>
                                                                <ApplicationStatus status={app.status} />
                                                            </span>
                                                        </div>
                                                    </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        </div>
                                    </StatCardLayout>
                                ) : (
                                <StatCardLayout className='h-full'>
                                    <MultipleLineChart />
                                </StatCardLayout>
                                )}
                            </div>
                        </div>
                        <div className='grid grid-cols-1 gap-6'>
                            <StatCardLayout>
                                <EnrollWebinarCard
                                    totalEnrollee = {webinar[0].totalEnrollee}
                                    title={webinar[0].title}
                                    imageSrc={webinar[0].bannerImage}
                                    date={webinar[0].date}
                                    startTime={webinar[0].startTime}
                                    endTime={webinar[0].endTime}
                                    enrolled={webinar[0].enrollee}
                                />
                            </StatCardLayout> 
                            {/* <StatCardLayout>
                                <UpcomingEvent />
                            </StatCardLayout> */}
                            <StatCardLayout>
                                <ProfileCompleteProgress progressValue={90} />
                            </StatCardLayout>  
                        </div>
                    </div>
                    <div className='flex md:flex-row flex-col gap-6 pt-6 pb-20'>
                        <div className='md:w-2/5 w-full'>
                            <StatCardLayout>
                                <UpcomingEvent />
                            </StatCardLayout>
                        </div>
                        <div className='md:w-3/5 w-full grid md:grid-cols-2 grid-cols-1 gap-6'>
                            <StatCardLayout className='h-full'>
                                <StatInfoCardBig
                                    imageSrc={GlobalStudent}
                                    description='Global Students'
                                    number='3345'
                                />
                            </StatCardLayout>
                            <StatCardLayout className='h-full'>
                                <StatInfoCardBig
                                    imageSrc={GlobalUniversities}
                                    description='Global Universities'
                                    number='27'
                                />
                            </StatCardLayout>
                        </div>
                    </div>
                </div>
            </DashboardLayout>
        </>
    )

}

export default RegularStudent