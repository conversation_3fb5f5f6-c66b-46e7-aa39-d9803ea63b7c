import Image from 'next/image';
import Files from '../assets/svg/Files';
import React, { useEffect, useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { FileReviewProps, Attachment } from '@/types';
import DatePickerWithRange from './DatepickerWithRange';
import { Check, X, FileText, Download } from 'lucide-react';

const demoPDf = 'https://pdfobject.com/pdf/sample.pdf'

const mockApiData: FileReviewProps[] = [
    {
        title: 'Copy of Passport',
        required: true,
        attachments: [
            { 
                filename: 'Passport.jpg', 
                url: 'https://cdn.pixabay.com/photo/2025/04/22/10/10/coast-9549731_1280.png', 
                type: 'image/jpeg'
            }
        ],
        status: 'uploaded',
        date: 'Sept 07, 2023',
    },
    {
        title: 'English Language Proficiency Test',
        required: true,
        attachments: [
            { 
                filename: 'IELTS_score.pdf', 
                url: demoPDf, 
                type: 'application/pdf'
            },
            { 
                filename: 'Duolingo_score.pdf', 
                url: demoPDf,
                type: 'application/pdf'
            }
        ],
        status: 'approved',
        date: 'Sept 07, 2023',
    },
    {
        title: 'Scholarship Personal Statement',
        required: true,
        attachments: [
            { 
                filename: 'Scholarship_statement.pdf', 
                url: demoPDf,
                // url: '/api/files/scholarship-statement.pdf',
                type: 'application/pdf'
            }
        ],
        status: 'rejected',
        date: 'Sept 07, 2023',
    },
    {
        title: 'test for error',
        required: true,
        attachments: [
            { 
                filename: 'test.pdf', 
                url: '/dsds/fdgdg/gfdf',
                // url: '/api/files/scholarship-statement.pdf',
                type: 'file'
            }
        ],
        status: 'rejected',
        date: 'Sept 07, 2023',
    },
];



const FileReviewComponent = () => {
    const [documents, setDocuments] = useState<FileReviewProps[]>([]);
    const [selectedFileMap, setSelectedFileMap] = useState<Record<number, Attachment | null>>({});
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchDocuments = async () => {
            try {
                await new Promise(resolve => setTimeout(resolve, 800));
                setDocuments(mockApiData);
            } catch (error) {
                console.error('Error fetching documents:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchDocuments();
    }, []);

    const handleFileClick = (file: Attachment, docIndex: number): void => {
        setSelectedFileMap(prev => {
            if (prev[docIndex]?.filename === file.filename) {
                const newMap = {...prev};
                delete newMap[docIndex];
                return newMap;
            }
            return { ...prev, [docIndex]: file };
        });
    };

    const getFileIcon = (fileType: string): JSX.Element => {
        if (fileType.startsWith('image/')) {
            return <Files className="w-4 h-4 text-primaryColor" />;
        } else if (fileType === 'application/pdf') {
            return <Files className="w-4 h-4 text-[#FF3B30]" />;
        } else {
            return <Files className="w-5 h-5 text-grayThree" />;
        }
    };

    const renderFilePreview = (file: Attachment): JSX.Element => {
        return (
            <div className="bg-white rounded-md">
                <div className="h-64 flex items-center justify-center bg-grayOne rounded-md">
                    {file.type.startsWith('image/') && file.url && file.url.trim() !== '' ? (
                        <div className="relative w-full h-full">
                            <Image
                                src={file.url}
                                alt={`Preview of ${file.filename}`}
                                fill
                                className="object-contain"
                            />
                        </div>
                    ) : file.type === 'application/pdf' ? (
                        // <div className="w-full h-full">
                        //     <object
                        //         data={file.url}
                        //         type="application/pdf"
                        //         width="100%"
                        //         height="100%"
                        //         className="w-full h-full"
                        //     >
                        //         <div className="text-center py-8">
                        //             <FileText className="w-12 h-12 text-[#FF3B30] mx-auto mb-2" />
                        //             <p>Your browser doesn't support embedded PDFs.</p>
                        //             <a 
                        //                 href={file.url} 
                        //                 download={file.filename}
                        //                 className="text-primaryColor mt-2 inline-block"
                        //             >
                        //                 Download the PDF instead
                        //             </a>
                        //         </div>
                        //     </object>
                        // </div>

                        <div className="w-full">
                        <embed
                            src={file.url}
                            type="application/pdf"
                            style={{
                                width: '100%',
                                height: '500px',
                            }}
                            onError={(e) => {
                                // This is a fallback but won't actually be triggered in most cases
                                // Browser will show its own error UI for failed embeds
                                const target = e.target as HTMLElement;
                                if (target) {
                                    target.outerHTML = `
                                        <div class="text-center py-8 h-64 bg-grayOne rounded-md">
                                            <div class="w-12 h-12 text-[#FF3B30] mx-auto mb-2">
                                                <FileText />
                                            </div>
                                            <p>Unable to display PDF.</p>
                                            <a 
                                                href=${file.url}
                                                download=${file.filename}
                                                class="text-primaryColor mt-2 inline-block"
                                            >
                                                Download the PDF instead
                                            </a>
                                        </div>
                                    `;
                                }
                            }}
                        />
                    </div>
                    ) : (
                        <div className="text-center">
                            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-2" />
                            <p className="text-gray-500">Preview not available for {file.filename}</p>
                            <p className="text-sm text-gray-400 mt-2">Click download to view the file</p>
                        </div>
                    )}
                </div>
            </div>
        );
    };

    if (loading) {
        return (
            <div className="py-5 px-4 flex justify-center items-center h-64">
                <div className="text-gray-500">Loading documents...</div>
            </div>
        );
    }

    return (
        <div className="py-5 px-4">
            <div className="overflow-hidden">
                <div className="grid grid-cols-3 text-sm font-medium border border-tertiary/30 rounded-lg text-graySix p-4">
                    <div>Details</div>
                    <div>Attachments</div>
                    <div className="text-right">Actions</div>
                </div>
                
                {documents.map((doc, idx) => (
                    <div key={idx} className="my-5">
                        <div className="grid grid-cols-3 items-center border border-tertiary/30 rounded-lg text-sm px-4 py-3">
                            <div>
                                <div className="font-semibold text-graySix flex items-center gap-2">
                                    {doc.title}
                                    {doc.required && (
                                        <span className="text-xs text-primaryColor bg-primaryColor/10 px-1 py-0.5 rounded-full">
                                            Required
                                        </span>
                                    )}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                    Requested on {doc.date}
                                </div>
                            </div>
                            <div className="flex flex-wrap gap-2">
                                {doc.attachments.map((attachment, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleFileClick(attachment, idx)}
                                        className={`text-secondaryColor rounded-full py-1 px-2 text-sm flex items-center gap-1 transition-colors ${
                                            selectedFileMap[idx]?.filename === attachment.filename 
                                                ? "bg-blue-200" 
                                                : "bg-primaryOne hover:bg-blue-100"
                                        }`}
                                    >
                                        {getFileIcon(attachment.type)}
                                        <span>{attachment.filename}</span>
                                    </button>
                                ))}
                            </div>
                            <div className="text-right">
                                {doc.status === 'uploaded' && (
                                    <div className="gap-4 flex items-center justify-end">
                                        <Check className="text-white bg-primaryColor p-1 rounded-full" /> 
                                        <X className="text-white bg-grayThree p-1 rounded-full" />
                                    </div>
                                )}
                                {doc.status === 'approved' && (
                                    <span className="text-blue-600 font-medium flex items-center justify-end gap-1">
                                        <Check className="text-primaryColor p-1 rounded-full w-6 h-6" />
                                        Approved
                                    </span>
                                )}
                                {doc.status === 'rejected' && (
                                    <span className="text-grayThree flex items-center justify-end gap-1">
                                        <X className="text-grayThree p-1 rounded-full w-6 h-6" /> 
                                        Rejected
                                    </span>
                                )}
                            </div>
                        </div>

                        {selectedFileMap[idx] && (
                            <div className="mt-2 border border-tertiary/30 rounded-lg overflow-hidden">
                                <div className="bg-gray-50 px-4 py-3 flex justify-between items-center">
                                    <div className="font-medium flex items-center gap-2">
                                        {getFileIcon(selectedFileMap[idx]!.type)}
                                        <span>
                                            Viewing: {selectedFileMap[idx]!.filename} from {doc.title}
                                        </span>
                                    </div>
                                    <div className="flex gap-2">
                                        <a 
                                            href={selectedFileMap[idx]!.url} 
                                            download={selectedFileMap[idx]!.filename}
                                            className="text-primaryColor flex items-center gap-1 text-sm hover:text-tertiary"
                                        >
                                            <Download className="w-4 h-4" /> Download
                                        </a>
                                        <button 
                                            onClick={() => {
                                                setSelectedFileMap(prev => {
                                                    const newMap = {...prev};
                                                    delete newMap[idx];
                                                    return newMap;
                                                });
                                            }}
                                            className="text-grayFive flex items-center gap-1 text-sm hover:text-graySeven"
                                        >
                                            <X className="w-4 h-4" /> Close
                                        </button>
                                    </div>
                                </div>
                                <div className="p-4 bg-white">
                                    {renderFilePreview(selectedFileMap[idx]!)}
                                </div>
                            </div>
                        )}

                        {doc.status === 'rejected' && (
                            <div className="flex border-t mt-2 p-4 gap-2 border border-tertiary/30 rounded-b-lg">
                                <div className="text-grayFive text-sm font-medium">
                                    Explanation:
                                </div>
                                <div className="w-full border rounded-md border-tertiary/30 p-3.5">
                                    <div className='w-fit'>
                                        <DatePickerWithRange 
                                            title='Resubmission Date' 
                                        />
                                    </div>
                                    <div className="border-t mt-4 pt-4">
                                        <Textarea 
                                            className="border-none" 
                                            placeholder="Add your explanation here..." 
                                        /> 
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default FileReviewComponent;