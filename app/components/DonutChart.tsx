'use client';

import { useState } from 'react';
import { <PERSON>, PieC<PERSON>, Sector } from 'recharts';
import { PieSectorDataItem } from 'recharts/types/polar/Pie';
import {
    Card,
    CardContent,
    CardTitle,
} from '@/components/ui/card';
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';

// Define the shape of the data
interface DonutChartItem {
    category: string;
    value: number;
    fill: string;
}

// Define the props for the DonutChart component
interface DonutChartProps {
    data: DonutChartItem[];
    chartConfig: ChartConfig;
    title: string;
    legendPosition?: 'right' | 'bottom';
    chartClassName?: string;
    innerCircleRedius?: number;
}

export function DonutChart({ 
    data, 
    title, 
    chartConfig,
    legendPosition = 'right',
    chartClassName = 'h-[320px]',
    innerCircleRedius = 75
}: DonutChartProps) {
    const [activeIndex, setActiveIndex] = useState<number | null>(null);
    // Ensure chartData is always an array
    const chartData = Array.isArray(data) ? data : [];

    const onPieEnter = (_: any, index: number) => {
        setActiveIndex(index);
    };

    const onPieLeave = () => {
        setActiveIndex(null);
    };

    return (
        <Card className="drop-shadow-none shadow-none border-none">
            <CardTitle className='flex justify-between p-6'>
                <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                    <h2>{title}</h2>
                </div>
            </CardTitle>
            <CardContent className={`flex-1 pb-0 h-full flex ${legendPosition == 'right' ? 'flex-row': 'flex-col'}`}>
                <ChartContainer
                    config={chartConfig}
                    className={`${chartClassName} ${legendPosition == 'right' ? 'w-1/2': 'w-full'}`}
                >
                    <PieChart>
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent hideLabel />}
                        />
                        <Pie
                            data={chartData}
                            dataKey="value"
                            nameKey="category"
                            innerRadius={innerCircleRedius}
                            startAngle={90}
                            endAngle={450}
                            activeIndex={activeIndex ?? undefined}
                            activeShape={({
                                outerRadius = 0,
                                ...props
                            }: PieSectorDataItem) => (
                                <Sector 
                                    {...props} 
                                    outerRadius={outerRadius + 10} 
                                    innerRadius={innerCircleRedius} 
                                    className='drop-shadow-xl transition-all duration-300 ease-in-out'
                                    forceCornerRadius cornerRadius={5}
                                />
                            )}
                            inactiveShape={
                                <Sector cornerRadius={5} className='transition-all duration-300 ease-in-out' />
                            }
                            onMouseEnter={onPieEnter}
                            onMouseLeave={onPieLeave}
                        />
                    </PieChart>
                </ChartContainer>
                <div className={`flex justify-center items-center ${legendPosition == 'right' ? 'w-1/2 h-[340px]': 'w-full h-12'}`}>
                    <div className={`flex items-start text-grayFive ${legendPosition == 'right' ? 'flex-col gap-3': 'flex-row gap-5'}`}>
                        {Array.isArray(chartData) && chartData.map((item, index) => (
                            <div key={index} className='flex items-center justify-center gap-9'>
                                <span 
                                    className='w-3 h-3 rounded-full' 
                                    style={{ backgroundColor: item.fill }}
                                ></span>
                                <span>{item.category}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
