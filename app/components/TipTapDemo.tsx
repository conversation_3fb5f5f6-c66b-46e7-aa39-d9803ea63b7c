'use client';

import Toolbar from './Toolbar';
import { useState } from 'react';
import Text from '@tiptap/extension-text';
import Link from '@tiptap/extension-link';
import StarterKit from '@tiptap/starter-kit';
import Heading from '@tiptap/extension-heading';
import { Button } from '@/components/ui/button';
import Document from '@tiptap/extension-document';
import ListItem from '@tiptap/extension-list-item';
import Paragraph from '@tiptap/extension-paragraph';
import Subscript from '@tiptap/extension-subscript';
import Blockquote from '@tiptap/extension-blockquote';
import BulletList from '@tiptap/extension-bullet-list';
import Superscript from '@tiptap/extension-superscript';
import Placeholder from '@tiptap/extension-placeholder';
import OrderedList from '@tiptap/extension-ordered-list';
import { useEditor, EditorContent } from '@tiptap/react';
import HardBreak from '@tiptap/extension-hard-break';

interface TipTapDemoProps {
    onSend: (content: string) => void; // Callback to send content
}

const TipTapDemo: React.FC<TipTapDemoProps> = ({ onSend }) => {
    // Local state to track the editor's content
    const [content, setContent] = useState('');

    // Initialize the editor
    const editor = useEditor({
        immediatelyRender: false,
        // shouldRerenderOnTransaction: false,
        extensions: [
            StarterKit.configure({
                // heading: false, // Disable built-in heading
                listItem: false, // Disable built-in listItem
                bulletList: false, // Disable built-in bulletList
                orderedList: false, // Disable built-in orderedList
                paragraph: false, // Disable built-in paragraph
                text: false, // Disable built-in text
                blockquote: false, // Disable built-in blockquote
                hardBreak: false, // Disable built-in hardBreak
            }),
            Paragraph.configure({
                HTMLAttributes: {
                    class: 'text-grayFive text-sm leading-[19px] font-normal my-4'
            }}),
            Heading.configure({
                levels: [1, 2, 3],
            }).extend({
                addAttributes() {
                    return {
                        class: {
                            default: null,
                            renderHTML: (attributes) => {
                                const level = attributes.level;
                                let customClass = '';

                                if (level === 1) {
                                    customClass =
                                        'text-[28px] text-grayFive leading-[34px] font-bold';
                                } else if (level === 2) {
                                    customClass =
                                        'text-[24px] text-grayFive leading-[29px] font-semibold';
                                } else if (level === 3) {
                                    customClass =
                                        'text-[22px] text-grayFive leading-[27px] font-semibold';
                                }

                                return {
                                    class: customClass,
                                };
                            },
                        },
                    };
                },
            }),
            ListItem,
            BulletList.configure({
                HTMLAttributes: {
                    class: 'text-grayFive list-disc py-4 px-4 my-4 mr-4 ml-4',
                },
                keepMarks: true,
                keepAttributes: true,
                itemTypeName: 'listItem',
            }),
            OrderedList.configure({
                HTMLAttributes: {
                    class: 'text-grayFive list-decimal px-4 my-4 mr-4 ml-4',
                },
                itemTypeName: 'listItem',
            }),
            // Document.configure({
            //     HTMLAttributes: {
            //         class: 'text-grayFive bg-green-500',
            //     },
            // }),
            Text,
            Blockquote.configure({
                HTMLAttributes: {
                    class: 'text-grayFive border-l-[3px] border-primaryColor bg-primaryOne',
                },
            }),
            Superscript,
            Subscript,
            Placeholder.configure({
                // Use a placeholder:
                placeholder: 'Please Write Your Reply …',
                // Use different placeholders depending on the node type:
                // placeholder: ({ node }) => {
                //   if (node.type.name === 'heading') {
                //     return 'What’s the title?'
                //   }

                //   return 'Can you add some further context?'
                // },
                showOnlyWhenEditable: false,
            }),
            Link.configure({
                HTMLAttributes: {
                    class: 'text-primaryColor cursor-pointer underline',
                },
                autolink: true,
                defaultProtocol: 'https',
                protocols: ['http', 'https','mailto','ftp'],
                isAllowedUri: (url, ctx) => {
                    try {
                        // construct URL
                        const parsedUrl = url.includes(':')
                            ? new URL(url)
                            : new URL(`${ctx.defaultProtocol}://${url}`);

                        // use default validation
                        if (!ctx.defaultValidate(parsedUrl.href)) {
                            return false;
                        }

                        // disallowed protocols
                        const disallowedProtocols = ['ftp', 'file', 'mailto'];
                        const protocol = parsedUrl.protocol.replace(':', '');

                        if (disallowedProtocols.includes(protocol)) {
                            return false;
                        }

                        // only allow protocols specified in ctx.protocols
                        const allowedProtocols = ctx.protocols.map((p) =>
                            typeof p === 'string' ? p : p.scheme
                        );

                        if (!allowedProtocols.includes(protocol)) {
                            return false;
                        }

                        // disallowed domains
                        const disallowedDomains = [
                            'example-phishing.com',
                            'malicious-site.net',
                        ];
                        const domain = parsedUrl.hostname;

                        if (disallowedDomains.includes(domain)) {
                            return false;
                        }

                        // all checks have passed
                        return true;
                    } catch {
                        return false;
                    }
                },
                shouldAutoLink: (url) => {
                    try {
                        // construct URL
                        const parsedUrl = url.includes(':')
                            ? new URL(url)
                            : new URL(`https://${url}`);

                        // only auto-link if the domain is not in the disallowed list
                        const disallowedDomains = [
                            'example-no-autolink.com',
                            'another-no-autolink.com',
                        ];
                        const domain = parsedUrl.hostname;

                        return !disallowedDomains.includes(domain);
                    } catch {
                        return false;
                    }
                },
            }),
            HardBreak.extend({
                addKeyboardShortcuts () {
                  return {
                    Enter: () => this.editor.commands.setHardBreak()
                  }
                }
              })
        ],
        content,
        editorProps: {
            attributes: {
                class: 'bg-white flex flex-col justify-start items-start w-full gap-3 rounded-bl-md rounded-br-md outline-none text-grayFive text-sm',
            },
        },
        onUpdate: ({ editor }) => {
            // Update the local state whenever the editor content changes
            setContent(editor.getHTML());
        },
    });
    

    if (!editor) {
        return null;
    }

    return (
        <div className="pb-5">
            <div className="w-full drop-shadow-[0px_2px_2px_#00000026] ">
                {/* Pass editor instance and content to the toolbar */}
                <Toolbar editor={editor} content={content} />
                <div className="bg-white border-b border-l border-r rounded-bl-md rounded-br-md border-grayOne px-4 py-5">
                    <EditorContent editor={editor} />
                    <Button
                        onClick={() => {
                            onSend(content)
                            // console.log(content);
                        }}
                        className="bg-primaryColor text-white px-4 py-2 rounded-[50px] mt-7 focus:outline-none"
                    >
                        Send
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default TipTapDemo;
