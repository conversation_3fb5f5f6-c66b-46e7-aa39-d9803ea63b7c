import React from 'react';
import { cn } from '@/lib/utils';
import { InputWithIconProps } from '@/types';


const InputWithIcon: React.FC<InputWithIconProps> = ({ 
    icon, 
    className, 
    type = 'text',
    placeholder = 'Enter text',
    value,
    onChange,
}) => {
    return (
        <div className='relative flex items-center'>
            <div className='absolute left-4 flex items-center'>
                {React.createElement(icon)}
            </div>

            <input
                type={type}
                placeholder={placeholder}
                value={value}
                onChange={onChange}
                className={cn(
                'w-full pl-10 pr-3 py-2 rounded-md border border-grayTwo focus:ring-2 focus:ring-blue-500 focus:outline-none',
                'text-xs text-grayThree placeholder-grayThree',className
                )}
            />
        </div>
    );
};

export default InputWithIcon;
