'use client';

import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect, useRef } from 'react';
import {
  useSendOTPMutation,
  useVerifyOTPMutation,
} from '@/lib/redux/api/authApi';

const OtpForm = () => {
  const router = useRouter();
  const { toast } = useToast();
  const [otp, setOtp] = useState<string[]>(new Array(6).fill(''));
  const [timer, setTimer] = useState(90);
  const [hasError, setHasError] = useState(false);
  const inputRefs = useRef<HTMLInputElement[]>([]);

  const email = typeof window !== 'undefined'
    ? sessionStorage.getItem('verificationEmail')
    : null;

  const isOtpComplete = otp.every((digit) => digit.trim() !== '');

  const [verifyOTP, { isLoading: isVerifying }] = useVerifyOTPMutation();
  const [sendOTP, { isLoading: isResending }] = useSendOTPMutation();

  useEffect(() => {
    const countdown = setInterval(() => {
      setTimer((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    return () => clearInterval(countdown);
  }, []);

  const handleChange = (element: HTMLInputElement, index: number) => {
    if (isNaN(Number(element.value))) return;
    const newOtp = [...otp];
    newOtp[index] = element.value;
    setOtp(newOtp);
    setHasError(false);

    if (element.nextSibling && element.value) {
      (element.nextSibling as HTMLInputElement).focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async () => {
    if (!isOtpComplete || !email) return;
    const enteredOtp = otp.join('');

    try {
      const response = await verifyOTP({
        email,
        otp: enteredOtp,
        type: 'register',
      }).unwrap();

      if (response.success) {
        toast({
          title: 'Success',
          description: 'OTP verified successfully!',
        });
        sessionStorage.removeItem('verificationEmail');
        setTimeout(() => router.push('/login'), 1500);
      } else {
        handleOtpFailure(response.message || 'Invalid or expired OTP.');
      }
    } catch (err: any) {
      const msg =
        err?.data?.message || err?.message || 'OTP verification failed.';
      handleOtpFailure(msg);
    }
  };

  const handleOtpFailure = (message: string) => {
    setHasError(true);
    setOtp(new Array(6).fill(''));
    inputRefs.current[0]?.focus();
    toast({
      variant: 'destructive',
      title: 'Verification Failed',
      description: message,
    });
  };

  const handleResendOtp = async () => {
    if (timer > 0 || !email) return;

    try {
      const response = await sendOTP({ email, type: 'register' }).unwrap();
      if (response.success) {
        setTimer(90);
        setOtp(new Array(6).fill(''));
        setHasError(false);

        toast({
          title: 'OTP Resent',
          description: 'A new OTP has been sent to your email.',
        });
      } else {
        throw new Error(response.message || 'Resend failed');
      }
    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: 'Failed to Resend OTP',
        description: err?.message || 'Please try again.',
      });
    }
  };

  const handleCancel = () => {
    router.push('/login');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white px-4">
      <div className="max-w-md w-full text-center">
        <div className="flex justify-center gap-2 mb-6">
          {otp.map((value, index) => (
            <input
              key={index}
              type="text"
              maxLength={1}
              value={value}
              className={`w-14 h-14 text-center border rounded-[8px] text-2xl font-semibold text-graySeven ${
                hasError
                  ? 'border-red-500 focus:ring-red-500'
                  : 'border-[#1952BB33] focus:ring-blue-400'
              } focus:outline-none focus:ring-2`}
              onChange={(e) => handleChange(e.target, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              ref={(el) => {
                if (el) inputRefs.current[index] = el;
              }}
            />
          ))}
        </div>

        <button
          onClick={handleVerify}
          disabled={!isOtpComplete || isVerifying}
          className={`w-full py-2 px-4 text-white rounded-[8px] font-semibold text-base mb-4 transition ${
            isOtpComplete && !isVerifying
              ? 'bg-primaryColor hover:bg-tertiary'
              : 'bg-[#90ABDE] cursor-not-allowed'
          }`}
        >
          {isVerifying ? 'Verifying...' : 'Verify'}
        </button>

        <button
          onClick={handleCancel}
          className="w-full py-2 border border-primaryColor rounded-[8px] text-primaryColor font-semibold text-base mb-6"
        >
          Cancel
        </button>

        <p className="text-sm font-normal text-grayFive mb-8">
          Remaining Time:{' '}
          <span className="text-primaryColor font-semibold">{formatTime(timer)}</span>
        </p>
        <p className="text-sm font-normal text-grayFive">
          Didn’t receive the code?
          <span
            onClick={timer === 0 ? handleResendOtp : undefined}
            className={`ml-1 font-semibold ${
              timer === 0
                ? 'text-primaryColor cursor-pointer hover:underline'
                : 'text-gray-400 cursor-not-allowed'
            }`}
          >
            {isResending ? 'Resending...' : 'Resend'}
          </span>
        </p>
      </div>
    </div>
  );
};

export default OtpForm;
