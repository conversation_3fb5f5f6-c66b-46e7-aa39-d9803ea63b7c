// import TipTapDemo from './TipTapDemo';
// import React, { useState } from 'react';
import ApplicantsMessage from './ApplicantsMessage';
import ApplyGoalLogoSmall from '@/app/assets/svg/ApplyGoalLogoSmall';

const ApplicationStudentProfileNotes = () => {
    // const [openTiptap, setOpenTiptap] = useState(false);
    // const [replyContent, setReplyContent] = useState('');

    const handleResponseSubmit = (response: string) => {
        // console.log(`User's response:`, response);
        alert('Response submitted successfully!');
    };

    // const handleReplyClick = () => {
    //     setOpenTiptap(true);
    // };

    return (
        <div>
            <ApplicantsMessage
                senderImage={<ApplyGoalLogoSmall />}
                senderName='Applygoal'
                title='Application Cancelled'
                message={`Please be advised that due to the long period of inactivity of this application, we have cancelled the application and credited the fee, if any, back to your account. If you still wish to proceed with this application, or if you wish to update to a later semester in order to have time to provide the necessary documents, please let us know by creating a note on this application.`}
                messagetime='Sep 8, 2023, 12:47 PM'
                onSubmit={handleResponseSubmit} 
                // replyContent={replyContent}
                // onReplyClick={handleReplyClick} 
            />
            {/* {openTiptap && <TipTapDemo onSend={setReplyContent} />} */}
        </div>
    )
}

export default ApplicationStudentProfileNotes
