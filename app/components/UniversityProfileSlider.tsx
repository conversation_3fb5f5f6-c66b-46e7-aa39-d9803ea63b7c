'use client';

import 'swiper/css';
import Image from 'next/image';
import 'swiper/css/pagination';
import { slideContent } from '@/common';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';


const UniversityProfileSlider: React.FC = () => {
    return (
        <Swiper
            breakpoints={{
                320: {
                    slidesPerView: 1.5,
                    spaceBetween: 20
                },
                640: {
                    slidesPerView: 2.5,
                    spaceBetween: 20
                },
                768: {
                    slidesPerView: 3.5,
                    spaceBetween: 30
                }
            }}
            spaceBetween={30}
            loop={true}
            autoplay={{
                delay: 2500,
                disableOnInteraction: false,
            }}
            pagination={{ 
                clickable: true,
                bulletClass: 'slider-pagination__item',
                bulletActiveClass: 'active',
            }}
            modules={[ Autoplay, Pagination ]}
            className="unislider"
        >
            {slideContent.map((slide, index) => (
                <SwiperSlide key={index}>
                    <Image
                        src={slide.img}
                        alt='Campus image'
                        className='rounded-xl object-cover swiper-slide-image w-full h-full'
                    />
                </SwiperSlide>
            ))}
        </Swiper>
    );
};

export default UniversityProfileSlider;
