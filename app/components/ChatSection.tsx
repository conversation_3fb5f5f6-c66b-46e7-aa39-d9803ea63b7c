import Image from 'next/image';
import { messages } from '@/common';
import { ChatSectionProps } from '@/types';
import Send from '@/app/assets/svg/send';
import Back from '@/app/assets/svg/back';
import User from '@/app/assets/img/user.png';
import React, { useState, useRef  } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import AttachFile from '@/app/assets/svg/attach_file_add';

const ChatSection: React.FC<ChatSectionProps> = ({ 
    setChat, 
    selectedDept  
}) => {
    const [profileImage, setProfileImage] = useState<string>();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const chatBack = () => {
        setChat(false);
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            if (file) {
                const imageUrl = URL.createObjectURL(file);
                setProfileImage(imageUrl);
            }
        };
        
        const triggerFileInput = () => {
            fileInputRef.current?.click();
    };

    return (
        <div className='bg-white flex flex-col relative rounded-[20px] h-screen'>
            <div className='flex justify-between items-center px-4 md:px-10 py-[17px]'>
                <div className='flex gap-4'>
                    <Image
                        src={User}
                        alt='Carter Kenter'
                        width={40}
                        height={40}
                        className='rounded-full'
                    />
                    <div>
                        <h3 className='text-lg leading-6 text-graySeven font-semibold'>{ selectedDept || 'Chat' }</h3>
                        <span className='flex items-center gap-2 text-xs text-grayThree font-semibold leading-4'>
                            <span className='block w-[10px] h-[10px] rounded-full bg-[#12B76A]'></span>
                            Online
                        </span>
                    </div>
                </div>
                <div className='md:hidden block'>
                    <button onClick={chatBack}>
                        <Back />
                    </button>
                </div>
            </div>
            <Separator />

            <ScrollArea className='flex-1 px-4 md:px-10 overflow-y-auto space-y-4'>
                <div className='pb-4'>
                    {messages.map((msg, index) => (
                        msg.dept && selectedDept === msg.dept && (
                            <div
                                key={index}
                                className={`flex ${
                                msg.sender === 'me' ? 'justify-end' : 'justify-start'
                                }`}
                        >
                            <div
                                className={`mb-1 font-normal text-[13px] leading-5 py-2 px-4 ${
                                msg.sender === 'me'
                                    ? 'bg-primaryColor text-white'
                                    : 'bg-grayOne text-graySix'
                                } max-w-[60%] rounded-[30px]`}
                            >
                                <p className='text-sm'>{msg.text}</p>
                            </div>
                        </div>
                        )
                        
                    ))}
                </div>
            </ScrollArea>

            <div className='sticky bottom-0 w-full border-t p-4 bg-white'>
                <div className='flex items-center space-x-4'>
                    <div className='relative'>
                        <div
                            onClick={triggerFileInput}
                            className="cursor-pointer"
                        >
                            <AttachFile />
                        </div>
                        <input
                            type="file"
                            accept="image/*"
                            ref={fileInputRef}
                            onChange={handleImageChange}
                            className="hidden"
                        />
                    </div>
                    <div className='flex w-full items-center relative'>
                        <Textarea 
                            placeholder='Type a message'
                            className='placeholder:font-normal placeholder:text-[13px] placeholder:leading-5 placeholder:text-grayFive border-2 rounded-[30px] border-[#E2E8F0] min-h-0 py-2.5 px-4'
                            rows={1}
                            cols={1}
                        />
                        <div className='absolute right-4 cursor-pointer'>
                            <Send />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ChatSection