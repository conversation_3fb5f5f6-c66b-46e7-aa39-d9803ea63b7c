'use client';

import { ReactNode, useState } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>A<PERSON>s,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Legend,
    CartesianGrid,
    LabelList,
} from 'recharts';

import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';

import { ChartContainer, ChartTooltipContent } from '@/components/ui/chart';
import Image from 'next/image';
import Link from 'next/link';

const CustomAxisLabel = ({ x, y, payload, customLabels }:any) => {
    const label = payload.value;
    const customLabel = customLabels?.[label];
  
    // If there's a custom label configuration for this value
    if (customLabel) {
      if (customLabel.imageUrl) {
        // Handle both imported SVG components and URL strings
        const ImageComponent = customLabel.imageUrl;
        
        return (
            <g transform={`translate(${x - 40},${y})`}>
            {/* Using the image directly as a src attribute handles both string URLs and imported SVGs */}
            <image 
              xlinkHref={customLabel.imageUrl} 
              width="30" 
              height="30" 
              x="0" 
              y="-15"
            />
            {customLabel.text && (
              <text x="-5" y="25" textAnchor="end" fill="#666">
                {customLabel.text}
              </text>
            )}
          </g>
        );
      } else if (customLabel.text) {
        return (
          <text x={x - 10} y={y} dy="0.35em" textAnchor="end" fill="#666">
            {customLabel.text}
          </text>
        );
      }
    }
  
    // Default label
    return (
      <text x={x - 10} y={y} dy="0.35em" textAnchor="end" fill="#666">
        {label}
      </text>
    );
  };

export default function HorizontalBarChart({
    title = 'Chart Title',
    viewAllLink = '',
    data = [],
    config = {},
    height = 400,
    barRadius = 0,
    barSize,
    barGap = 4,
    barCategoryGap = '16%',
    showLegend = true,
    customAxisLabels = {},
    footer,
    className = '',
    xAxisDomain = ['auto', 'auto'],
    showGrid = true,
    showTooltip = true,
    showBarLabels = true,
    barLabelPosition = 'right',
    yAxisWeidth = 30
}: {
    title?: string;
    viewAllLink?: string;
    data?: any[];
    config?: Record<string, any>;
    height?: number;
    barRadius?: number;
    barSize?: number;
    barGap?: number;
    barCategoryGap?: string;
    showLegend?: boolean;
    customAxisLabels?: Record<string, { imageUrl?: string ; text?: string }>;
    footer?: React.ReactNode;
    className?: string;
    xAxisDomain?: [number | 'auto', number | 'auto'];
    showGrid?: boolean;
    showTooltip?: boolean;
    showBarLabels?: boolean;
    barLabelPosition?: 'top' | 'right' | 'bottom' | 'left';
    yAxisWeidth?: number;
}) {
    return (
        <Card
            className={`drop-shadow-none shadow-none border-none ${className}`}
        >
            <CardHeader>
                <CardTitle className='flex justify-between items-center md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold'>
                    {title}
                    {viewAllLink && (
                        <Link
                            href={viewAllLink}
                            className='text-secondaryColor font-medium text-sm py-2 px-3.5 bg-primaryOne rounded-full w-fit'
                        >
                            View All
                        </Link>
                    )}
                </CardTitle>
                
            </CardHeader>
            <CardContent>
                <ChartContainer
                    config={config}
                    className={`mx-auto aspect-auto w-full gap-9`}
                    style={{ height: `${height}px` }}
                >
                    <BarChart
                        accessibilityLayer
                        data={data}
                        layout="vertical"
                        margin={{
                            left: customAxisLabels ? 50 : 20,
                            right: 30,
                            top: 10,
                            bottom: 10,
                        }}
                        barGap={barGap}
                        barCategoryGap={barCategoryGap}
                        barSize={barSize}
                    >
                        <XAxis
                            type="number"
                            tick
                            tickLine={false}
                            axisLine={false}
                            domain={xAxisDomain}
                        />
                        <YAxis
                            dataKey="category"
                            type="category"
                            tickLine={false}
                            tickMargin={10}
                            axisLine={false}
                            tick={
                                customAxisLabels
                                    ? (props) => 
                                        <CustomAxisLabel {...props} customLabels={customAxisLabels} /> : undefined}
                            width={yAxisWeidth}
                            
                        />

                        {showGrid && (
                            <CartesianGrid
                                horizontal={false}
                                strokeDasharray="3 3"
                            />
                        )}

                        {showTooltip && (
                            <Tooltip
                                cursor={false}
                                content={<ChartTooltipContent hideLabel />}
                            />
                        )}

                        {showLegend && <Legend />}

                        {Object.keys(config).map((key) => (
                            <Bar
                                key={key}
                                dataKey={key}
                                fill={`var(--color-${key})`}
                                radius={barRadius}
                                
                            >
                                {showBarLabels && (
                                    <LabelList position={barLabelPosition} />
                                )}
                            </Bar>
                        ))}
                    </BarChart>
                </ChartContainer>
            </CardContent>
            {footer && (
                <CardFooter className="flex-col items-start gap-2 text-sm">
                    {footer}
                </CardFooter>
            )}
        </Card>
    );
}
