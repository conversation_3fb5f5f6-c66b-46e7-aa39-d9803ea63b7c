import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ApplicationCardProps } from '@/types';
import ToolTipIcon from '../assets/svg/ToolTipIcon';
import Guptin from '@/app/assets/img/guptin-lee.png';
import CaltechNniLogo from '@/app/assets/svg/caltech-uni-logo.svg';
import { 
    Tooltip, 
    TooltipContent, 
    TooltipProvider, 
    TooltipTrigger 
} from '@/components/ui/tooltip';


const ApplicantInfoCard: React.FC<ApplicationCardProps> = ({
    name,
    email,
    level,
    program,
    intakes,
    university,
    requiredLevel,
    applicationId,
    deliveryMethod,
    submissionDeadline,
    paymentAmount,
    paymentStatus
}) => {
    return (
        <div className='bg-white rounded-xl p-4 md:w-[20%] w-full md:min-w-[302px] divide-y divide-[#E5E7EB] h-fit md:sticky md:top-0'>
      {/* Header Section */}
        <div className='flex flex-col items-center space-y-4 pb-4'>
            <Image
                src={Guptin}
                alt='Profile'
                className='rounded-full'
                width={70}
                height={70}
            />
            <div className='flex flex-col items-center'>
                <h2 className='text-xl md:leading-7 leading-6 font-medium text-graySix'>{name}</h2>
                <p className='text-sm text-grayFour'>{email}</p>
            </div>
        </div>

        {/* University Information */}
        <div className='py-6'>
            <div className='flex justify-between items-center mb-3'>
                <Link href={'/university-profile'} className='font-semibold text-base line-clamp-1 text-graySix'>{university}</Link>
                <Image 
                    alt='uni logo'
                    src={CaltechNniLogo}
                    className='rounded-full w-9 h-9'
                />
            </div>
            <div className='flex flex-col gap-1.5'>
                <p className='text-grayFive text-sm'>{program}</p>
                <p className='text-grayFive text-sm'>
                    <strong className='font-bold'>Level:</strong> {level}
                </p>
                <p className='text-grayFive text-sm'>
                    <strong className='font-bold'>Required Level:</strong> {requiredLevel}
                </p>
                <p className='text-grayFive text-sm'>
                    <strong className='font-bold'>Application ID:</strong> {applicationId}
                </p>
            </div>
            <p className='text-grayFive text-sm pt-6 space-x-5'>
                <strong>Delivery Method:</strong>
                <span className='py-1 px-3 bg-blue-100 text-primaryColor text-xs leading-[15px] font-medium rounded-full drop-shadow-5xl'>
                    {deliveryMethod}
                </span>
            </p>
        </div>

        {/* Intakes */}
        <div className='py-6 flex gap-[18px] justify-between items-start'>
            <p className='text-base text-graySix '>
                Intake(s):
            </p>
            <div className='flex flex-col'>
                <span className='text-graySix text-xs leading-6 grid grid-cols-1'>
                    ESL
                    <span className='text-sm text-grayFive font-medium'>
                        {intakes.esl || 'N/A'} 
                    </span>
                </span>
            </div>
            <div className='flex flex-col'>
                <span
                    className='text-xs leading-6 text-graySix'
                >
                    Academic <span className={` ${
                    intakes.academic.toLowerCase() === 'closed'
                        ? 'text-[#FF3B30]'
                        : 'text-green-500'
                }`}>{intakes.academic}</span>
                </span>
                <span className='text-sm text-grayFive font-medium'>{intakes.sessionend}</span>
            </div>

        </div>

        {/* Deadline */}
        <div className='py-6 flex items-center '>
            <p className='text-grayFive text-sm leading-6 font-semibold'>
                Academic Submission Deadline
            </p>
            <div className=' text-grayFive text-sm leading-6 font-normal flex items-center'>
                <p>
                    {submissionDeadline}
                </p>
                <TooltipProvider delayDuration={100}>
                    <Tooltip>
                        <TooltipTrigger>
                            <ToolTipIcon />
                        </TooltipTrigger>
                        <TooltipContent className='bg-primaryOne p-2.5 drop-shadow-5xl'>
                            <p className='text-grayFour w-36 text-[12px] leading-[16px]'>Submission deadlines are displayed in your local time.</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </div>
        <div className='py-6 flex items-center justify-between '>
            <div>
                <p className='text-grayFive text-sm leading-6 font-semibold'>
                    Application Fee
                </p>
                <p className=' text-sm leading-6 font-semibold text-graySix'>
                    {paymentAmount} USD
                </p>
            </div>

            <div className=' text-grayFive text-sm leading-6 font-normal flex items-center gap-2'>
                <p className='text-primaryColor font-semibold text-sm'>
                    {paymentStatus}
                </p>
                <TooltipProvider delayDuration={100}>
                    <Tooltip>
                        <TooltipTrigger>
                            <ToolTipIcon />
                        </TooltipTrigger>
                        <TooltipContent className='bg-primaryOne p-2.5 drop-shadow-5xl'>
                            <p className='text-grayFour w-36 text-[12px] leading-[16px]'>This is a payment that has begun, but is not complete.</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </div>
    </div>
    )
}

export default ApplicantInfoCard
