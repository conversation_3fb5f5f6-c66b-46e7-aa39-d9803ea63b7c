import React from 'react';
import Tick from '../assets/svg/Tick';
import Cross from '../assets/svg/Cross';


interface ServiceGetInPackageProps {
    serviceHeading: string;
    serviceAvailable?: string[];
    serviceNotAvailable?: string[];
}

const ServiceGetInPackage: React.FC<ServiceGetInPackageProps> = ({
    serviceHeading,
    serviceAvailable,
    serviceNotAvailable
}) => {
    return (
        <div>
            <h3 className='pt-5 pb-4 font-medium text-lg leading-5 text-graySix'>{serviceHeading}</h3>
            {serviceAvailable && serviceAvailable.map((service, index) => (
                <div 
                    key={index} 
                    className='flex gap-2.5 items-center pb-3'
                >
                    <Tick />
                    <span className='font-normal text-lg leading-5 text-grayFour'>{service}</span>
                </div>
            ))}

            {serviceNotAvailable && 
            serviceNotAvailable.map((service, index) => (
                <div 
                    key={index} 
                    className='flex gap-2.5 items-center pb-3 text-[#7A7B8280]'
                >
                    <Cross />
                    <span className='font-normal text-lg leading-5'>{service}</span>
                </div>
            ))}
        </div>
    )
}

export default ServiceGetInPackage