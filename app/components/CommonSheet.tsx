'use client';

import {
    Sheet,
    SheetClose,
    SheetContent,
    SheetDes<PERSON>,
    SheetHeader,
    Sheet<PERSON>itle,
    SheetTrigger,
} from '@/components/ui/sheet';
import React, { ReactNode, useRef, isValidElement, cloneElement, ReactElement } from 'react';

interface CommonSheetProps {
    label?: string;
    Icon?: ReactNode;
    children: ReactNode;
    title?: string;
    description?: string;
    triggerClassName?: string;
    onSave?: () => void;
}

const CommonSheet = ({
    label,
    Icon,
    children,
    title,
    description,
    triggerClassName = 'p-4 rounded-xl w-full flex items-center gap-2 text-base font-medium border border-blue-100 text-blue-600 hover:bg-',
    onSave,
}: CommonSheetProps) => {
    const formRef = useRef<HTMLFormElement>(null);
    return (
        <Sheet>
            <SheetTrigger asChild>
                <button className={triggerClassName}>
                    <div className='border-2 p-2 rounded-full border-primaryColor'>
                        {Icon}
                    </div>
                    {label}
                </button>
            </SheetTrigger>
            <SheetContent className='sm:max-w-[45%] overflow-auto px-12 pt-[88px]'>
                <SheetHeader className='flex flex-col justify-center items-center mb-16'>
                    <div className='bg-[#DCE8FF] border-[12px] rounded-full border-primaryOne p-[18px]'>
                        {Icon}
                    </div>
                    {title && <SheetTitle>{title}</SheetTitle>}
                    {description && (
                        <SheetDescription>{description}</SheetDescription>
                    )}
                </SheetHeader>
                <SheetDescription hidden />
                <div className="max-w-6xl mx-auto bg-white">
                    {isValidElement(children) && typeof children.type !== 'string'
                        ? cloneElement(children as ReactElement, { ref: formRef })
                        : children}
                    <div className="flex w-full gap-3 mt-12">
                        <SheetClose asChild className='w-full'>
                            <button className="py-2.5 px-6 rounded-lg border border-gray-300 text-gray-700 font-medium">
                                Cancel
                            </button>
                        </SheetClose>
                        <SheetClose asChild className='w-full'>
                            <button 
                                className="py-2.5 px-6 rounded-lg bg-primaryColor text-white font-medium"
                                onClick={() => formRef.current?.requestSubmit()}
                            >
                                Save
                            </button>
                        </SheetClose>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    );
};

export default CommonSheet;
