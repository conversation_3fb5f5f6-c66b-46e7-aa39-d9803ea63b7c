'use client';

import React, { useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import FacebookIcon from '@/app/assets/svg/facebook.svg';
import LinkedInIcon from '@/app/assets/svg/linkedin.svg';
import TwitterIcon from '@/app/assets/svg/twitter.svg';
import InstagramIcon from '@/app/assets/svg/instagram.svg';
import YouTubeIcon from '@/app/assets/svg/youtube.svg';
import GoogleScholarIcon from '@/app/assets/svg/google-scholar.svg';
import GroupIcon from '@/app/assets/svg/group.svg';
import PendingIcon from '@/app/assets/svg/pending.svg';
import { formatDate } from '@/lib/utils';

const StudentProfile = ({personalInfo, studentId}: {personalInfo: any, studentId: string}) => {
  const getInitials = (name: string) =>
    name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);

  const formatId = (id: any) => {
    if (!id) return 'N/A';
    if (typeof id === 'object' && id.low !== undefined && id.high !== undefined) {
      return id.low || id.high || 'N/A';
    }
    return String(id);
  };

  const InfoRow = ({ label, value }: { label: string; value: string }) => (
    <div className="grid grid-cols-[220px_1fr] text-sm">
      <p className="text-gray-500 font-medium">{label} :</p>
      <p className="text-gray-900 font-normal">{value || 'N/A'}</p>
    </div>
  );

  const getSocialIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'facebook': return <FacebookIcon />;
      case 'linkedin': return <LinkedInIcon />;
      case 'twitter': return <TwitterIcon />;
      case 'instagram': return <InstagramIcon />;
      case 'youtube': return <YouTubeIcon />;
      case 'google-scholar': return <GoogleScholarIcon />;
      case 'group': return <GroupIcon />;
      default: return <PendingIcon />;
    }
  };

  return (
    <div className="bg-white rounded-xl px-6 py-8 shadow-sm space-y-8">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <Avatar className="h-14 w-14">
            <AvatarImage src="https://github.com/shadcn.png" alt="Profile" />
            <AvatarFallback>{getInitials(`${personalInfo?.firstName} ${personalInfo?.lastName}`)}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{personalInfo?.firstName} {personalInfo?.lastName}</h2>
            <p className="text-sm text-gray-500">ID: #{formatId(studentId)}</p>
          </div>
        </div>
        <div className="text-sm space-y-1 text-right">
          <p><span className="text-gray-500 font-medium">Passport:</span> {personalInfo?.passport || 'N/A'}</p>
          <p><span className="text-gray-500 font-medium">National ID:</span> {personalInfo?.nid || 'N/A'}</p>
        </div>
      </div>

      {/* Personal Info */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-4">Personal Information</h3>
        <div className="grid md:grid-cols-2 gap-y-4 gap-x-8 bg-gray-50 p-5 rounded-md">
          <InfoRow label="First Name / Given Name" value={personalInfo?.firstName} />
          <InfoRow label="Last Name / Surname" value={personalInfo?.lastName} />
          <InfoRow label="Date Of Birth" value={formatDate(new Date(personalInfo?.dateOfBirth))} />
          <InfoRow label="NID" value={personalInfo?.nid} />
          <InfoRow label="Gender" value={personalInfo?.gender} />
          <InfoRow label="Email" value={personalInfo?.email} />
          <InfoRow label="Name In Native Language" value={personalInfo?.nameInNative} />
          <InfoRow label="Phone Number" value={personalInfo?.phone} />
          <InfoRow label="Present Address" value={personalInfo?.presentAddress?.address} />
          <InfoRow label="Permanent Address" value={personalInfo?.permanentAddress?.address} />
          <InfoRow label="Father Name" value={personalInfo?.fatherName} />
          <InfoRow label="Mother Name" value={personalInfo?.motherName} />
          <InfoRow label="Guardian Phone" value={personalInfo?.guardianPhone} />
          <InfoRow label="Marital Status" value={personalInfo?.maritalStatus?.status} />
          <InfoRow label="Spouse Name" value={personalInfo?.maritalStatus?.spouseName} />
          <InfoRow label="Spouse Passport Number" value={personalInfo?.maritalStatus?.spousePassport} />
          <InfoRow label="Spouse Phone Number" value={personalInfo?.maritalStatus?.spousePhone} />
          <InfoRow label="Sponsor Name" value={personalInfo?.sponsor?.name} />
          <InfoRow label="Relationship with Sponsor" value={personalInfo?.sponsor?.relation} />
          <InfoRow label="Sponsor Phone Number" value={personalInfo?.sponsor?.phone} />
          <InfoRow label="Sponsor Email" value={personalInfo?.sponsor?.email} />
        </div>
      </div>

      {/* Emergency Contact */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-4">Emergency Contact</h3>
        <div className="grid md:grid-cols-2 gap-y-4 gap-x-8 bg-gray-50 p-5 rounded-md">
          <InfoRow label="First Name" value={personalInfo?.emergencyContact?.firstName} />
          <InfoRow label="Last Name" value={personalInfo?.emergencyContact?.lastName} />
          <InfoRow label="Middle Name" value={personalInfo?.emergencyContact?.middleName} />
          <InfoRow label="Relationship" value={personalInfo?.emergencyContact?.relation} />
          <InfoRow label="Phone (Home)" value={personalInfo?.emergencyContact?.phoneHome} />
          <InfoRow label="Phone (Mobile)" value={personalInfo?.emergencyContact?.phoneMobile} />
        </div>
      </div>

      {/* Social Links */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-4">Social Links</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {(personalInfo?.socialLinks || []).map((link: any, index: number) => (
            <div key={index} className="flex items-center justify-between border rounded-lg px-4 py-2">
              <div className="flex items-center gap-2">
                <span className="text-xl">{getSocialIcon(link.platform)}</span>
                <span className="text-sm text-gray-700">@{link.url}</span>
              </div>
              <a
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primaryColor"
              >↗</a>
            </div>
          ))}
        </div>
      </div>

      {/* Notes */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-2">Notes</h3>
        <div className="bg-gray-50 p-4 rounded-md text-sm text-gray-700">
          {personalInfo?.note || 'No additional notes provided.'}
        </div>
      </div>
    </div>
  );
};

export default StudentProfile;