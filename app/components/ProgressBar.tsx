'use client'

import * as React from 'react';
import EmptyStar from '../assets/svg/EmptyStar';
import { Progress } from '@/components/ui/progress';

interface ProgressBarProps {
    progressValue: number;
    star?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
    star,
    progressValue
}) => {
    const [progress, setProgress] = React.useState(13)

    React.useEffect(() => {
        const timer = setTimeout(() => setProgress(progressValue), 500)
        return () => clearTimeout(timer)}, [progressValue])

    return (
        <div className='flex items-center gap-3.5'>
            {star && (
                <div className='flex gap-1 items-center'>
                    <EmptyStar />
                    <span className='font-normal text-sm leading-4 text-graySix '>{star}</span>
                </div>
            )}
            <Progress value={progress} className='bg-primaryTwo' />
        </div>
    )
}

export default ProgressBar
