import GoldTab from './GoldTab';
import { useRouter } from 'next/navigation';
import { partnerBenefitsTabs } from '@/common';
import { useSearchParams } from 'next/navigation';
import React, { useState, useEffect, Suspense } from 'react';


const TabContent = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const tabParam = searchParams.get('tab');

    const [activeTab, setActiveTab] = useState<number>(() => {
        if (tabParam === 'application') return 3; 
        return 1; 
    });

    useEffect(() => {
        const tabName = partnerBenefitsTabs.find((tab) => tab.id === activeTab)?.label.toLowerCase();
        if (tabName) {
            router.replace(`?tab=${tabName}`, { scroll: false });
        }
    }, [activeTab, router]);

    const renderContent = () => {
        switch (activeTab) {
            case 1:
                return <GoldTab />;
            case 2:
                return <GoldTab />;
            case 3:
                return <GoldTab />;
            case 4:
                return <GoldTab />;
            case 5:
                return <GoldTab />;
            default:
                return null;
        }
    };

    return (
        <>
            <div className='w-full pb-20'>
                <div className='flex justify-center pt-[60px] pb-10'>
                    <h2 className='font-semibold text-[28px] leading-8 text-graySix'>Your Preferred Partner Benefits </h2>
                </div>
                <div className='flex justify-center bg-white space-x-14 border-b border-[#1E62E01A]'>
                    {partnerBenefitsTabs.map((tab) => (
                        <button
                            key={tab.id}
                            className={`py-4 font-semibold text-base leading-[20px] ${
                              activeTab === tab.id
                                ? 'border-b-2 border-primaryColor text-primaryColor'
                                : 'text-grayFive hover:text-primaryColor'
                            }`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            {tab.label}
                        </button>
                    ))}
                </div>

                <div>{renderContent()}</div>
            </div>
        </>
    );
};

const PartnerBenefitsTab = () => {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <TabContent />
        </Suspense>
    )
}

export default PartnerBenefitsTab