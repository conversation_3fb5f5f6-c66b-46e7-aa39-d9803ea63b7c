'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { useLogoutMutation } from '@/lib/redux/api/authApi';
import { useDispatch } from 'react-redux';
import { logout } from '@/lib/redux/slices/authSlice';
import LogoutSettings from '../assets/svg/LogoutSettings';

const LogoutButton = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [logoutApi] = useLogoutMutation();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    const token = Cookies.get('accessToken');

    try {
      if (token) {
        await logoutApi(token).unwrap();
      }

      Cookies.remove('accessToken');
      dispatch(logout());

      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      Cookies.remove('accessToken');
      dispatch(logout());
      router.push('/login');
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <button
      onClick={handleLogout}
      disabled={isLoggingOut}
      className=" flex items-center gap-2 w-full px-3 py-1.5 hover:bg-primaryOne"
    >
      <LogoutSettings />
      {isLoggingOut ? 'Logging out...' : 'Logout'}
    </button>
  );
};

export default LogoutButton;