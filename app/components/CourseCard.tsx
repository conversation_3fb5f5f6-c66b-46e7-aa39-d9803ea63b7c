import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { CourseCardProps } from '@/types';
import { Checkbox } from '@/components/ui/checkbox';

const CourseCard: React.FC<CourseCardProps> = ({ 
    course, 
    isSelected, 
    onSelect, 
    isChecked 
}) => {
    const wrapperClasses = `space-y-2 rounded-xl p-4 transition-all cursor-pointer ${
        isSelected
          ? ' bg-primaryThree'
          : ' bg-white'
    }`;
    const content = (
        <>
        <div className='flex gap-2.5 items-center justify-between'>
            <div className='flex gap-2.5'>
                <div className='max-w-[20px] w-auto max-h-[20px] h-auto'>
                    <Image src={course.universityLogo} alt='University logo' />
                </div>
                <div>
                    <p className='font-normal text-xs text-grayFour'>
                        {course.university}
                    </p>
                </div>
            </div>
            {isChecked === true && (
                <Checkbox
                    id={`course-${course.id}`}
                    className='w-5 h-5 rounded-[50px] border border-[#D0D5DD] data-[state=checked]:bg-[#144296]'
                    checked={isSelected}
                    onCheckedChange={() => onSelect(course.id)}
                />
            )}
        </div>
        <h3 className='font-medium text-base leading-6 text-graySix mt-2'>
            {course.program}
        </h3>
        <p className='font-normal text-xs text-grayFour'>
            Tuition Fees:{' '}
            <span className='font-semibold text-xs text-grayFive'>
                ${course.tuitionFees[0]}
            </span>{' '}
            -{' '}
            <span className='font-semibold text-xs text-grayFive'>
                ${course.tuitionFees[1]} USD
            </span>
        </p>
        <p className='font-normal text-xs text-grayFour'>
            Course Rank:{' '}
        <span className='font-semibold text-xs text-grayFive'>
            {course.courseRank}
        </span>
        </p>
        <p className='font-normal text-xs text-grayFour'>
            Acceptance Rate:{' '}
        <span className='font-semibold text-xs text-grayFive'>
            {course.acceptanceRate}
        </span>
        </p>
        <p className='font-normal text-xs text-grayFour'>
            Program: {course.programType}
        </p>
        <div className='flex justify-between'>
            <p className='font-normal text-xs text-grayFour'>
                Intake: {course.intake}
            </p>
            <div className='max-w-[22px] w-auto max-h-[22px] h-auto'>
                <Image src={course.countryLogo} alt='Country logo' />
            </div>
        </div>
        </>
    );
    
    return !isChecked ? (
        <Link 
            href={`/course-finder/${course.slug}`} 
            className={wrapperClasses}
        >
            {content}
        </Link>
        ) : (
        <label 
            htmlFor={`course-${course.id}`} 
            className={wrapperClasses}
        >
            {content}
        </label>
    );
};
    

export default CourseCard;

