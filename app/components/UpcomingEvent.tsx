'use client';

import Link from 'next/link';
import Image from 'next/image';
import { events } from '@/common';
import Star from '@/app/assets/svg/star.svg';
import { UpcomingEventProps } from '@/types';
import ThreeDots from '../assets/svg/ThreeDots';
import DND from '@/app/assets/svg/do_not_disturb.svg';
import React, { useState, useEffect, useRef } from 'react';
import ChevronForward from '@/app/assets/svg/chevron_forward';

const parseTime = (timeString: string): Date => {
    const [time, modifier] = timeString.split(' ');
    const [hours, minutes] = time.split(':').map(Number);

    const date = new Date();
    date.setHours(modifier === 'pm' && hours < 12 ? hours + 12 : hours);
    date.setMinutes(minutes);
    date.setSeconds(0);
    date.setMilliseconds(0);

    return date;
};

const isLessThanOneHourFromNow = (startTime: string): boolean => {
    const eventStartTime = parseTime(startTime);
    const now = new Date();

    const differenceInMs = eventStartTime.getTime() - now.getTime();
    return differenceInMs > 0 && differenceInMs <= 3600000;
};

const UpcomingEvent: React.FC = () => {
    const [openIndex, setOpenIndex] = useState<number | null>(null);
    const dropdownRef = useRef<HTMLDivElement | null>(null);

    const toggleDropdown = (index: number) => {
        setOpenIndex(openIndex === index ? null : index);
    };

    const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
            setOpenIndex(null);
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className="py-6 px-5 flex flex-col gap-[22px]">
            <div className='flex justify-between items-center'>
                <h2 className="font-bold text-[18px] leading-[22px] text-grayFive">Upcoming Event</h2>
                <Link href={'/events-and-meetings'} className='flex gap-0.5 font-medium text-[10px] bg-primaryOne py-1.5 px-2 leading-3 rounded-2xl text-secondaryColor'>
                    View All
                    <ChevronForward />
                </Link>
            </div>
            <ul className="space-y-[20px]">
                {events.map((event: UpcomingEventProps, index: number) => {
                    const [startTime] = event.time.split(' - ');
                    const isJoinVisible = isLessThanOneHourFromNow(startTime);

                    return (
                        <li
                            key={index}
                            className="flex items-center justify-between relative"
                        >
                            <div className="flex gap-[17px] items-center">
                                <div className="flex flex-col items-center justify-center bg-white rounded-[6px] w-10 h-10 drop-shadow-[0px_2px_6px_rgba(87,88,94,0.12)]">
                                    <span className="text-xs leading-4 font-semibold text-primaryColor">
                                        {event.day}
                                    </span>
                                    <span className="font-normal text-xs leading-4 text-graySix">
                                        {event.date}
                                    </span>
                                </div>
                                <div>
                                    <p className="font-normal text-sm leading-[18px] text-graySix">
                                        {event.title}
                                    </p>
                                    <p className="text-xs text-grayThree leading-[14px]">
                                        {event.time}
                                    </p>
                                </div>
                            </div>
                            {isJoinVisible ? (
                                <button
                                    className="text-xs leading-[14px] font-medium bg-primaryColor text-white px-3 py-1.5 rounded-2xl"
                                    // onClick={() =>
                                    //     console.log(`Joining event: ${event.title}`)
                                    // }
                                >
                                    Join
                                </button>
                            ) : (
                                <button onClick={() => toggleDropdown(index)}>
                                    <ThreeDots />
                                </button>
                            )}
                            {openIndex === index && (
                                <div
                                    ref={dropdownRef}
                                    className="absolute right-0 mt-2 w-28 z-10"
                                >
                                    <div className="py-1 font-normal text-xs text-grayFive">
                                        <button
                                            className="drop-shadow-6xl bg-white rounded flex items-center gap-2 w-full px-3 py-1.5"
                                            // onClick={() =>
                                            //     console.log('Go to Profile')
                                            // }
                                        >
                                            <Image src={Star} alt="Menu icon" />
                                            Interested
                                        </button>
                                        <button
                                            className="drop-shadow-6xl bg-white rounded flex items-center gap-2 w-full px-3 py-1.5 mt-1"
                                            // onClick={() =>
                                            //     console.log('Ignore')
                                            // }
                                        >
                                            <Image src={DND} alt="Menu icon" />
                                            Ignore
                                        </button>
                                    </div>
                                </div>
                            )}
                        </li>
                    );
                })}
            </ul>
        </div>
    );
};

export default UpcomingEvent;
