import React, { useRef } from 'react';
import Personal, { PersonalRef } from './Personal';
import { useStudentSave } from '@/hooks/useStudentSave';

interface StudentFormWrapperProps {
  studentId?: string | number;
  initialData?: any;
  onSaveSuccess?: (data: any) => void;
}

const StudentFormWrapper: React.FC<StudentFormWrapperProps> = ({
  studentId,
  initialData,
  onSaveSuccess
}) => {
  const personalRef = useRef<PersonalRef>(null);
  const { saveStudentData, isLoading } = useStudentSave();

  const handleSave = async () => {
    if (personalRef.current) {
      const success = await personalRef.current.save();
      if (success && onSaveSuccess) {
        const formData = personalRef.current.getFormData();
        onSaveSuccess(formData);
      }
    }
  };

  const handleAutoSave = async (data: any) => {
    if (onSaveSuccess) {
      onSaveSuccess(data);
    }
  };

  return (
    <div className="space-y-6">
      <Personal
        ref={personalRef}
        studentId={studentId}
        initialData={initialData}
        onSave={handleAutoSave}
      />
      
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={isLoading}
          className="bg-indigo-600 text-white text-sm font-medium py-2 px-6 rounded-md hover:bg-indigo-700 transition focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : 'Save Student Information'}
        </button>
      </div>
    </div>
  );
};

export default StudentFormWrapper;
