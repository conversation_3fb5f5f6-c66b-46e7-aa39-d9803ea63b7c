'use clients'

import React from 'react';
import { SwitcherProps } from '@/types';
import { Switch } from '@/components/ui/switch';

const Switcher:React.FC<SwitcherProps> = ({ 
    label, 
    classes,  
    className, 
    isChecked, 
    handleToggle
}) => {
    return (
        <div className='flex items-center gap-5'>
            {label && (
                <p className='font-medium text-xs text-tertiary'>{ label }</p>
            )}
            <Switch 
                className={`${className}`} 
                classes={`${classes}`}
                id='airplane-mode' 
                checked={isChecked}
                onCheckedChange={handleToggle}
            />
        </div>
    )
}

export default Switcher