'use client';

import React from 'react';
import ArrowUp from '@/app/assets/svg/arrowUp';
import ArrowDown from '@/app/assets/svg/arrowDown';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

type AccordionItem = {
    data: Array<{ title: string; description: string; }>;
};

const CourseAccordion:React.FC<AccordionItem> = ({ data }) => {
    return (
        <Accordion
            type="single"
            collapsible
            className="w-full space-y-4 bg-white "
            defaultValue={data.length > 0 ? 'item-0' : undefined} // Default open the first item
        >
            {data.slice(0, 5).map((item, index) => (
                <AccordionItem
                    key={index}
                    value={`item-${index}`}
                    className="border border-[#1952BB33] rounded-[14px]"
                >
                    <AccordionTrigger
                        closeIcon={ArrowDown}
                        IconClass="!text-graySix"
                        openIcon={ArrowUp}
                        className="text-graySix flex justify-between items-center px-5 py-4 text-base font-medium hover:no-underline"
                    >
                        {item.title}
                    </AccordionTrigger>
                    <AccordionContent className="px-4 text-grayFive font-normal text-sm leading-[21px]">
                        {item.description}
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    );
};

export default CourseAccordion;
