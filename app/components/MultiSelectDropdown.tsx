'use client';

import { useState } from 'react';
import { Check } from 'lucide-react';
import ArrowDown from '../assets/svg/arrowDown';
import CrossIcon from '../assets/svg/Cross';

export interface DropdownOption {
    label: string;
    value: string;
}

interface MultiSelectDropdownProps {
    showSearch?: boolean;
    label?: string;
    data?: DropdownOption[];
    value?: DropdownOption[];
    onChange?: (selected: DropdownOption[]) => void;
}

export default function MultiSelectDropdown({
    showSearch = false,
    label,
    data = [],
    value ,
    onChange,
}: MultiSelectDropdownProps) {
    const isControlled = value !== undefined && onChange !== undefined;
    const [internalValue, setInternalValue] = useState<DropdownOption[]>([]);
    const selectedValues = isControlled ? value : internalValue;

    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    const setValue = (updated: DropdownOption[]) => {
        if (isControlled) {
            onChange?.(updated);
        } else {
            setInternalValue(updated);
        }
    };

    const toggleDropdown = () => setIsOpen(!isOpen);

    const handleSelect = (option: DropdownOption) => {
        const exists = selectedValues.some((v) => v.value === option.value);
        const updated = exists
            ? selectedValues.filter((v) => v.value !== option.value)
            : [...selectedValues, option];
        setValue(updated);
    };

    const handleRemove = (option: DropdownOption) => {
        const updated = selectedValues.filter((v) => v.value !== option.value);
        setValue(updated);
    };

    const filteredOptions = data.filter((option) =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const allFilteredSelected = filteredOptions.every((opt) =>
        selectedValues.some((v) => v.value === opt.value)
    );

    const handleSelectAllToggle = () => {
        const updated = allFilteredSelected
            ? selectedValues.filter(
                (v) => !filteredOptions.some((f) => f.value === v.value)
            )
            : Array.from(
                new Set([
                    ...selectedValues,
                    ...filteredOptions.filter(
                        (f) =>
                            !selectedValues.some((v) => v.value === f.value)
                    ),
                ])
            );
        setValue(updated);
    };

    return (
        <div className="relative w-full space-y-1.5">
            {label && (
                <label className="text-sm text-grayFive font-medium">
                    {label}
                </label>
            )}

            <div
                className="border border-primaryColor/20 max-h-[400px] py-3 px-3.5 rounded-[8px] flex justify-between items-center cursor-pointer bg-white overflow-y-auto"
                onClick={toggleDropdown}
            >
                <div className="text-grayTwo flex flex-wrap gap-2.5">
                    {selectedValues.length > 0
                        ? selectedValues.map((option, index) => (
                            <span
                                key={index}
                                className="py-1 px-2.5 border text-grayFive bg-grayOne rounded-full text-xs font-medium flex items-center gap-1"
                            >
                                {option.label}
                                <div
                                    className="cursor-pointer"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleRemove(option);
                                    }}
                                >
                                    <CrossIcon className="w-3 h-3 text-grayFour" />
                                </div>
                            </span>
                        ))
                        : 'Select Your Favorite Program'}
                </div>
                <ArrowDown className="text-grayFive" />
            </div>

            <div
                className={`
                    absolute p-3 mt-1.5 w-full bg-white rounded-md drop-shadow-md z-50 border
                    ${isOpen ? 'block' : 'hidden'}
                `}
            >
                {showSearch && (
                    <input
                        type="text"
                        placeholder="Search..."
                        className="w-full py-2.5 bg-white px-6 border border-primaryColor/30 outline-none rounded-[10px] placeholder:text-grayTwo sticky top-0 mb-3"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                )}

                {filteredOptions.length > 0 && (
                    <div className="flex justify-end mb-2">
                        <button
                            type="button"
                            className="text-sm text-blue-600 font-medium underline hover:text-blue-700"
                            onClick={(e) => {
                                e.stopPropagation();
                                handleSelectAllToggle();
                            }}
                        >
                            {allFilteredSelected
                                ? 'Clear All'
                                : 'Select All'}
                        </button>
                    </div>
                )}
                <div
                    className={` max-h-[220px] overflow-y-auto ${
                        showSearch ? 'mt-2' : ''
                    }`}
                >
                    {filteredOptions.map((option) => {
                        const isSelected = selectedValues.some(
                            (v) => v.value === option.value
                        );
                        return (
                            <div
                                key={option.value}
                            >
                                <button
                                    type="button"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleSelect(option);
                                    }}
                                    className="flex items-center w-full text-left hover:bg-blue-50 rounded-md p-2"
                                >
                                    <div
                                        className={`w-5 h-5 border flex items-center justify-center rounded ${
                                            isSelected
                                                ? 'bg-primaryColor border-tertiary'
                                                : 'border-grayThree hover:border-secondaryColor'
                                        }`}
                                    >
                                        {isSelected && (
                                            <Check className="w-4 h-4 text-white" />
                                        )}
                                    </div>
                                    <span className="ml-2 text-gray-700">
                                        {option.label}
                                    </span>
                                </button>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}
