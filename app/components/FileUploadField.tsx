'use client';

import React from 'react';
import Close from '../assets/svg/Close';
import { FileUploadSectionProps } from '@/types';

const MAX_FILE_SIZE_MB = 50;
const ACCEPTED_FILE_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];

const FileUploadField: React.FC<FileUploadSectionProps> = ({fields, setFields }) => {
    const handleFileUpload = (fieldId: string, newFiles: FileList | null) => {
        if (!newFiles) return;

        const validFiles: File[] = [];
        Array.from(newFiles).forEach((file) => {
        if (file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB && ACCEPTED_FILE_TYPES.includes(file.type)) {
            validFiles.push(file);
        } else {
            alert(`Invalid file: ${file.name}`);
        }
        });

        setFields((prevFields) =>
        prevFields.map((field) =>
            field.id === fieldId
            ? { ...field, files: [...field.files, ...validFiles] }
            : field
        )
        );
    };

    const handleRemoveFile = (fieldId: string, fileName: string) => {
        setFields((prevFields) =>
        prevFields.map((field) =>
            field.id === fieldId
            ? { ...field, files: field.files.filter((file) => file.name !== fileName) }
            : field
        )
        );
    };

    return (
        <div className='space-y-6'>
            {fields.map((field) => (
            <div key={field.id} className='flex flex-col md:flex-row items-center'>
                <div className='w-full md:w-[15%]'>
                    <label className='block text-base font-medium mb-2 text-grayFive'>{field.label}</label>
                </div>
                <div className='flex items-center gap-3 w-full md:w-[75%] rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                    <label
                        className={`relative border-[0.5px] py-0.5 px-2 border-primaryColor cursor-pointer font-medium text-xs text-primaryColor rounded`}
                    >
                        <span>Choose Files</span>
                        <input
                            type='file'
                            className='sr-only'
                            multiple
                            accept={ACCEPTED_FILE_TYPES.join(',')}
                            onChange={(e) => handleFileUpload(field.id, e.target.files)}
                        />
                    </label>
                    {field.files.length > 0 ? (
                        <div className='flex gap-2'>
                            {field.files.map((file) => (
                                <div 
                                    key={file.name} 
                                    className='flex items-center gap-1 rounded-[50px] px-1.5 bg-primaryOne font-medium text-sm text-[#144296]'
                                >
                                    <span>{file.name}</span>
                                    <button 
                                        className='cursor-pointer'
                                        onClick={() => handleRemoveFile(field.id, file.name)}
                                    >
                                        <Close />
                                    </button>
                                </div>
                            ))}
                        </div>
                    ): (
                        <span className='font-normal text-xs leading-[22px] text-grayFive'>No File Chosen</span>
                    )}
                </div>
            </div>
            ))}
        </div>
    );
};

export default FileUploadField;
