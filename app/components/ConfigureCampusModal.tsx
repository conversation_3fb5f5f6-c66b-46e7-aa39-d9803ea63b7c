import React, { useState } from 'react';
import Heading from './Heading';
import { Checkbox } from '@/components/ui/checkbox';
import EditAction from '../assets/svg/EditAction';

interface CourseData {
    title: string;
    lastAcademic: string;
    minGPA: string;
    lectureLanguages: string[];
    courseRank: number;
    acceptanceRate: string;
    intakes: string[];
    fields: string;
    formats: string[];
    testRequirements: {
        ielts?: {
            overall: number;
            literacy?: number;
            comprehension?: number;
            conversation?: number;
            production?: number;
        };
        duolingo?: { overall: number };
        gre?: { overall: number; vr?: number; qr?: number; aw?: number };
    };
    applicationFee: string;
    tuitionFee: string;
    additionalRequirements: string[];
}

interface CourseConfigProps {
    data: CourseData;
}

const ConfigureCampusModal: React.FC<CourseConfigProps> = ({ data }) => {
    const [selectedField, setSelectedField] = useState(data.fields[0]);
    const [selectedFormat, setSelectedFormat] = useState(data.formats[0]);
    const [selectedIntake, setSelectedIntake] = useState(data.intakes[0]);
    const [isEditing, setIsEditing] = useState(false);
    const [selectedTestRequirement, setSelectedTestRequirement] = useState(data.testRequirements.ielts?.overall || 0);
    const [checkedIntakeSubs, setCheckedIntakeSubs] = useState<{ [intake: string]: { parent: boolean; initial: boolean; transfer: boolean } }>(
        () =>
            Object.fromEntries(
                data.intakes.map((intake) => [
                    intake,
                    { parent: false, initial: false, transfer: false }
                ])
            )
    );
    const handleFieldChange = (field: string) => setSelectedField(field);
    const handleFormatChange = (format: string) => setSelectedFormat(format);
    const handleIntakeChange = (intake: string) => setSelectedIntake(intake);
    const handleTestRequirementChange = (testName: string, value: number) => setSelectedTestRequirement(value);
    const handleEditToggle = () => setIsEditing(!isEditing);
    const handleSave = () => {
        // Logic to save changes
        console.log('Saved:', { selectedField, selectedFormat });
        setIsEditing(false);
    };
    const handleCancel = () => {
        setSelectedField(data.fields[0]);
        setSelectedFormat(data.formats[0]);
        setIsEditing(false);
    };
    const handleIntakeParentToggle = (intake: string) => {
        setCheckedIntakeSubs((prev) => {
            const newParent = !prev[intake].parent;
            return {
                ...prev,
                [intake]: {
                    parent: newParent,
                    initial: newParent,
                    transfer: newParent
                }
            };
        });
    };
    const handleIntakeSubToggle = (intake: string, sub: 'initial' | 'transfer') => {
        setCheckedIntakeSubs((prev) => {
            const newSub = !prev[intake][sub];
            const otherSub = sub === 'initial' ? prev[intake].transfer : prev[intake].initial;
            const newParent = newSub || otherSub;
            return {
                ...prev,
                [intake]: {
                    ...prev[intake],
                    [sub]: newSub,
                    parent: newParent
                }
            };
        });
    };

    return (
        <div className=" w-full">
            <div className="flex justify-center mb-4">
                <Heading level={'h1'}>
                    Configure Campus by Course
                </Heading>
            </div>
            <div className='py-[26px] px-[30px]'>
                <h2 className="text-xl text-graySix font-medium py-6 border-b border-tertiary/20">{data.title}</h2>
                <div className='grid grid-cols-2 gap-6 py-6'>
                    <div className='flex flex-col justify-between'>
                        <span className="text-grayFive flex items-center gap-2.5">Last Academic: <p className=' font-medium'>{data.lastAcademic}</p></span>
                        <span className="text-grayFive flex items-center gap-2.5">Minimum GPA: <p className=' font-medium'>{data.minGPA}</p></span>
                        <span className="text-grayFive flex items-center gap-2.5">Lecture Languages: <p className=' font-medium'>{data.lectureLanguages.join(', ')}</p></span>
                    </div>
                    <div className='flex justify-center items-center gap-11 py-3.5'>
                        <span className="text-grayFive font-semibold flex flex-col items-center gap-2.5">
                            Course Rank
                            <span className='text-primaryColor font-semibold text-4xl'>{data.courseRank}</span>
                        </span>
                        <span className="text-grayFive font-semibold flex flex-col items-center gap-2.5">
                            Acceptance Rate
                            <span className='text-primaryColor font-semibold text-4xl'>{data.acceptanceRate}</span>
                        </span>
                    </div>
                </div>
                <div className='mb-6 border border-tertiary/20 rounded-[10px] overflow-hidden'>
                    <table className="w-full ">
                        <thead>
                            <tr className="bg-primaryOne text-grayFive text-sm">
                                <th className="text-left pl-2.5 py-3.5">Intake</th>
                                <th className="text-left pl-2.5 py-3.5">Field of Study</th>
                                <th className="text-left py-3.5">Format</th>
                                <th className="text-left py-3.5">
                                    Standardized Test Requirements
                                </th>
                                <th className="text-center py-3.5">Application Fee</th>
                                <th className="text-center py-3.5">Tuition Fee</th>
                                <th className="text-center py-3.5 pr-2.5">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td className="px-2.5 py-5">
                                    {data.intakes.map((intake) => (
                                        <div key={intake} className="mb-2">
                                            <div className="flex items-center font-medium gap-2">
                                                <Checkbox
                                                    checked={checkedIntakeSubs[intake]?.parent}
                                                    onCheckedChange={() => handleIntakeParentToggle(intake)}
                                                    disabled={!isEditing}
                                                />
                                                {intake}
                                            </div>
                                            <div className="flex flex-col gap-4 ml-8 mt-1">
                                                <label className="flex items-center gap-1">
                                                    <Checkbox
                                                        checked={checkedIntakeSubs[intake]?.initial}
                                                        onCheckedChange={() => handleIntakeSubToggle(intake, 'initial')}
                                                        disabled={!isEditing}
                                                    />
                                                    Initial
                                                </label>
                                                <label className="flex items-center gap-1">
                                                    <Checkbox
                                                        checked={checkedIntakeSubs[intake]?.transfer}
                                                        onCheckedChange={() => handleIntakeSubToggle(intake, 'transfer')}
                                                        disabled={!isEditing}
                                                    />
                                                    Transfer
                                                </label>
                                            </div>
                                        </div>
                                    ))}
                                </td>
                                <td className="px-2.5 py-5">
                                    {data.fields}
                                </td>
                                <td className="px-2.5 py-5">
                                    {data.formats.map((format) => (
                                        <div key={format} className='py-3'>
                                            <Checkbox
                                                checked={selectedFormat === format}
                                                onCheckedChange={() =>
                                                    handleFormatChange(format)
                                                }
                                                disabled={!isEditing}
                                            />

                                            <label htmlFor={format} className="ml-2">
                                                {format}
                                            </label>
                                        </div>
                                    ))}
                                </td>
                                <td className="px-2.5 py-5">
                                    {Object.entries(data.testRequirements).map(([testName, testFields]) => (
                                        <div key={testName} className="mb-2 flex gap-2.5 items-center">
                                            <Checkbox
                                                checked={selectedTestRequirement === testFields.overall}
                                                onCheckedChange={() =>
                                                    handleTestRequirementChange(testName, testFields.overall)
                                                }
                                                disabled={!isEditing}
                                            />
                                            <div className='flex flex-col gap-1'>
                                                <label htmlFor={testName} className=" font-semibold capitalize">
                                                    {testName}
                                                </label>
                                                <div className=" flex gap-10">
                                                    {Object.entries(testFields).map(([field, value]) => (
                                                        <div key={field} className='flex flex-col gap-1 items-center'>
                                                            <span className='text-grayFour text-sm'>{field.charAt(0).toUpperCase() + field.slice(1)}</span>
                                                            <span className='text-grayFive text-sm'>{value}</span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                            
                                        </div>
                                    ))}
                                </td>
                                <td className=" text-center px-2.5 py-5">
                                    {data.applicationFee}
                                </td>
                                <td className=" text-center px-2.5 py-5">{data.tuitionFee}</td>
                                <td className=" text-center px-2.5 py-5">
                                    <button
                                        onClick={handleEditToggle}
                                        className='bg-primaryColor rounded-full p-1 hover:bg-secondaryColor text-white'
                                    >
                                        <EditAction />
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div className="mb-4 border border-tertiary/20 rounded-[10px] p-4">
                    <h3 className="font-medium text-grayFive pb-2.5 border-b border-tertiary/20">Additional Requirements</h3>
                    <ul className="list-decimal list-inside grid grid-cols-2 text-grayFive text-sm mt-3.5">
                        {data.additionalRequirements.map((req, index) => (
                            <li key={index}>{req}</li>
                        ))}
                    </ul>
                    <p className='text-grayFive text-sm mt-4'>These requirements can vary slightly by program</p>
                </div>
                {isEditing && (
                    <div className="flex justify-end gap-4">
                        <button
                            onClick={handleCancel}
                            className="bg-gray-300 px-4 py-2 rounded hover:bg-gray-400"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleSave}
                            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                        >
                            Save Changes
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ConfigureCampusModal;
