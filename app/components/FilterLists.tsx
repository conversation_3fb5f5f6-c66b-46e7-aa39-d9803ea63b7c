import React from 'react';
import FilterOption from './FilterOption';
import { FilterListsProps } from '@/types';

const FilterLists: React.FC<FilterListsProps> = ({
    lists,
    onRemove,
    onClearAll
}) => {
    return (
        <div className='flex flex-wrap gap-3'>
            <div className='flex flex-wrap gap-3'>
                {lists.map((list, index) => (
                    <FilterOption 
                        key={index} 
                        option={list} 
                        onRemove={onRemove}
                    />
                ))}
            </div>
            {lists.length > 0 && (
                <button 
                    onClick={onClearAll} 
                    className='font-medium text-xs leading-6 text-primaryColor'
                >
                    Clear all
                </button>
            )}
        </div>
    )
}

export default FilterLists
