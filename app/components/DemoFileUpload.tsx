// 'use client'
// import React, { useState } from 'react';
// import { uploadImage } from '@/lib/api';

// const DemoFileUpload = () => {
//     const [imageUrl, setImageUrl] = useState<string | null>(null);
//     const [isUploading, setIsUploading] = useState(false);
//     const [error, setError] = useState<string | null>(null);

//     const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
//         const file = e.target.files?.[0];
//         if (!file) return;

//         setIsUploading(true);
//         setError(null);

//         try {
//             const url = await uploadImage(file);
//             setImageUrl(url);
//         } catch (err) {
//             console.error('Upload failed', err);
//             setError('Upload failed');
//         } finally {
//             setIsUploading(false);
//         }
//     };

//   return (
//     <div className="space-y-4">
//       <input type="file" accept="image/*" onChange={handleFileChange} />
//       {isUploading && <p>Uploading...</p>}
//       {error && <p className="text-red-500">{error}</p>}
//       {imageUrl && (
//         <div>
//           <p>Uploaded Image:</p>
//           <img src={imageUrl} alt="Uploaded" className="w-64 h-auto rounded" />
//         </div>
//       )}
//     </div>
//   );
// }

// export default DemoFileUpload

// 'use client';

// import { useState } from 'react';
// import { uploadFile  } from '@/lib/api';

// export default function FileUpload() {
//   const [fileUrl, setFileUrl] = useState<string | null>(null);
//   const [isUploading, setIsUploading] = useState(false);
//   const [error, setError] = useState<string | null>(null);

//   const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (!file) return;

//     setIsUploading(true);
//     setError(null);

//     try {
//       const url = await uploadFile(file);
//       setFileUrl(url);
//     } catch (err) {
//       console.error('Upload failed', err);
//       setError('Upload failed');
//     } finally {
//       setIsUploading(false);
//     }
//   };

//   return (
//     <div className="space-y-4">
//       <input type="file" onChange={handleChange} />
//       {isUploading && <p>Uploading...</p>}
//       {error && <p className="text-red-500">{error}</p>}
//       {fileUrl && (
//         <div>
//           <p>Uploaded File:</p>
//           <a href={fileUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">
//             {fileUrl}
//           </a>
//         </div>
//       )}
//     </div>
//   );
// }

'use client';

import React, { useState } from 'react';
import { useUploadImageMutation } from '@/lib/redux/api/fileUploadApi';

const DemoFileUpload = () => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [uploadImage, { isLoading }] = useUploadImageMutation();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setError(null);

    try {
      const url = await uploadImage(file).unwrap();
      setImageUrl(url);
    } catch (err) {
      console.error('Upload failed:', err);
      setError('Upload failed');
    }
  };

  return (
    <div className="space-y-4">
      <input type="file" accept="image/*" onChange={handleFileChange} />
      {isLoading && <p>Uploading...</p>}
      {error && <p className="text-red-500">{error}</p>}
      {imageUrl && (
        <div>
          <p>Uploaded Image:</p>
          <img src={imageUrl} alt="Uploaded" className="w-64 h-auto rounded" />
        </div>
      )}
    </div>
  );
};

export default DemoFileUpload;
