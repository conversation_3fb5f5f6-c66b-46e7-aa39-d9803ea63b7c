'use client';

import Image from 'next/image';
import Pagination from './Pagination';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ApplicationsTableProps } from '@/types';
import ApplicationStatus from './ApplicationStatus';
import AddDocumentOutline from '@/app/assets/svg/add-document-outline';

const ApplicationsTable: React.FC<ApplicationsTableProps> = ({ 
    applications,
    itemsPerPage = 3,  
}) => {

    const [currentPage, setCurrentPage] = useState(1);
    const router = useRouter();

    const totalPages = Math.ceil(applications.length / itemsPerPage);

    const currentApplications = applications.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    const tableHeadData = [
        'Details',
        'Date Created',
        'Status'
    ];

    return (
        <div className='flex flex-col h-auto justify-between py-5 pb-[111px]'>
            <div className='flex justify-between items-center'>
                <h1 className='md:text-[28px] text-[18px] md:leading-[33px] leading-[21px] font-bold text-graySix'>All Applications</h1>
                <Button className='py-2.5 px-5 rounded-[50px] bg-primaryColor text-white md:text-sm text-xs leading-[17px] font-semibold'>
                    <AddDocumentOutline />
                    <span>Add Application</span>
                </Button>
            </div>
                <div className='mt-5 mb-[50px] rounded-[20px] bg-white'>
                    <div className='overflow-x-auto'>
                        <table className='md:w-full md:table-fixed w-[1000px]'>
                            <thead>
                                <tr className='text-left'>
                                    {tableHeadData.map((thead,index) => (
                                        <th 
                                            scope='col' 
                                            key={index}
                                            className='text-graySix font-medium text-base leading-5 py-[26px] md:w-[50%] min-w-full pl-6'
                                        >
                                            {thead}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {currentApplications?.map((app, index) => (
                                    <tr 
                                        key={index} 
                                        className='hover:bg-primaryFour duration-700 border-t border-[#1E62E033] cursor-pointer'
                                        // onClick={() => handleRowClick(app.id)} // Pass application ID or identifier here
                                        onClick={() => router.push('/application-student-profile')}
                                    >
                                        <td className='py-[26px] flex gap-6 pl-6'>
                                            <Image
                                                src={app.logo}
                                                alt={`${app.university} logo`}
                                                className="w-12 h-12 rounded-full"
                                            />
                                            <div className='flex flex-col gap-1.5'>
                                                <p className='font-medium text-base leading-[18px] text-graySix'>{app.program}</p>
                                                <p className='text-sm leading-[17px] font-normal text-graySix'>
                                                    {app.university}
                                                </p>
                                                <p className='font-normal text-xs text-grayFour leading-[15px]'>
                                                    {app.location}
                                                </p>
                                            </div>
                                        </td>
                                        <td className='text-sm leading-[17px] font-medium text-grayFour'>
                                            {app.dateCreated}
                                        </td>
                                        <td className='text-sm leading-[17px] font-semibold  pr-6'>
                                            <span className={`py-1 rounded-full text-sm`}>
                                                <ApplicationStatus status={app.status} />
                                            </span>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
                <Pagination 
                    onPageChange={goToPage} 
                    currentPage={currentPage} 
                    totalPages={totalPages}
                />
        </div>
    );
};

export default ApplicationsTable;
