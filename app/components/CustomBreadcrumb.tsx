import React from 'react';
import { usePathname } from 'next/navigation';
import ArrowForward from '@/app/assets/svg/arrow_forward';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

type BreadcrumbItem = {
    name: string;
    href: string;
    isCurrent: boolean;
};

const CustomBreadcrumb = () => {
    const pathname = usePathname();
    const pathSegments = pathname.split('/').filter(Boolean);

    const breadcrumbs: BreadcrumbItem[] = pathSegments.map((segment, index) => {
        const href = '/' + pathSegments.slice(0, index + 1).join('/');
        const name = decodeURIComponent(segment).replace(/-/g, ' ');

        return {
            name: name.charAt(0).toUpperCase() + name.slice(1),
            href,
            isCurrent: index === pathSegments.length - 1,
        };
    });

    return (
        <div className='mt-10'>
            {pathSegments.length > 0 && (
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink
                                href='/'
                                className='font-normal text-base hover:text-grayFour hover:underline text-grayFour'
                            >
                                Dashboard
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        {breadcrumbs.map((breadcrumb, index) => (
                            <BreadcrumbItem key={index} className='flex items-center space-x-1'>
                                <ArrowForward className='w-4 h-4 text-grayFour' />
                                {breadcrumb.isCurrent ? (
                                    <span
                                        className='font-medium text-base text-graySix leading-5'
                                        aria-current='page'
                                    >
                                        {breadcrumb.name}
                                    </span>
                                ) : (
                                    <BreadcrumbLink
                                        href={breadcrumb.href}
                                        className='font-normal text-base hover:text-grayFour hover:underline text-grayFour'
                                    >
                                        {breadcrumb.name}
                                    </BreadcrumbLink>
                                )}
                            </BreadcrumbItem>
                        ))}
                    </BreadcrumbList>
                </Breadcrumb>
            )}
        </div>
    );
};

export default CustomBreadcrumb;
