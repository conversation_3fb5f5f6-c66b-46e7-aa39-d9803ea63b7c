import React from 'react';
import Heading from './Heading';
// import Pagination from './Pagination';
import Clock from '@/app/assets/svg/clock';
import Calendar from '@/app/assets/svg/calendar';
import { applicationSummaryBoxProps } from '@/types';
import Universities from '@/app/assets/svg/universities';

const applicationSummaryBox: React.FC<applicationSummaryBoxProps> = ({ 
    daysLeft, 
    isIncomplete,
    complete
}) => {
    const getBackgroundColor = () => {
        if (daysLeft <= 6) return 'bg-[#FEF3CC] '; 
        if (daysLeft >= 7) return 'bg-[#1E62E0] bg-opacity-10'; 
        return '';
    };

    return (
        <div className='bg-white flex justify-between items-center rounded-[10px]'>
            <div className='flex'>
                <div className={`flex ${complete ? 'bg-[#ECFDF3]' : `${getBackgroundColor()}`} items-center justify-center px-[30px] rounded-l-[10px]`}>
                    <span className='font-semibold text-lg leading-5'>#08723</span>
                </div>
                <div className='flex flex-col py-4 pl-7 gap-3'>
                    <Heading 
                        level='h3' 
                        className='font-medium text-base leading-5 text-primaryColor'
                    >
                        New York Institute of Technology - Vancouver (NYIT) - Withdrawal Form
                    </Heading>
                    <div className='flex gap-8'>
                        <div className='flex gap-2.5 items-center'>
                            <Calendar className='w-4 h-4 text-grayFour' />
                            <span className='font-medium text-sm leading-4 text-grayFour'>Oct 2024</span>
                        </div>
                        <div className='flex gap-2.5 items-center'>
                            <Universities className='w-4 h-4 text-grayFour' />
                            <span className='font-medium text-sm leading-4 text-grayFour'>Oct 2024</span>
                        </div>
                    </div>
                </div>
            </div>
            {isIncomplete && (
                <div>
                    <div className={`${getBackgroundColor()} rounded-[50px] py-2.5 px-3.5 flex gap-2 items-center mr-[30px]`}>
                        <Clock />
                        <span className='font-semibold text-sm leading-4 text-[#664D03]'>2 days left</span>
                    </div>
                </div>
            )}
        </div>
    )
}

export default applicationSummaryBox;