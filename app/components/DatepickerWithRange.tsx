'use client';
import * as React from 'react';
import { addDays, format, isValid } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
interface CalendarNavigationProps {
    displayMonth: Date;
    onMonthChange: (date: Date) => void;
}

const CalendarNavigation: React.FC<CalendarNavigationProps> = ({
    displayMonth,
    onMonthChange,
}) => {
    const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    const monthIndex = displayMonth.getMonth();
    const year = displayMonth.getFullYear();

    const currentYearActual = new Date().getFullYear();
    const years = Array.from(
        { length: 6 },
        (_, i) => currentYearActual - 5 + i
    );

    const handleMonthChange = (newMonthIndex: string) => {
        const newMonth = new Date(year, parseInt(newMonthIndex));
        onMonthChange?.(newMonth);
    };

    const handleYearChange = (newYear: string) => {
        const newMonth = new Date(parseInt(newYear), monthIndex);
        onMonthChange?.(newMonth);
    };

    return (
        <div className="flex items-center justify-center space-x-2 py-1 px-3">
            <Select
                value={monthIndex.toString()}
                onValueChange={handleMonthChange}
            >
                <SelectTrigger className="h-8 w-[110px]">
                    <SelectValue placeholder="Month" />
                </SelectTrigger>
                <SelectContent>
                    {months.map((month, i) => (
                        <SelectItem key={month} value={i.toString()}>
                            {month}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            <Select value={year.toString()} onValueChange={handleYearChange}>
                <SelectTrigger className="h-8 w-[80px]">
                    <SelectValue placeholder="Year" />
                </SelectTrigger>
                <SelectContent>
                    {years.map((y) => (
                        <SelectItem key={y} value={y.toString()}>
                            {y}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
};

interface DatePickerWithRangeProps extends React.HTMLAttributes<HTMLDivElement> {
    label?: string;
}

export default function DatePickerWithRange({
    className,
    label
}: DatePickerWithRangeProps) {
    const today = new Date();

    const [date, setDate] = React.useState<DateRange | undefined>({
        from: addDays(today, -7),
        to: today,
    });

    const [calendarMonth, setCalendarMonth] = React.useState<Date>(
        date?.from ?? today
    );
    const [singleDate, setSingleDate] = React.useState<Date>();
    const [mode, setMode] = React.useState<'range' | 'single'>('single');

    const handlePresetChange = (value: string) => {
        const today = new Date();
        switch (value) {
            case 'today':
                setDate({ from: today, to: today });
                break;
            case 'yesterday':
                const yesterday = addDays(today, -1);
                setDate({ from: yesterday, to: yesterday });
                break;
            case 'last7':
                setDate({ from: addDays(today, -7), to: today });
                break;
            case 'last30':
                setDate({ from: addDays(today, -30), to: today });
                break;
            case 'last90':
                setDate({ from: addDays(today, -90), to: today });
                break;
        }
    };

    const formatDateDisplay = () => {
        if (mode === 'single') {
            return singleDate && isValid(singleDate) ? format(singleDate, 'LLL dd, y') : 'Resubmission Date';
        }

        if (date?.from && isValid(date.from)) {
            if (date.to && isValid(date.to)) {
                return `${format(date.from, 'LLL dd, y')} - ${format(
                    date.to,
                    'LLL dd, y'
                )}`;
            }
            return format(date.from, 'LLL dd, y');
        }

        return 'Pick a date range';
    };

    return (
        <div className={cn('grid gap-1.5', className)}>
            {label && 
                <label className='text-sm text-grayFive font-medium'>{label}</label>
            }
            <Popover>
                <PopoverTrigger asChild>
                    <Button
                        id="date"
                        className={cn(
                            'w-full justify-start text-left font-normal border border-primaryColor/30 py-3 px-3.5 rounded-[8px]',
                            !date && !singleDate && 'text-muted-foreground'
                        )}
                    >
                    {formatDateDisplay() || <span >Resubmission Date</span>}
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {/* {formatDateDisplay()} */}
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-3" align="start">
                    <Tabs
                        defaultValue="single"
                        onValueChange={(value) =>
                            setMode(value as 'range' | 'single')
                        }
                    >
                        <TabsList className="grid w-full grid-cols-2 mb-4 bg-primaryFour ">
                            <TabsTrigger className='data-[state=active]:shadow-none data-[state=active]:text-primaryColor data-[state=inactive]:text-grayThree' value="range">Date Range</TabsTrigger>
                            <TabsTrigger className='data-[state=active]:shadow-none data-[state=active]:text-primaryColor data-[state=inactive]:text-grayThree' value="single">
                                Single Date
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="range" className="space-y-4 flex ">
                            <div className="flex flex-col gap-2 bg-primaryOne mt-4 rounded-l-md">
                                <Button 
                                    onClick={() => handlePresetChange('today')}
                                >
                                    Today only
                                </Button>
                                <Button
                                    onClick={() => handlePresetChange('yesterday')}
                                >
                                    Yesterday only
                                </Button>
                                <Button
                                    onClick={() => handlePresetChange('last7')}
                                >
                                    Last 7 days
                                </Button>
                                <Button
                                    onClick={() => handlePresetChange('last30')}
                                >
                                    Last 30 days
                                </Button>
                                <Button
                                    onClick={() => handlePresetChange('last90')}
                                >
                                    Last 90 days
                                </Button>
                            </div>

                            <div className=" border-l ">
                                <Calendar
                                    components={{
                                        Caption: ({ displayMonth }) => (
                                            <CalendarNavigation
                                                displayMonth={displayMonth}
                                                onMonthChange={setCalendarMonth}
                                            />
                                        ),
                                    }}
                                    initialFocus
                                    mode="range"
                                    defaultMonth={calendarMonth}
                                    month={calendarMonth}
                                    fromMonth={new Date(2020, 0)}
                                    toMonth={today}
                                    selected={date}
                                    onSelect={setDate}
                                    numberOfMonths={2}
                                    toDate={today}
                                    onMonthChange={setCalendarMonth}
                                    classNames={
                                        {
                                            day_range_start: "bg-primaryColor hover:bg-primaryColor hover:text-white ",
                                            day_range_end: "bg-primaryColor hover:bg-primaryColor hover:text-white ",
                                            day_selected: " text-white ! hover:text-primaryColor ",
                                            day_range_middle: "aria-selected:bg-primaryFour aria-selected:text-primaryColor ",
                                            cell: "[&:has([aria-selected])]:bg-primaryFour " ,
                                        }
                                    }
                                />
                            </div>
                        </TabsContent>

                        <TabsContent value="single">
                            <Select
                                onValueChange={(value) => {
                                    switch (value) {
                                        case 'today':
                                            setSingleDate(new Date());
                                            setCalendarMonth(new Date());
                                            break;
                                        case 'yesterday':
                                            const y = addDays(new Date(), -1);
                                            setSingleDate(y);
                                            setCalendarMonth(y);
                                            break;
                                        case 'lastWeek':
                                            const lw = addDays(new Date(), -7);
                                            setSingleDate(lw);
                                            setCalendarMonth(lw);
                                            break;
                                        case 'lastMonth':
                                            const lm = addDays(new Date(), -30);
                                            setSingleDate(lm);
                                            setCalendarMonth(lm);
                                            break;
                                    }
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select" />
                                </SelectTrigger>
                                <SelectContent position="popper">
                                    <SelectItem value="today">Today</SelectItem>
                                    <SelectItem value="yesterday">
                                        Yesterday
                                    </SelectItem>
                                    <SelectItem value="lastWeek">
                                        7 days ago
                                    </SelectItem>
                                    <SelectItem value="lastMonth">
                                        30 days ago
                                    </SelectItem>
                                </SelectContent>
                            </Select>

                            <div className="rounded-md border mt-4">
                                <Calendar
                                    components={{
                                        Caption: ({ displayMonth }) => (
                                            <CalendarNavigation
                                                displayMonth={displayMonth}
                                                onMonthChange={setCalendarMonth}
                                            />
                                        ),
                                    }}
                                    mode="single"
                                    selected={singleDate}
                                    onSelect={(date) => {
                                        if (date) {
                                            setSingleDate(date);
                                            setCalendarMonth(date);
                                        }
                                    }}
                                    defaultMonth={calendarMonth}
                                    onMonthChange={setCalendarMonth}
                                    fromMonth={new Date(2020, 0)}
                                    toMonth={today}
                                    initialFocus
                                    toDate={today}
                                />
                            </div>
                        </TabsContent>
                    </Tabs>
                </PopoverContent>
            </Popover>
        </div>
    );
}
