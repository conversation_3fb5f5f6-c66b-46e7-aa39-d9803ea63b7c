'use client';

import React from 'react';
import { Tabs, Ta<PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface TabItem {
    value: string;
    name: string;
    logo: React.ReactNode;
    content: React.ReactNode;
}

interface HorizontalTabProps {
    defaultValue?: string;
    tabs: TabItem[];
}

const HorizontalTab: React.FC<HorizontalTabProps> = ({
    defaultValue,
    tabs,
}) => {
    return (
        <Tabs defaultValue={defaultValue || tabs[0]?.value} className="w-full">
            <TabsList className="flex w-full overflow-x-auto gap-2 border-b bg-muted p-1 rounded-none">
                {tabs.map((tab) => (
                    <TabsTrigger
                        key={tab.value}
                        value={tab.value}
                        className="flex items-center gap-2 whitespace-nowrap px-4 py-2"
                    >
                        {tab.logo}
                        {tab.name}
                    </TabsTrigger>
                ))}
            </TabsList>

            {tabs.map((tab) => (
                <TabsContent key={tab.value} value={tab.value} className="mt-4">
                    {tab.content}
                </TabsContent>
            ))}
        </Tabs>
    );
};

export default HorizontalTab;
