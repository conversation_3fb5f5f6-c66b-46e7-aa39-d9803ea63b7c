import React from 'react';
import { TabLayoutProps } from '@/types';
import {
    Tabs,
    TabsList,
    TabsTrigger,
    TabsContent
} from '@/components/ui/tabs';

const TabLayout: React.FC<TabLayoutProps> = ({
    children,
    tabLists,
    tabContents
}) => {

    return (
        <Tabs 
            defaultValue={tabLists[0]?.value} 
            className='w-full'
        >
            <TabsList className='bg-[#E9F0FF] h-20 rounded-[16px] w-full'>
                <div className='mx-1.5 bg-primaryOne grid grid-cols-2 w-full rounded-xl'>
                    {tabLists.map((tab, index) => (
                        <TabsTrigger 
                            className={`data-[state=active]:shadow-none rounded-none ${index % 2 === 0 ? 'rounded-l-xl': 'rounded-r-xl'} data-[state=active]:text-primaryColor font-semibold text-base leading-5 text-grayFive py-5`}
                            value={tab.value}
                            key={index}
                        >
                            {tab.label}
                        </TabsTrigger>
                    ))}
                </div>
            </TabsList>

            {children}

            {tabContents.map((tabContent, index) => (
                <TabsContent 
                    value={tabContent.value}
                    key={index}
                >
                    {tabContent.content()}
                </TabsContent>
            ))}
            
        </Tabs>
    )
}

export default TabLayout