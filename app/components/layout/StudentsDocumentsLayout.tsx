import React from 'react';
import { StudentsDocumentsLayoutProps } from '@/types';

const StudentsDocumentsLayout: React.FC<StudentsDocumentsLayoutProps> = ({ 
    children, 
    className,
    sectionTitle
}) => {
    return (
        <>
        <div className={`flex gap-3 items-center ${className}`}>
            <h3 className='font-semibold text-xl tracking-[0.4px] text-graySix'>{sectionTitle}</h3>
            <p className='font-normal text-[10px] text-grayFour'>*Accepted file types: JPEG, PNG, PDF, up to 50 MB</p>
        </div>
        <div className='rounded-xl py-10 px-5 mt-6 bg-[#1E62E0] bg-opacity-[4%]'>
            {children}
        </div>
        </>
    )
}

export default StudentsDocumentsLayout