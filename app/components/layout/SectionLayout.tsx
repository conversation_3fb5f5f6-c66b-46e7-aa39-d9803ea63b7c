import React from 'react';
import { SectionLayoutProps } from '@/types';

const SectionLayout: React.FC<SectionLayoutProps> = ({
    heading,
    children,
    className
}) => {
    return (
        <div className="w-full overflow-hidden">
            {heading && (
                <h2 className='py-5 font-semibold text-2xl leading-[30px] text-graySix'>{ heading }</h2>
            )}
            <div className={`rounded-[20px] p-5 bg-white w-full overflow-hidden ${className}`}>
                {children}
            </div>
        </div>
    )
}

export default SectionLayout