'use client'

import React, { useState} from 'react';
import Heading from '@/app/components/Heading';
import Block from '@/app/assets/svg/Block';
import Cross from '@/app/assets/svg/Cross';
import Check from '@/app/assets/svg/check';
import Canada from '@/app/assets/svg/canada';
import Pending from '@/app/assets/svg/pending';
import { CourseFinderFiltersProps } from '@/types';
import CoursesFilter from '@/app/components/CoursesFilter';
import UserProfile from '@/app/assets/svg/userProfile';
import ButtonWithIcon from '@/app/components/ButtonWithIcon';
import TableLayout from '@/app/components/layout/TableLayout';
import SwapVertical from '@/app/assets/svg/SwapVertical';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { 
    courses, 
    countries, 
    programs, 
    sortByOptions, 
    intakes 
} from '@/common';

const page = () => {
     const [filters, setFilters] = useState<CourseFinderFiltersProps>({
        program: '',
        intake: '',
        country: [],
        tuitionFees: '',
        sortBy: '',
    });
    const tableHead = [
        { label: 'ID', icon: <SwapVertical />},
        { label: 'Name'},
        { label: 'Mobile'},
        { label: 'Program'},
        { label: 'Intake'},
        { label: 'Country'},
        { label: 'Status'},
    ];

    const tableData = [
        {
            id: "262",
            name: "Nolan Westervelt",
            mobile: "+8801962-446543",
            program: "2 year Undergraduate Diploma",
            intake: "Oct 2024",
            country: <Canada />,
            status: {label: 'Pending', icon: <Pending />}
        },
        {
            id: "261",
            name: "Nolan Westervelt",
            mobile: "+8801962-446543",
            program: "2 year Undergraduate Diploma",
            intake: "Oct 2024",
            country: <Canada />,
            status: {label: 'Reject', icon: <Cross />},
        },
        {
            id: "260",
            name: "Nolan Westervelt",
            mobile: "+8801962-446543",
            program: "2 year Undergraduate Diploma",
            intake: "Oct 2024",
            country: <Canada />,
            status: {label: 'Success', icon: <Check />},
        },
        {
            id: "263",
            name: "Nolan Westervelt",
            mobile: "+8801962-446543",
            program: "2 year Undergraduate Diploma",
            intake: "Oct 2024",
            country: <Canada />,
            status: {label: 'Pending', icon: <Pending />},
        },
        {
            id: "264",
            name: "Nolan Westervelt",
            mobile: "+8801962-446543",
            program: "2 year Undergraduate Diploma",
            intake: "Oct 2024",
            country: <Canada />,
            status: {label: 'Discontinue', icon: <Block />},
        },
    ]
    return (
        <DashboardLayout>
            <div className='flex justify-between items-center py-5'>
                <Heading level='h1'>
                    Students
                </Heading>
                <ButtonWithIcon 
                    icon={<UserProfile />} 
                    label='Add Student' 
                    className='text-white bg-primaryColor rounded-[50px] px-5 py-2.5 drop-shadow-[0_1px_4px_rgba(0, 0, 0, 0.05)]' 
                />
            </div>
            <CoursesFilter 
                programs={programs}
                intakes={intakes}
                countries={countries}
                tuitionFees={filters.tuitionFees}
                sortByOptions={sortByOptions}
                onFilterChange={setFilters}
            />
            <TableLayout 
                tableHeading='All Students'
                tableHead ={tableHead}
                tableData = {tableData}
            />
        </DashboardLayout>    
    )
}

export default page