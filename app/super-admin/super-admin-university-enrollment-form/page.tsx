'use client';

import React, { useEffect, useState } from 'react';
import { DropdownOption } from '@/types';
import Upload from '@/app/assets/svg/upload';
import Heading from '@/app/components/Heading';
import Download from '@/app/assets/svg/download';
import InputField from '@/app/components/InputField';
import EmailInbox from '@/app/assets/svg/emailInbox';
import SelectField from '@/app/components/SelectField';
import NoteTextarea from '@/app/components/NoteTextarea';
import { Country, State, City } from 'country-state-city';
import ImageUploader from '@/app/components/ImageUploader';
import PhoneNumberInput from '@/app/components/PhoneNumberInput';
import InputFieldWithIcon from '@/app/components/InputFieldWithIcon';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { UniTypeSelectButton } from '@/common';
import SelectAndSearchCombobox from '@/app/components/SelectAndSearchCombobox';

const page = () => {
    const [logo, setLogo] = useState<File | null>(null);
    const [address, setAddress] = useState({
        address: '',
        country: '',
        state: '',
        city: '',
        zip: '',
    });
    const [states, setStates] = useState<DropdownOption[]>([]);
    const [cities, setCities] = useState<DropdownOption[]>([]);

    const countryOptions: DropdownOption[] = Country.getAllCountries().map((country) => ({
        label: country.name,
        value: country.name,
    }));

    useEffect(() => {
        const selectedCountry = Country.getAllCountries().find(c => c.name === address.country);
        const statesList = selectedCountry
            ? State.getStatesOfCountry(selectedCountry.isoCode).map(state => ({
                label: state.name,
                value: state.name,
            }))
            : [];
        setStates(statesList);
        setCities([]);
        setAddress(prev => ({ ...prev, state: '', city: '' }));
    }, [address.country]);

    useEffect(() => {
        if (address.state && address.country) {
            const selectedCountry = Country.getAllCountries().find(c => c.name === address.country);
            const selectedState = selectedCountry
                ? State.getStatesOfCountry(selectedCountry.isoCode).find(s => s.name === address.state)
                : null;
            const citiesList = (selectedCountry && selectedState)
                ? City.getCitiesOfState(selectedCountry.isoCode, selectedState.isoCode).map(city => ({
                    label: city.name,
                    value: city.name,
                }))
                : [];
            setCities(citiesList);
            setAddress(prev => ({ ...prev, city: '' }));
        }
    }, [address.state, address.country]);

    const handlePresentChange = (field: string, value: string) => {
        setAddress(prev => ({ ...prev, [field]: value }));
    };

    return (
        <DashboardLayout>
            <div className="flex justify-between pt-10 pb-5">
                <div className="flex items-center justify-between w-full">
                    <Heading level="h1">Add University</Heading>
                    <div className="flex items-center gap-3.5">
                        <button className="py-2.5 px-[18px] bg-primaryColor text-white rounded-full flex items-center gap-1">
                            <Download />
                            Template
                        </button>
                        <button className="py-2.5 px-[18px] bg-primaryOne text-primaryColor border border-primaryColor/20 rounded-full flex items-center gap-1">
                            <Upload className="w-4 h-4" />
                            CSV
                        </button>
                    </div>
                </div>
            </div>
            <form action="" className="pb-10">
                <div className="p-[30px] bg-white rounded-[20px] space-y-[30px]">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <InputField
                            id="Name"
                            placeholder="Enter the university name"
                            type="text"
                            label="Name"
                            required
                            className="py-3"
                        />
                        <ImageUploader
                            label="University Logo"
                            multiple={false}
                            value={logo}
                            onChange={(file) => setLogo(file as File | null)}
                            className=""
                        />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="max-h-[137px]">
                            <NoteTextarea
                                label="Main Campus Address*"
                                placeholder="Address"
                            />
                        </div>

                        <div className="grid grid-cols-2 gap-x-6 gap-y-[30px]">
                            <SelectAndSearchCombobox
                                label="Country"
                                options={countryOptions}
                                className="py-3"
                                buttonClassName=" border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree"
                                selectedValue={address.country}
                                onChange={(val) =>
                                    handlePresentChange('country', val)
                                }
                            />
                            <SelectAndSearchCombobox
                                label="State"
                                options={states}
                                className="py-3"
                                buttonClassName=" border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree"
                                selectedValue={address.state}
                                onChange={(val) =>
                                    handlePresentChange('state', val)
                                }
                                disabled={!address.country}
                            />
                            <SelectAndSearchCombobox
                                label="City"
                                options={cities}
                                className="py-3"
                                buttonClassName=" border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree"
                                selectedValue={address.city}
                                onChange={(val) =>
                                    handlePresentChange('city', val)
                                }
                                disabled={!address.state}
                            />
                            <InputField
                                id="zip_code"
                                type="text"
                                label="Postal/Zip Code"
                                className="py-3"
                                value={address.zip}
                                onChange={(e) =>
                                    handlePresentChange('zip', e.target.value)
                                }
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-2 gap-6">
                        <PhoneNumberInput
                            label="Phone Number"
                            className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                        />
                        <InputFieldWithIcon
                            label="Email"
                            placeholder="Email"
                            icon={<EmailInbox />}
                            type="email"
                            className="mt-0.5"
                        />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="max-h-[137px]">
                            <NoteTextarea label="About" placeholder="About" />
                        </div>
                        <div className="grid grid-cols-2 gap-6">
                            <SelectField
                                label="Type"
                                options={UniTypeSelectButton}
                                className="py-6"
                            />
                            <InputField
                                id="website_url"
                                placeholder="Website link paste here"
                                type="text"
                                label="Website URL"
                            />
                            <InputField
                                id="founded_on"
                                type="text"
                                label="Founded On"
                            />
                            <InputField
                                id="institution_code"
                                type="text"
                                label="Institution Code"
                            />
                        </div>
                    </div>
                </div>
                <div className="flex justify-end w-full">
                    <button className="py-2.5 px-[18px] bg-tertiary text-white  rounded-full mt-12">
                        Submit
                    </button>
                </div>
            </form>
        </DashboardLayout>
    );
};

export default page;
