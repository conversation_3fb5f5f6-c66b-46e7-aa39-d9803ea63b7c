import React from 'react';
import canada from '@/app/assets/svg/canada';
import search from '@/app/assets/svg/search';
import Heading from '@/app/components/Heading';
import InputWithIcon from '@/app/components/InputWithIcon';
import DropDownButton from '@/app/components/DropDownButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const tableHeadData = [
        'UID',
        'University',
        'Location',
        'Country',
        'University Type',
        'Application Processing Time (days)'
    ];

    const applicationProcessingTime = [
        {
            uid: 262,
            university: 'Massachusetts Institute of Technology (MIT)',
            location: 'Cambridge, AL',
            country: canada,
            universityType: 'Private',
            apt: '100'
        }
    ];

    return (
        <DashboardLayout>
            <Heading level='h1'>
                Application Processing Time
            </Heading>
            <div className='flex justify-between items-center my-5'>
                <InputWithIcon 
                    icon={search} 
                    placeholder='search by id, name, mobile, email' 
                    className='font-normal text-xs leading-none text-grayTwo w-[300px] rounded-[50px] py-2.5  border-[0.5px] bg-white border-grayTwo' 
                />
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            {tableHeadData.map((thead, index) => (
                                <th 
                                    key={index}
                                    className={`${ index === 0 ? 'flex items-center gap-0.5 cursor-pointer': ''} py-3.5 px-6 font-bold text-xs tracking-[0.4px] text-grayFive`}
                                >
                                    {thead}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {applicationProcessingTime.map((apt, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{apt.uid}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{apt.university}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{apt.location}</td>
                                <td className='text-xs leading-5 text-graySix px-10 py-3.5'>{<apt.country />}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{apt.universityType}</td>
                                <td className='text-xs leading-5 text-graySix px-28 py-3.5 font-normal'>{apt.apt}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </DashboardLayout>
    )
}

export default page