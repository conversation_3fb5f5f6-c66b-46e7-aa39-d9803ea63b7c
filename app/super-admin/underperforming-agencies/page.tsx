'use client'

import { courses } from '@/common';
import React, { useState } from 'react';
import Search from '@/app/assets/svg/search';
import canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import SortIcon from '@/app/assets/svg/SortIcon';
import Pagination from '@/app/components/Pagination';
import InputWithIcon from '@/app/components/InputWithIcon';
import DropDownButton from '@/app/components/DropDownButton';
import VerticalThreeDots from '@/app/assets/svg/VerticalThreeDots';
import DashboardLayout from '@/app/components/layout/DashboardLayout'

const page = () => {
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };
    
    const tableHeadData = [
        'AID',
        'Agency',
        'Key Account Manager',
        'Email',
        'Contact',
        'Added on',
        'Country',
        'App. Volume',
        'Action'
    ];

    const underperformingAgencies = [
        {
            aid: 262,
            agency: 'FICC',
            keyAccountManager: 'Brandon Philips',
            email: '<EMAIL>',
            contact: '+8801962-446543',
            addedOn: '22 Oct 2023',
            country: canada,
            appVolume: '00'
        }
    ];

    return (
        <DashboardLayout>
            <Heading level='h1'>
                Underperforming Agencies
            </Heading>
            <div className='flex justify-between items-center my-5'>
                <InputWithIcon 
                    icon={Search} 
                    placeholder='search by id, name, mobile, email' 
                    className='font-normal text-xs leading-none text-grayTwo w-[300px] rounded-[50px] py-2.5  border-[0.5px] bg-white border-grayTwo' 
                />
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            {tableHeadData.map((thead, index) => (
                                <th 
                                    key={index}
                                    className={`${ index === 0 ? 'flex items-center gap-0.5 cursor-pointer': ''} py-3.5 px-6 font-bold text-xs tracking-[0.4px] text-grayFive`}
                                >
                                    {thead}
                                    {index === 0 && (
                                        <SortIcon />
                                    )}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {underperformingAgencies.map((agency, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{agency.aid}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{agency.agency}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{agency.keyAccountManager} </td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{agency.email}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{agency.contact}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{agency.addedOn}</td>
                                <td className='text-xs leading-5 text-graySix px-10 py-3.5'>{<agency.country />}</td>
                                <td className='text-xs leading-5 text-graySix px-14 py-3.5 font-normal'>{agency.appVolume}</td>
                                <td className='text-xs leading-5 text-graySix px-9 py-3.5 font-normal'>
                                    <VerticalThreeDots />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className='pb-24 pt-12'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default page