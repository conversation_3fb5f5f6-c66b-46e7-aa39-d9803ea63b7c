'use client';

import React from 'react';
import Heading from '@/app/components/Heading';
import { Button } from '@/components/ui/button';
import Location from '@/app/assets/svg/Location';
import IntakeIcon from '@/app/assets/svg/IntakeIcon';
import EditAction from '@/app/assets/svg/EditAction';
import ProgramIcon from '@/app/assets/svg/ProgramIcon';
import CommonSheet from '@/app/components/CommonSheet';
import PaymentIcon from '@/app/assets/svg/PaymentIcon';
import Payment from '@/app/components/ui/form/Payment';
import IntakeForm from '@/app/components/ui/form/IntakeForm';
import CommissionIcon from '@/app/assets/svg/CommissionIcon';
import NationalityIcon from '@/app/assets/svg/NationalityIcon';
import ProgramForm from '@/app/components/ui/form/ProgramForm';
import StandardizedIcon from '@/app/assets/svg/StandardizedIcon';
import FieldOfStudy from '@/app/components/ui/form/FieldOfStudy';
import CommissionForm from '@/app/components/ui/form/CommissionForm';
import { CircularProgress } from '@/app/components/CircularProgress';
import ContactDetailsIcon from '@/app/assets/svg/ContactDetailsIcon';
import ContactDetails from '@/app/components/ui/form/ContactDetails';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import AccordionWithButton from '@/app/components/AccordionWithButton';
import TheUniversityOfChicago from '@/app/assets/svg/TheUniversityOfChicago';
import EligibleNationalities from '@/app/components/ui/form/EligibleNationalities';
import UniEnrollementFeesForm from '@/app/components/ui/form/UniEnrollementFeesForm';
import UniEnrollmentCourseForm from '@/app/components/ui/form/UniEnrollmentCourseForm';
import UniEnrollmentAlumniForm from '@/app/components/ui/form/UniEnrollmentAlumniForm';
import UniEnrollmentGeneralForm from '@/app/components/ui/form/UniEnrollmentGeneralForm';
import UniEnrollementCampusForm from '@/app/components/ui/form/UniEnrollementCampusForm';
import UniEnrollementConfigurebyCourse from '@/app/components/ui/form/UniEnrollementConfigurebyCourse';



const sections = [
    { 
        name: 'Intake', 
        icon: <IntakeIcon />,
        content: <IntakeForm />
    },
    { 
        name: 'Program', 
        icon: <ProgramIcon />,
        content: <ProgramForm  />
    },
    { 
        name: 'Field of Study', 
        icon: <StandardizedIcon />,
        content: <FieldOfStudy />
    },
    { 
        name: 'Eligible Nationalities', 
        icon: <NationalityIcon />,
        content: <EligibleNationalities />
    },
    { 
        name: 'Commission', 
        icon: <CommissionIcon />,
        content: <CommissionForm />
    },
    { 
        name: 'Payment Settings', 
        icon: <PaymentIcon />,
        content: <Payment  />
    },
    { 
        name: 'Contact Details', 
        icon: <ContactDetailsIcon />,
        content: <ContactDetails  />
    }
];

const accordions = [
    {
        name: 'General',
        component: <UniEnrollmentGeneralForm />,
    },
    {
        name: 'Fees',
        component: <UniEnrollementFeesForm />
    },
    {
        name: 'Course',
        component: <UniEnrollmentCourseForm />
    },
    {
        name: 'Campus',
        component: <UniEnrollementCampusForm />
    },
    {
        name: 'Configure Campus by Course',
        component: <UniEnrollementConfigurebyCourse />
    },
    {
        name: 'Alumni',
        component: <UniEnrollmentAlumniForm />
    },
]

const page = () => {
    return (
        <DashboardLayout>
            <div className='pb-24 overflow-hidden'>
                <Heading level='h1'>
                    University Information
                </Heading>
                <div className='rounded-[16px] border-[0.5px] border-grayOne p-8 bg-white mt-5'>
                    <Heading className='font-semibold text-xl leading-none text-graySix mb-[30px]' level='h2'>
                        UID # 567
                    </Heading>
                    <div className='flex gap-3 items-center text-primaryColor border-b pb-3.5 border-primaryColor/20'>
                        General Info <Button className='px-2 py-1.5 border border-primaryColor/10 rounded-full text-xs gap-1'><EditAction /> Edit</Button>
                    </div>
                    <div className='flex gap-5 w-full mt-[30px]'>
                        <div>
                            <TheUniversityOfChicago />
                        </div>
                        <div className='flex flex-col gap-5 w-full'>
                            <div className='space-y-1.5'>
                                <Heading level='h3'>
                                    The University of Chicago
                                </Heading>
                                <span className='text-sm text-grayFive inline-flex items-center gap-1.5'>
                                    <Location className='w-4 h-4' />
                                    Office of Admissions and Records
                                    Division of Enrollment Management
                                    Student Services Building, 47SA
                                    9001 Stockdale Highway
                                    Bakersfield, CA 93311-1022 
                                </span>
                            </div>
                            <div className='grid grid-cols-4 gap-[30px]'>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Website</span>
                                    <span className='font-semibold text-sm leading-none underline text-primaryColor'>www.uchicago.edu</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Email</span>
                                    <span className='font-semibold text-sm leading-none underline text-primaryColor'><EMAIL></span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Join Date</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>22 Oct 2014</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Type</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>Public, Non-Profit</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Primary Contact Number</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>+44 305 1234 5678</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Founded On</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>1804</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Institution Code</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>O19209939282</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {accordions.map((content, index) => (
                    <div key={index} className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                        <AccordionWithButton
                            label={content.name}
                            labelClassName='text-2xl font-medium'
                        >
                            {content.component}
                        </AccordionWithButton>
                    </div>
                ))}
                <div className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                    <AccordionWithButton
                        label='Configure Advanced Setup'
                        labelClassName='text-2xl font-medium'
                        subLabel='Once you have set up these sections, you will be able to create an advanced university profile.'
                    >
                        <div className='grid grid-cols-4 gap-6'>
                            <div className='row-span-3 flex flex-col justify-center items-center text-[18px] gap-6'>
                                <CircularProgress
                                    value={40}
                                    size={120}
                                    strokeWidth={12}
                                    animationDuration={1500}
                                />
                                <p className='text-grayFive'>Overall Progress</p>
                            </div>
                            {sections.map(( item, index ) => (
                                <CommonSheet
                                    key={index}
                                    label={item.name}
                                    Icon={item.icon}
                                    title={item.name}
                                >
                                    {item.content}
                                </CommonSheet>
                            ))}

                        </div>
                    </AccordionWithButton>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page