import Ficc from '@/app/assets/svg/Ficc'
import AccordionWithButton from '@/app/components/AccordionWithButton'
import Heading from '@/app/components/Heading'
import DashboardLayout from '@/app/components/layout/DashboardLayout'
import AgencyEnrollmentBranchForm from '@/app/components/ui/form/AgencyEnrollmentBranchForm'
import AgencyEnrollmentContactForm from '@/app/components/ui/form/AgencyEnrollmentContactForm'
import React from 'react'

const page = () => {
    return (
        <DashboardLayout>
            <div className='pb-24'>
                <Heading level='h1'>
                    Agency Information
                </Heading>
                <div className='rounded-[16px] border-[0.5px] border-grayOne p-8 bg-white mt-5'>
                    <Heading className='font-semibold text-xl leading-none text-graySix' level='h2'>
                        AID # 567
                    </Heading>
                    <div className='flex gap-3 items-center text-primaryColor pb-3.5 border-b mt-[30px]'>
                        <span>General Info</span>
                        <button className='bg-primaryThree border border-primaryTwo px-2 py-1 rounded-full'>Edit</button>
                    </div>
                    <div className='flex gap-5 mt-[30px]'>
                        <div>
                            <Ficc />
                        </div>
                        <div className='flex flex-col'>
                            <Heading level='h3'>
                                Farees International Career Counseling (FICC)
                            </Heading>
                            <div className='grid grid-cols-5 gap-20 mt-4'>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Website</span>
                                    <span className='font-semibold text-sm leading-none underline text-primaryColor'>ficc.com.bd</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Email</span>
                                    <span className='font-semibold text-sm leading-none underline text-primaryColor'><EMAIL></span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Location</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>9th floor, 10/2, Gawsia Kashem Center, Motijheel, Dhaka.</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Join Date</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>22 Oct 2012</span>
                                </div>
                                <div className='flex flex-col gap-1.5'>
                                    <span className='font-normal text-sm leading-none text-grayFour'>Agency Size</span>
                                    <span className='font-semibold text-sm leading-none text-grayFour'>150-200 Employees</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                    <AccordionWithButton
                        label='Branch'
                        labelClassName='text-2xl font-medium'
                    >
                        <AgencyEnrollmentBranchForm />
                    </AccordionWithButton>
                </div>
                <div className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                    <AccordionWithButton
                        label='Contact'
                        labelClassName='text-2xl font-medium'
                    >
                        <AgencyEnrollmentContactForm />
                    </AccordionWithButton>
                </div>
                <div className='mt-5 rounded-[16px] border-[0.5px] border-grayOne bg-white'>
                    <AccordionWithButton
                        label='Commission'
                        labelClassName='text-2xl font-medium'
                    >
                        sdsfsdfsd
                    </AccordionWithButton>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page
