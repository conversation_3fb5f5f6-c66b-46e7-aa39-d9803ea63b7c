'use client';

import Upload from '@/app/assets/svg/upload';
import Heading from '@/app/components/Heading';
import Download from '@/app/assets/svg/download';
import DashboardLayout from '@/app/components/layout/DashboardLayout'
import React, { useState } from 'react'
import InputField from '@/app/components/InputField';
import ImageUploader from '@/app/components/ImageUploader';
import NoteTextarea from '@/app/components/NoteTextarea';
import SelectField from '@/app/components/SelectField';
import PhoneNumberInput from '@/app/components/PhoneNumberInput';
import InputFieldWithIcon from '@/app/components/InputFieldWithIcon';
import EmailInbox from '@/app/assets/svg/emailInbox';

export const DemoSelectButtonData = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
    { value: 'non-profit', label: 'Non-Profit' }
]


const page = () => {

    const [logo, setLogo] = useState<File | null>(null);
    
    return (
        <DashboardLayout>
            <div className="flex justify-between pt-10 pb-5">
                <div className='flex items-center justify-between w-full'>
                    <Heading level="h1">University Enrollment Form</Heading>
                    <div className='flex items-center gap-3.5'>
                        <button className='py-2.5 px-[18px] bg-primaryColor text-white rounded-full flex items-center gap-1'>
                            <Download />
                            Template
                        </button>
                        <button className='py-2.5 px-[18px] bg-primaryOne text-primaryColor border border-primaryColor/20 rounded-full flex items-center gap-1'>
                            <Upload className='w-4 h-4' />
                            CSV
                        </button>
                    </div>
                </div>
            </div>
            <form action="" className='mb-10'>
                <div className='p-[30px] bg-white rounded-[20px] space-y-[30px]'>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <InputField
                            id="agency_name"
                            placeholder="Enter the university name"
                            sublabel='(Official registered name)'
                            type="text"
                            label="Agency Name*"
                            required
                            className='py-3'
                        />
                        <ImageUploader
                            label="Agency Logo*"
                            multiple={false}
                            value={logo}
                            onChange={(file) => setLogo(file as File | null)}
                            className=''
                        />
                    </div>
                    <div className='grid grid-cols-4 gap-6'>
                        <div className='col-span-2'>
                            <InputField
                                id="address"
                                placeholder="Addresses"
                                sublabel='(Head Office Location)'
                                type="text"
                                label="Address*"
                                required
                                className='py-3'
                            />
                        </div>
                        <SelectField
                            label="Country*"
                            options={DemoSelectButtonData}
                        />
                        <SelectField
                            label="State"
                            options={DemoSelectButtonData}
                        />
                    </div>
                    <div className='grid grid-cols-4 gap-6'>
                        <div className='col-span-2'>
                            <InputField
                                id="agency_size"
                                placeholder="ex,. 200 employees"
                                type="text"
                                label="Agency Size*"
                                required
                                className='py-3'
                            />
                        </div>
                        <SelectField
                            label="City*"
                            options={DemoSelectButtonData}
                        />
                        <InputField
                            id="zip_code"
                            type="text"
                            label="Postal/Zip Code"
                            required
                            className='py-3'
                        />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className='max-h-[145px]'>
                            <NoteTextarea />
                        </div>
                        <div className='grid grid-cols-1 gap-6'>
                            <InputField
                                id="agency_registration_name"
                                placeholder="Enter the agency registration name"
                                type="text"
                                label="Agency Registration Number*"
                            />
                            <InputField
                                id="website_url"
                                placeholder="website link paste here"
                                type="text"
                                label="Website URL*"
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-2 gap-6">
                        <PhoneNumberInput
                            label="Primary Contact Number"
                            className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                        />
                        <InputFieldWithIcon
                            label="Email"
                            placeholder="Email"
                            icon={<EmailInbox />}
                            type="email"
                        />
                    </div>
                    
                </div>
                <div className='flex justify-end w-full'>
                    <button className='py-2.5 px-[18px] bg-tertiary text-white  rounded-full mt-12'>
                        Submit
                    </button>
                </div>
            </form>
        </DashboardLayout>
    )
}

export default page
