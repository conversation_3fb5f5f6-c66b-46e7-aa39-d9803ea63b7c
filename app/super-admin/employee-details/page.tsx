'use client';

import EditAction from '@/app/assets/svg/EditAction'
import Heading from '@/app/components/Heading'
import DashboardLayout from '@/app/components/layout/DashboardLayout'
import SelectField from '@/app/components/SelectField'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Image from 'next/image'
import React, { useState } from 'react'

const page = () => {
    // Add state for selected identity type
    const [identityType, setIdentityType] = useState('Passport');

    // Placeholder data for each identity type
    const identityInfo: Record<string, { label: string; value: string }[]> = {
        Passport: [
            { label: 'Passport Number', value: '********' },
            { label: 'Nationality', value: 'Bangladeshi' },
            { label: 'Issue Date', value: '2025-01-01' },
            { label: 'Expire Date', value: '2025-01-01' },
        ],
        NID: [
            { label: 'NID Number', value: '*********0' },
            { label: 'Nationality', value: 'Bangladeshi' },
            { label: 'Issue Date', value: '2015-01-01' },
        ],
        'Driving License': [
            { label: 'License Number', value: 'DL-987654' },
            { label: 'Nationality', value: 'Bangladeshi' },
            { label: 'Issue Date', value: '2020-01-01' },
            { label: 'Expire Date', value: '2030-01-01' },
        ],
    };

    return (
        <DashboardLayout>
            <div className='pb-24'>
                <div className='flex justify-between items-center'>
                    <Heading level='h1'>
                        Profile
                    </Heading>
                    <button className='bg-primaryColor text-white px-4 py-2 flex items-center gap-2 rounded-full text-sm font-medium'>
                        <EditAction />
                        Edit
                    </button>
                </div>
                <div className='grid grid-cols-3 gap-6 mt-5'>
                    <div className="col-span-2 bg-white rounded-[20px] flex items-center p-7 gap-7 border border-primaryFour">
                        <Image
                            src="https://images.pexels.com/photos/30446434/pexels-photo-30446434.jpeg"
                            alt="Jane Park"
                            width={120}
                            height={120}
                            className="rounded-full object-cover w-[120px] h-[120px]"
                        />
                        <div className="flex-1">
                            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Jane Park</h2>
                            <div className="grid grid-cols-4 gap-y-5 gap-x-2.5 text-sm">
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">ID</span>
                                    <div className="text-grayFive font-medium">6365</div>
                                </div>
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">Designation</span>
                                    <div className="text-grayFive font-medium">Admin</div>
                                </div>
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">Job Location</span>
                                    <div className="text-grayFive font-medium">Sylhet</div>
                                </div>
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">Department</span>
                                    <div className="text-grayFive font-medium">Application</div>
                                </div>
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">Supervisor</span>
                                    <div className="text-grayFive font-medium">Samuel Morgue</div>
                                </div>
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">Join Date</span>
                                    <div className="text-grayFive font-medium">22 Oct 2025</div>
                                </div>
                                <div className='grid grid-cols-2 gap-2.5'>
                                    <span className="text-grayFour">Email</span>
                                    <div className="text-grayFive font-medium"><EMAIL></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className='bg-white rounded-[20px] p-7 border border-primaryFour col-span-1 row-span-2'>
                        <Heading level='h3' className='text-grayFive text-lg font-bold'>
                            Personal Info
                        </Heading>
                        <Heading level='h3' className='!text-grayFour py-[30px] text-lg font-semibold'>
                            Primary Contact
                        </Heading>
                        <div className='grid grid-cols-1 gap-[22px]'>
                            <div className='flex justify-between'>
                                <p className='text-grayFour'>Name</p>
                                <p className='text-grayFive'>Emery Septimus</p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-grayFour'>Relationship</p>
                                <p className='text-grayFive'>Father</p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-grayFour'>Phone</p>
                                <p className='text-grayFive'>+8801717171717</p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-grayFour'>Email</p>
                                <p className='text-grayFive'><EMAIL></p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-grayFour'>Address</p>
                                <p className='text-grayFive'>123/A, Dhaka, Bangladesh</p>
                            </div>

                        </div>
                    </div>
                    <div className='bg-white rounded-[20px] p-7 border border-primaryFour col-span-2'>
                        <Heading level='h3' className='text-grayFive mb-3.5 text-lg font-bold'>
                            Personal Info
                        </Heading>
                        <div className='grid grid-cols-4 gap-2.5 text-sm'>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Gender</span>
                                <span className='text-grayFive font-medium'>Male</span>
                            </div>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Date of Birth</span>
                                <span className='text-grayFive font-medium'>12/02/1990</span>
                            </div>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Age</span>
                                <span className='text-grayFive font-medium'>25</span>
                            </div>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Blood Group</span>
                                <span className='text-grayFive font-medium'>B+</span>
                            </div>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Nationality</span>
                                <span className='text-grayFive font-medium'>Bangladeshi</span>
                            </div>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Contact</span>
                                <span className='text-grayFive font-medium'>+8801717171717</span>
                            </div>
                            <div className='flex flex-col gap-2.5'>
                                <span className='text-grayFour '>Address</span>
                                <span className='text-grayFive font-medium'>123/A, Dhaka, Bangladesh</span>
                            </div>
                        </div>
                    </div>
                    <div className='bg-white rounded-[20px] p-7 border border-primaryFour col-span-1'>
                        <div className='flex justify-between items-center'>
                            <Heading level='h3' className='text-grayFive text-lg font-bold'>
                                Identity Information
                            </Heading>
                            <SelectField
                                value={identityType}
                                onChange={setIdentityType}
                                options={[
                                    { value: 'NID', label: 'NID' },
                                    { value: 'Passport', label: 'Passport' },
                                    { value: 'Driving License', label: 'Driving License' },
                                ]}
                                className='text-primaryColor bg-primaryOne !py-1.5 !px-3 rounded-full text-xs !h-fit border-none focus-within:ring-0 focus-within:outline-none'
                            />
                        </div>
                        <div className='flex flex-col gap-[22px] mt-[30px]'>
                            {identityInfo[identityType]?.map((item) => (
                                <div key={item.label} className='grid grid-cols-2 gap-2.5 text-sm'>
                                    <div className='text-grayFour'>{item.label}</div>
                                    <div className='text-grayFive text-right'>{item.value}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className='bg-white rounded-[20px] p-7 border border-primaryFour col-span-1'>
                        <Heading level='h3' className='!text-grayFive text-lg font-bold pb-[30px]'>
                            Bank Account
                        </Heading>
                        <div className="grid grid-cols-2 gap-y-[22px] text-sm text-grayFour">
                            <div>Account Holder Name</div>
                            <div className="text-right text-graySix">Josh Septimus</div>
                            <div>Account Number</div>
                            <div className="text-right text-graySix">*********</div>
                            <div>Bank Name</div>
                            <div className="text-right text-graySix">AVB Bank</div>
                            <div>Branch Name</div>
                            <div className="text-right text-graySix">PYD Branch</div>
                        </div>
                    </div>
                    <div className='bg-white rounded-[20px] p-7 border border-primaryFour col-span-1'>
                        <Heading level='h3' className='!text-grayFive text-lg font-bold pb-[30px]'>
                            Social Profile
                        </Heading>
                        <div className="grid grid-cols-2 gap-y-[22px] text-sm text-grayFour">
                            <div>LinkedIn</div>
                            <div className="text-right text-graySix">Josh Septimus</div>
                            <div>Twitter</div>
                            <div className="text-right text-graySix">Josh Septimus</div>
                            <div>Facebook</div>
                            <div className="text-right text-graySix">Josh Septimus</div>
                            <div>WhatsApp</div>
                            <div className="text-right text-graySix">+991 563214548</div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page
