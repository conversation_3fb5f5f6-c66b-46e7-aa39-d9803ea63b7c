'use client';

import Image from 'next/image';
import { SaveIcon } from 'lucide-react';
import countries from 'world-countries';
import { DropdownOption } from '@/types';
import Heading from '@/app/components/Heading';
import { Switch } from '@/components/ui/switch';
import InputField from '@/app/components/InputField';
import AddPicture from '@/app/assets/svg/AddPicture';
import EmailInbox from '@/app/assets/svg/emailInbox';
import SelectField from '@/app/components/SelectField';
import { DatePicker } from '@/app/components/DatePicker';
import { Country, State, City } from 'country-state-city';
import React, { useEffect, useState, useRef } from 'react';
import Text<PERSON>reaField from '@/app/components/TextAreaField';
import FormSubSection from '@/app/components/FormSubSection';
import ButtonWithIcon from '@/app/components/ButtonWithIcon';
import PhoneNumberInput from '@/app/components/PhoneNumberInput';
import InputFieldWithIcon from '@/app/components/InputFieldWithIcon';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import SelectAndSearchCombobox from '@/app/components/SelectAndSearchCombobox';

const page = () => {
    const [presentAddress, setPresentAddress] = useState({
        address: '',
        country: '',
        state: '',
        city: '',
        zip: '',
    });
    const [permanentAddress, setPermanentAddress] = useState({
        address: '',
        country: '',
        state: '',
        city: '',
        zip: '',
    });
    const [sameAsPresent, setSameAsPresent] = useState(false);
    const [presentStates, setPresentStates] = useState<DropdownOption[]>([]);
    const [presentCities, setPresentCities] = useState<DropdownOption[]>([]);
    const [permanentStates, setPermanentStates] = useState<DropdownOption[]>([]);
    const [permanentCities, setPermanentCities] = useState<DropdownOption[]>([]);

    const [profileImage, setProfileImage] = useState<string | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);
    const [isLocalImage, setIsLocalImage] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const countryOptions: DropdownOption[] = countries.map((country) => ({
        label: country.name.common,
        value: country.cca2,
    }));

    useEffect(() => {
        if (sameAsPresent) {
            setPermanentAddress({ ...presentAddress });
            setPermanentStates(presentStates);
            setPermanentCities(presentCities);
        }
    }, [sameAsPresent, presentAddress, presentStates, presentCities]);

    useEffect(() => {
        if (presentAddress.country) {
            const states = State.getStatesOfCountry(presentAddress.country).map(state => ({
                label: state.name,
                value: state.isoCode,
            }));
            setPresentStates(states);
            setPresentCities([]);
            setPresentAddress(prev => ({ ...prev, state: '', city: '' }));
        }
    }, [presentAddress.country]);

    useEffect(() => {
        if (presentAddress.state) {
            const cities = City.getCitiesOfState(presentAddress.country, presentAddress.state).map(city => ({
                label: city.name,
                value: city.name,
            }));
            setPresentCities(cities);
            setPresentAddress(prev => ({ ...prev, city: '' }));
        }
    }, [presentAddress.state, presentAddress.country]);

    useEffect(() => {
        if (!sameAsPresent && permanentAddress.country) {
            const states = State.getStatesOfCountry(permanentAddress.country).map(state => ({
                label: state.name,
                value: state.isoCode,
            }));
            setPermanentStates(states);
            setPermanentCities([]);
            setPermanentAddress(prev => ({ ...prev, state: '', city: '' }));
        }
    }, [permanentAddress.country, sameAsPresent]);

    useEffect(() => {
        if (!sameAsPresent && permanentAddress.state) {
            const cities = City.getCitiesOfState(permanentAddress.country, permanentAddress.state).map(city => ({
                label: city.name,
                value: city.name,
            }));
            setPermanentCities(cities);
            setPermanentAddress(prev => ({ ...prev, city: '' }));
        }
    }, [permanentAddress.state, permanentAddress.country, sameAsPresent]);

    useEffect(() => {
        return () => {
            if (profileImage && profileImage.startsWith('blob:')) {
                URL.revokeObjectURL(profileImage);
            }
        };
    }, [profileImage]);

    const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            setUploadError('Please select a valid image file');
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            setUploadError('Image size should be less than 5MB');
            return;
        }

        setIsUploading(true);
        setUploadError(null);

        try {
            const previewUrl = URL.createObjectURL(file);
            setProfileImage(previewUrl);
            setIsLocalImage(true);

            mockUpload(file, previewUrl);

        } catch (error) {
            console.error('Image processing failed:', error);
            setUploadError('Failed to process image. Please try again.');
            setProfileImage(null);
            setIsUploading(false);
        }

        e.target.value = '';
    };

    const triggerFileInput = () => {
        fileInputRef.current?.click();
    };

    const removeProfileImage = () => {
        if (profileImage && profileImage.startsWith('blob:')) {
            URL.revokeObjectURL(profileImage);
        }
        setProfileImage(null);
        setUploadError(null);
        setIsLocalImage(false);
        setUploadProgress(0);
    };

    const mockUpload = (file: File, previewUrl: string) => {
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 100) {
                progress = 100;
                clearInterval(progressInterval);
                
                setTimeout(() => {
                    setProfileImage(previewUrl);
                    setIsLocalImage(false);
                    setIsUploading(false);
                    setUploadProgress(0);

                }, 500);
            }
            setUploadProgress(progress);
        }, 200);
    };

    const handlePresentChange = (field: string, value: string) => {
        setPresentAddress((prev) => ({ ...prev, [field]: value }));
    };
    const handlePermanentChange = (field: string, value: string) => {
        setPermanentAddress((prev) => ({ ...prev, [field]: value }));
    };


    return (
        <DashboardLayout>
            <div className='pb-24 flex flex-col gap-5'>
                <Heading level='h1' >
                    Add Employee
                </Heading>
                <form action="" className='bg-white rounded-[20px] p-[30px] space-y-[30px]'>
                    <FormSubSection heading={'Personal'} >
                        <div className='w-full flex gap-6'>
                            <div className='flex flex-col items-center gap-3'>
                                <div className='relative'>
                                    <div 
                                        className={`w-[120px] h-[120px] rounded-full flex items-center justify-center cursor-pointer transition-all duration-200 ${
                                            profileImage 
                                                ? 'bg-white border-2 border-primaryColor' 
                                                : 'bg-primaryOne hover:bg-primaryOne/80'
                                        }`}
                                        onClick={triggerFileInput}
                                    >
                                        {profileImage ? (
                                            <div className='w-[116px] h-[116px] overflow-hidden rounded-full'>
                                                <Image
                                                    src={profileImage}
                                                    alt="Profile"
                                                    width={116}
                                                    height={116}
                                                    className="rounded-full object-cover"
                                                />
                                            </div>
                                        ) : (
                                            <AddPicture />
                                        )}
                                    </div>
                                    
                                    {!profileImage && (
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            <div className="bg-black/50 rounded-full p-2">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12 5V19M5 12H19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                </svg>
                                            </div>
                                        </div>
                                    )}
                                    
                                    {profileImage && (
                                        <button
                                            onClick={removeProfileImage}
                                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors"
                                            type="button"
                                        >
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </button>
                                    )}

                                    {isUploading && (
                                        <div className="absolute inset-0 bg-black/20 rounded-full flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                                        </div>
                                    )}
                                    
                                    {isUploading && uploadProgress > 0 && (
                                        <div className="absolute -bottom-2 left-0 right-0">
                                            <div className="w-full bg-gray-200 rounded-full h-1">
                                                <div 
                                                    className="bg-primaryColor h-1 rounded-full transition-all duration-200"
                                                    style={{ width: `${uploadProgress}%` }}
                                                ></div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                
                                <button
                                    type="button"
                                    onClick={triggerFileInput}
                                    disabled={isUploading}
                                    className="text-primaryColor text-sm font-medium hover:text-primaryColor/80 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {profileImage ? 'Change Photo' : 'Upload Photo'}
                                </button>
                                
                                <p className="text-gray-500 text-xs text-center max-w-[120px]">
                                    {isUploading 
                                        ? 'Uploading...' 
                                        : isLocalImage 
                                            ? 'Image saved locally' 
                                            : profileImage 
                                                ? 'Image uploaded' 
                                                : 'JPG, PNG, GIF up to 5MB'
                                    }
                                </p>

                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageChange}
                                    className="hidden"
                                    disabled={isUploading}
                                />
                            </div>
                            <div className='grid grid-cols-2 gap-x-6 gap-y-[30px] w-full'>
                                <InputField
                                    id='last_name'
                                    placeholder='Last Name'
                                    type='text'
                                    label='Last Name / Surname'
                                    className='py-3'
                                />
                                <DatePicker
                                    title='Joining Date'
                                />
                                <InputField
                                    id='first_name'
                                    placeholder='First Name'
                                    type='text'
                                    label='First Name / Given Name'
                                    className='py-3'
                                />
                                <div className='grid grid-cols-2 gap-x-6 gap-y-[30px]'>
                                <SelectField
                                    label='Job Type'
                                    options={[
                                        { value: 'Contractual', label: 'Contractual' },
                                        { value: 'Permanent', label: 'Permanent' },
                                        { value: 'Probationary', label: 'Probationary' },
                                        { value: 'Suspended', label: 'Suspended' },
                                        { value: 'Trainee', label: 'Trainee' },
                                        { value: 'Intern', label: 'Intern' },
                                    ]}
                                    className='py-3'
                                />
                                <SelectField
                                    label='Job Status'
                                    options={[
                                        { value: 'Active', label: 'Active' },
                                        { value: 'Confirmed', label: 'Confirmed' },
                                        { value: 'Dismissed', label: 'Dismissed' },
                                        { value: 'Termination of Contract', label: 'Termination of Contract' },
                                        { value: 'Released', label: 'Released' },
                                        { value: 'Retirement', label: 'Retirement' },
                                        { value: 'Non Confirm', label: 'Non Confirm' },
                                        { value: 'Not In Service', label: 'Not In Service' },
                                        { value: 'Termination', label: 'Termination' },
                                        { value: 'Expired', label: 'Expired' }
                                    ]}
                                        className='py-3'
                                    />
                                </div>
                            </div>
                        </div>
                        <div className='grid grid-cols-4 gap-x-6 gap-y-[30px]'>
                            <SelectAndSearchCombobox
                                label='Nationality'
                                options={countryOptions}
                                className='py-6'
                                buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                            />
                            <DatePicker
                                label='Date of Birth'
                                className='py-3'
                            />
                            <SelectField
                                label='Blood Group'
                                options={[
                                    { value: 'A+', label: 'A+' },
                                    { value: 'A-', label: 'A-' },
                                    { value: 'B+', label: 'B+' },
                                    { value: 'B-', label: 'B-' },
                                    { value: 'AB+', label: 'AB+' },
                                    { value: 'AB-', label: 'AB-' },
                                    { value: 'O+', label: 'O+' },
                                    { value: 'O-', label: 'O-' },
                                ]}
                                className='py-6'
                            />
                            <div className='flex flex-col gap-2'>
                                <label className="font-medium text-sm text-grayFive">Gender</label>
                                <RadioGroup >
                                    <div className='flex gap-[21px] py-3'>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="male" id="male" />
                                            <label htmlFor="male" className="text-sm text-grayFive cursor-pointer">Male</label>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="female" id="female" />
                                            <label htmlFor="female" className="text-sm text-grayFive cursor-pointer">Female</label>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="other" id="other" />
                                            <label htmlFor="other" className="text-sm text-grayFive cursor-pointer">Other</label>
                                        </div>
                                    </div>
                                </RadioGroup>
                            </div>
                            <PhoneNumberInput
                                label='Phone Number'
                                className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                            />
                            <InputFieldWithIcon
                                id='email'
                                type='email'
                                label='Email'
                                className='py-3 mt-0.5' 
                                icon={<EmailInbox />}
                            />
                            
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Address'} >
                        <div className='grid grid-cols-2 gap-x-6 '>
                            <div className='grid gird-col-1 gap-y-[30px] border border-tertiary/20 rounded-[10px] p-5'>
                                <TextAreaField
                                    label='Present Address'
                                    placeholder='Enter Address'
                                    rows={0}
                                    className='h-fit'
                                    value={presentAddress.address}
                                    onChange={e => handlePresentChange('address', e.target.value)}
                                />
                                <div className='grid grid-cols-2 gap-x-6 gap-y-[30px]'>
                                    <SelectAndSearchCombobox
                                        label='Country'
                                        options={countryOptions}
                                        className='py-3'
                                        buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={presentAddress.country}
                                        onChange={val => handlePresentChange('country', val)}
                                    />
                                    <SelectAndSearchCombobox
                                        label='State'
                                        options={presentStates}
                                        className='py-3'
                                        buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={presentAddress.state}
                                        onChange={val => handlePresentChange('state', val)}
                                        disabled={!presentAddress.country}
                                    />
                                    <SelectAndSearchCombobox
                                        label='City'
                                        options={presentCities}
                                        className='py-3'
                                        buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={presentAddress.city}
                                        onChange={val => handlePresentChange('city', val)}
                                        disabled={!presentAddress.state}
                                    />
                                    <InputField
                                        id='zip_code'
                                        type='text'
                                        label='Postal/Zip Code'
                                        className='py-3'
                                        value={presentAddress.zip}
                                        onChange={e => handlePresentChange('zip', e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className='grid gird-col-1 gap-y-[30px] bg-primaryOne rounded-[10px] p-5'>
                                <div className='relative'>
                                    <div className='flex items-center gap-2 absolute top-0 right-0'>
                                        <label htmlFor='same_as_present' className='text-xs text-primaryColor cursor-pointer'>*Same as Present Address</label>
                                        <Switch
                                            id='same_as_present'
                                            className='border border-grayTwo h-4 w-7'
                                            classes='bg-grayTwo h-3 w-3 data-[state=checked]:translate-x-3'
                                            checked={sameAsPresent}
                                            onCheckedChange={setSameAsPresent}
                                        />
                                    </div>
                                    <TextAreaField
                                        label='Permanent Address'
                                        placeholder='Enter Address'
                                        rows={0}
                                        className='h-fit'
                                        value={permanentAddress.address}
                                        onChange={e => handlePermanentChange('address', e.target.value)}
                                        disabled={sameAsPresent}
                                    />
                                </div>
                                <div className='grid grid-cols-2 gap-x-6 gap-y-[30px]'>
                                    <SelectAndSearchCombobox
                                        label='Country'
                                        options={countryOptions}
                                        className='py-3'
                                        buttonClassName=' bg-primaryOne border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={permanentAddress.country}
                                        onChange={val => handlePermanentChange('country', val)}
                                        disabled={sameAsPresent}
                                    />
                                    <SelectAndSearchCombobox
                                        label='State'
                                        options={permanentStates}
                                        className='py-3'
                                        buttonClassName=' bg-primaryOne border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={permanentAddress.state}
                                        onChange={val => handlePermanentChange('state', val)}
                                        disabled={sameAsPresent || !permanentAddress.country}
                                    />
                                    <SelectAndSearchCombobox
                                        label='City'
                                        options={permanentCities}
                                        className='py-3'
                                        buttonClassName=' bg-primaryOne border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={permanentAddress.city}
                                        onChange={val => handlePermanentChange('city', val)}
                                        disabled={sameAsPresent || !permanentAddress.state}
                                    />
                                    <InputField
                                        id='zip_code'
                                        type='text'
                                        label='Postal/Zip Code'
                                        className='py-3 bg-primaryOne'
                                        value={permanentAddress.zip}
                                        onChange={e => handlePermanentChange('zip', e.target.value)}
                                        disabled={sameAsPresent}
                                    />
                                </div>
                            </div>
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Department Placement'} >
                        <div className='grid grid-cols-4 gap-x-6 gap-y-[30px]'>
                            <SelectField
                                label='Department'
                                options={[
                                    { value: 'Department 1', label: 'Department 1' },
                                    { value: 'Department 2', label: 'Department 2' },
                                    { value: 'Department 3', label: 'Department 3' },
                                ]}
                                className='py-6'
                            />
                            <SelectAndSearchCombobox
                                label='Supervisor'
                                options={[
                                    { value: 'Supervisor 1', label: 'Supervisor 1' },
                                    { value: 'Supervisor 2', label: 'Supervisor 2' },
                                    { value: 'Supervisor 3', label: 'Supervisor 3' },
                                ]}
                                className='py-6'
                                buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                            />
                            <SelectField
                                label='Job Position'
                                options={[
                                    { value: 'Department 1', label: 'Department 1' },
                                    { value: 'Department 2', label: 'Department 2' },
                                    { value: 'Department 3', label: 'Department 3' },
                                ]}
                                className='py-6'
                            />
                            <SelectAndSearchCombobox
                                label='Job Location'
                                options={[
                                    { value: 'Location 1', label: 'Location 1' },
                                    { value: 'Location 2', label: 'Location 2' },
                                    { value: 'Location 3', label: 'Location 3' },
                                ]}
                                className='py-6'
                                buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading='Emergency Contact'>
                        <div className='grid grid-cols-3 gap-x-6 gap-y-[30px]'>
                            <InputField
                                id='category_of_contact'
                                type='text'
                                label='Category of Contact'
                                className='py-3'
                                disabled={true}
                                value='Primary Contact'
                            />
                            <InputField
                                id='name_of_contact'
                                type='text'
                                label='Name'
                                className='py-3'
                            />
                            <InputField
                                id='relationship_with_contact'
                                type='text'
                                label='Relationship'
                                className='py-3'
                            />
                            <InputField
                                id='address'
                                type='text'
                                label='Address'
                                className='py-3'
                            />
                            <PhoneNumberInput
                                label='Phone Number'
                                className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                            />
                            <InputFieldWithIcon
                                id='email'
                                type='email'
                                label='Email'
                                className='py-3'
                                icon={<EmailInbox />}
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading='Identity Info'>
                        <div className='grid grid-cols-4 gap-x-6 gap-y-[30px]'>
                            <SelectField
                                label='Type'
                                className='py-6'
                                options={[
                                    { value: 'Passport', label: 'Passport' },
                                    { value: 'National ID', label: 'National ID' },
                                    { value: 'Driver License', label: 'Driver License' },
                                ]}
                            />
                            <SelectAndSearchCombobox
                                label='Nationality'
                                options={[
                                    { value: 'United States', label: 'United States' },
                                    { value: 'Canada', label: 'Canada' },
                                    { value: 'United Kingdom', label: 'United Kingdom' },
                                    { value: 'Australia', label: 'Australia' },
                                    { value: 'New Zealand', label: 'New Zealand' },
                                ]}
                                className='py-6'
                                buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                            />
                            <DatePicker
                                label='Issue Date'
                                className='py-3'
                            />
                            <DatePicker
                                label='Expiry Date'
                                className='py-3'
                            />
                            
                        </div>
                    </FormSubSection>
                    <FormSubSection heading='Bank Account'>
                        <div className='grid grid-cols-4 gap-x-6 gap-y-[30px]'>
                            <InputField
                                id='account_holder_name'
                                type='text'
                                label='Account Holder Name'
                                className='py-3'
                                placeholder='A/C Holder Name'
                            />
                            <InputField
                                id='account_number'
                                type='text'
                                label='Account Number'
                                className='py-3'
                                placeholder='Enter Account Number'
                            />
                            <InputField
                                id='bank_name'
                                type='text'
                                label='Bank Name'
                                className='py-3'
                                placeholder='Enter Bank Name'
                            />
                            <InputField
                                id='branch_name'
                                type='text'
                                label='Branch Name'
                                className='py-3'
                                placeholder='Enter Branch Name'
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading='Social Profile'>
                        <div className='grid grid-cols-2 gap-x-6 gap-y-[30px]'>
                            <SelectField
                                label='Social Media'
                                options={[
                                    { value: 'Facebook', label: 'Facebook' },
                                    { value: 'Twitter', label: 'Twitter' },
                                    { value: 'Instagram', label: 'Instagram' },
                                ]}
                                className='py-6'
                            />
                            <InputField
                                id='social_link'
                                type='text'
                                label='Link'
                                className='py-3'
                            />
                        </div>
                    </FormSubSection>
                    <div className='flex justify-end'>
                        <ButtonWithIcon
                            icon={<SaveIcon />}
                            label='Save'
                            className='bg-primaryColor text-white rounded-full'
                        />
                    </div>
                </form>
            </div>
        </DashboardLayout>
    )
}

export default page
