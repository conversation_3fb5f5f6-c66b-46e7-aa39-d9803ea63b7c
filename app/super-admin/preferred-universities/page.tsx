'use client'

import { courses } from '@/common';
import React, {useState} from 'react';
import canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import { UniTypeSelectButton } from '@/common';
import InputField from '@/app/components/InputField';
import Pagination from '@/app/components/Pagination';
import SelectField from '@/app/components/SelectField';
import DropDownButton from '@/app/components/DropDownButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };
        
    const tableHeadData = [
        'UID',
        'University',
        'Location',
        'Country',
        'University Type',
        'Applications Number'
    ];

    const universities= [
        {
            uid: 262,
            university: 'Massachusetts Institute of Technology (MIT)',
            location: 'Cambridge, AL',
            country: canada,
            universityType: 'Private',
            applicationsNumber: '1234'
        }
    ];

    return (
        <DashboardLayout>
            <Heading level='h1'>
                Preferred Universities
            </Heading>
            <div className='grid grid-cols-4 gap-3 py-5'>
                <InputField 
                    id='first_name'
                    placeholder='345689' 
                    type='text' 
                    // register={register('first_name', { required: 'First name is required' })}
                    label='UID' 
                    className='border-grayOne border-opacity-none'
                />
                <SelectField
                    label='Type'
                    options={UniTypeSelectButton}
                />
                 <SelectField
                    label='Country'
                    options={UniTypeSelectButton}
                />
                 <SelectField
                    label='Sort By'
                    options={UniTypeSelectButton}
                />
            </div>
            <div className='flex justify-between items-center py-5'>
                <Heading level='h2'>
                    Universities (32)
                </Heading>
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            {tableHeadData.map((thead, index) => (
                                <th 
                                    key={index}
                                    className={`${ index === 0 ? 'flex items-center gap-0.5 cursor-pointer': ''} py-3.5 px-6 font-bold text-xs tracking-[0.4px] text-grayFive`}
                                >
                                    {thead}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {universities.map((university, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{university.uid}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{university.university}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{university.location} </td>
                                <td className='text-xs leading-5 text-graySix px-9 py-3.5'>{<university.country />}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{university.universityType}</td>
                                <td className='text-xs leading-5 text-graySix px-16 py-3.5 font-normal'>{university.applicationsNumber}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className='pb-24 pt-12'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default page