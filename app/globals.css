@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

/* Ensure all containers respect max width */
.container, .w-full {
  max-width: 100%;
}

/* Fix for phone input components */
.PhoneInput {
  max-width: 100% !important;
  overflow: hidden;
}

.PhoneInput input {
  max-width: 100% !important;
}

/* Fix for dashboard layout sidebar issues */
.dashboard-layout {
  position: relative;
  overflow: hidden;
}

/* body {
    font-family: Arial, Helvetica, sans-serif;
} */

.swiper-pagination {
  @apply flex items-center justify-center flex-wrap gap-4 !relative;
}

.slider-pagination__item {
  @apply border border-grayTwo w-2.5 h-2.5 rounded-full  transition-all duration-300 ease-out cursor-pointer;
}

.slider-pagination__item.active {
  @apply w-[30px] opacity-100 bg-[#1E62E0] rounded-[10px] border-none;
}

.unislider .slider-pagination__item {
  margin-top: 30px;
}

/* Scale up the active slide */
.swiper-slide-active .swiper-slide-image {
  /* max-width: 480px; */
  /* height: 240px; */ 
  /* max-height: 240px; */
  transition: transform 0.3s ease; /* Smooth transition */
  z-index: 1; /* Ensure the active slide is above others */
}

/* Optional: Add a slight scale-down effect for non-active slides */
.swiper-slide:not(.swiper-slide-active) .swiper-slide-image {
  transition: transform 0.3s ease; /* Smooth transition */
  /* max-width: 360px; */
  /* max-height: 300px; */
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;
        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}


.PhoneInputCountryIconImg {
    object-fit: cover;
}
.PhoneInputCountryIcon {
    box-shadow: none !important;
    height: 24px !important;
    width: 24px !important;
    border-radius: 100%;
    overflow: hidden;
}

.tiptap {
    :first-child {
        margin-top: 0;
    }

    /* List styles */
    ul,
    ol {
        padding: 0 1rem;
        margin: 20px 1rem 20px 0.4rem;

        li p {
            margin-top: 0.25em;
            margin-bottom: 0.25em;
        }
    }
    blockquote {
        /* border-left: 3px solid rebeccapurple; */
        margin: 1.5rem 0;
        padding-left: 1rem;
    }

    /* Code and preformatted text styles */
  code {
    background-color: var(--purple-light);
    border-radius: 0.4rem;
    color: var(--black);
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }

  /* Link styles */
}

.tiptap p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

@layer base {
  /* .font-outline-2 {
      -webkit-text-stroke: 2px ;
  } */
  .font-outline-4 {
      -webkit-text-stroke-width: 4px ;
      paint-order: stroke fill;
  }
  /* .font-outline-color-1 {
      -webkit-text-stroke-color: #78A1EC ;
  }
  .font-outline-color-2 {
      -webkit-text-stroke-color: #4B81E6 ;
  }
  .font-outline-color-3 {
      -webkit-text-stroke-color: #1E62E0 ;
  } */
  .font-outline-color-4 {
      -webkit-text-stroke-color: #1E62E0 ;
  }
}