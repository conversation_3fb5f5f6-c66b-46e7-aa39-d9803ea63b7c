// import { StatusProps } from './types';
import { ElementType, ReactNode } from 'react';
import { StaticImageData } from 'next/image';
import { UseFormRegister, UseFormSetValue, UseFormGetValues } from 'react-hook-form';

export interface RegisterInput {
    name: string;
    email: string;
    phone?: string;
    password: string;
    roleName: string;
    nationality?: string;
    organizationName?: string;
}

export interface ForgetPasswordInput {
    email: string;
};

export interface ForgetPasswordResponse {
    success: boolean;
    message: string;
};

export interface ResetPasswordInput {
    email: string;
    newPassword: string;
    confirmPassword: string;
    resetToken: string;
};

export interface ResetPasswordResponse {
    success: boolean;
    message: string;
};

export interface LoginInput {
    email: string;
    password: string;
    ipAddress?: string;
    userAgent?: string;
}

export interface LoginResponse {
    success: boolean;
    message: string;
    accessToken?: string;
    refreshToken?: string;
    user?: {
        id: number | string;
        email: string;
        name?: string;
        roles?: Array<{
            id: number;
            name: string;
            actions?: Array<{
                id: number;
                name: string;
                featureId: number;
                feature: {
                    id: number;
                    name: string;
                }
            }>
        }>;
        [key: string]: any;
    };
}
export interface SendOTPInput {
    email: string;
    type?: 'login' | 'register' | 'forgot-password';
}

export interface VerifyOTPInput {
    email: string;
    otp: string;
    type?: 'login' | 'register' | 'forgot-password';
}

export interface OTPResponse {
    success: boolean;
    message: string;
    token?: string;
    user?: {
        id: string;
        email: string;
        name: string;
    };
    accessToken?: string;
    refreshToken?: string;
}

export interface SSOLoginProps {
    provider: string;
    idToken: string; // Google idToken
    email: string;
    name: string;
    ipAddress?: string;
    userAgent?: string;
}

export type DashboardLayoutProps = {
    children: React.ReactNode;
}

export type AuthLayoutProps = {
    imageSrc:  React.ReactNode ;
    // description: string;
    // title: string;
    // heading: string;
    // children: React.ReactNode;
    title: string;
    heading: string;
    // imageSrc: string;
    children: React.ReactNode;
    description: string;
    successIcon?: React.ReactNode;
}

export interface SectionLayoutProps {
    children: React.ReactNode;
    heading?: string;
    className?: string;
}

export interface StudentsDocumentsLayoutProps {
    sectionTitle: string;
    children: React.ReactNode;
    className?: string;
}

export interface ApplicationCardProps {
    name: string;
    email: string;
    university: string;
    program: string;
    level: string;
    requiredLevel: string;
    applicationId: string;
    deliveryMethod: string;
    intakes: {
        esl: string | null;
        academic: string;
        sessionend: string | null;
    };
    submissionDeadline: string;
    paymentStatus: string;
    paymentAmount: string;
}

export interface AddNewFieldButtonProps {
    onClick: React.MouseEventHandler<HTMLButtonElement>;
    title?: string;
}

export interface EditButtonProps{
    className?: string;
    onClick?: () => void;
}

export interface NotificationsProps {
    title: string;
    description: string;
    onClick?: () => void;
}

export interface EmailInputProps {
    id?: string;
    value?: string;
    type?: string;
    label?: string;
    className?: string;
    placeholder?: string;
    icon: React.ReactNode;
    register?: ReturnType<UseFormRegister<any>>;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    inputClass?: string;
    readonly?: boolean;
    error?: string;
}

export interface ChatSectionProps {
    setChat: (value: boolean) => void;
    selectedDept: string;
}

export interface TabProps {
    id: number;
    label: string;
}

export interface SwitcherProps {
    label?: string;
    classes?: string;
    className?: string;
    isChecked?: boolean;
    handleToggle?: (checked: boolean) => void;
}

export interface SelectFieldProps {
    label?: string;
    options: { value: string; label: string }[];
    placeholder?: string;
    value?: string;
    onChange?: (value: string) => void;
    className?: string;
    defaultValue?: string;
    sublabel?: string;
}

export interface SelectedItemsProps {
    subjects: { value: string; label: string }[];
    selectedOptions: string[];
    removeOption: (option: string) => void;
}
interface OptionProps {
    value: string;
    label: string;
}
export interface SelectAndSearchComboboxProps {
    options: OptionProps[];
    placeholder?: string;
    selectedValue?: string;
    onChange?: (value: string) => void;
    className?: string;
    buttonClassName?: string;
    width?: string;
    type?: string;
    label?: string;
    disabled?: boolean;
}

type IconType = string | React.ElementType;
export interface NavItemProps {
    isExpanded?: boolean;
    icon: IconType;
    label: string;
    toggleSidebar?: any;
    href: string;
}

export interface NavProps {
    isExpanded: boolean, 
    toggleSidebar: any
} 

export interface MultiSelectProps {
    options: { value: string; label: string }[];
    selectedValues: string[];
    onChange: (selectedValues: string[]) => void;
    placeholder?: string;
    label?: string
}

export type InputWithIconProps = {
    icon: IconType
    placeholder?: string;
    type?: string;
    className: string;
    value?: string;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface InputFieldProps{
    label?: string;
    sublabel?: string;
    value?: string;
    id: string;
    type?: 'text' | 'email' | 'password' | 'number'| 'date';
    placeholder?: string;
    required?: boolean;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    disabled?: boolean;
    errorMessage?: string;
    switcher?: boolean;
    className?: string
    register?: ReturnType<UseFormRegister<any>>;
    fieldNote?: string;
}
export interface TextareaProps {
    register?: ReturnType<UseFormRegister<any>>;
    label?: string;
    placeholder?: string;
    rows?: number;
    className?: string;
    value?: string;
    onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
    disabled?: boolean;
}

interface AcademicItem {
    label: string;
    value: string;
}
  
interface AcademicData {
    title?: string
    degree?: string;
    data?: AcademicItem[];
    info?: string[];
}
  
export interface InformationBoxProps {
    data: AcademicData;
}

interface AcademicField {
    id: string;
    label: string;
    files: File[];
}

export interface FileUploadSectionProps {
    fields: AcademicField[];
    setFields: React.Dispatch<React.SetStateAction<AcademicField[]>>;
}

export interface AcademicFieldProps {
    id: string;
    label: string;
    files: File[];
}

export interface ProficiencyProps {
    name: string;
    files?: string[];
}

export interface FiltersProps {
    programs: { value: string; label: string }[];
    intakes: { value: string; label: string }[];
    countries: { value: string; label: string }[];
    tuitionFees: string;
    sortByOptions: { value: string; label: string }[];
    onFilterChange: (filters: any) => void;
    searchCourse?: boolean;
    className?: string;
}
interface coursesProps {
    courseTitle: string;
    slug: string;
}

interface University {
    universityName: string;
    slug?: string;
    universityLogo?: StaticImageData;
    location: string;
    country: { label: string; logo: string | React.ElementType };
    province: string;
    city: string;
    type: string;
    tuitionFees: string;
    rank: number;
    courses?: coursesProps[];
  }
  
export interface UniversityTableProps {
    universityList: University[];
    isChecked?: boolean;
}

export interface CourseCardProps {
    course: {
        id: number; 
        university: string;
        slug: string;
        program: string;
        tuitionFees: [number, number];
        courseRank: number;
        acceptanceRate: string;
        programType: string;
        intake: string;
        country: string;
        scholarship: string;
        universityLogo: StaticImageData;
        countryLogo: StaticImageData;
    };
        isSelected: boolean;
        onSelect: (id: number) => void; 
        isChecked?: boolean;
}

export interface CompareButtonProps {
    selectedCourseNumber: number
}

interface Option {
    id: string;
    label: string;
}
export interface CheckboxProps {
    options: Option[];
    value?: string[];
    onChange?: (selectedIds: string[]) => void;
}

export interface ButtonWithIconProps {
    icon:  React.ReactNode;
    label: string;
    className?: string;
    onClick?: () => void | Promise<void>;
}

export interface AsideProps {
    isExpanded: boolean;
    expandSidbar: () => void;
    toggleSidebar?: any;
}

interface Application {
    id: number;
    program: string;
    university: string;
    location: string;
    dateCreated: string;
    status: string;
    logo: StaticImageData | string;
}

export interface ApplicationsTableProps {
    applications: Application[];
    itemsPerPage?: number;
}

export type UniversityDataProps = {
    university: string;
    location: string;
    program: string;
    programLevel: string;
    applicationFee: string;
    tuitionFee: string;
    intakes: string;
    gpa: string;
    ielts: string;
    duolingo: string;
    gre: string;
    pte: string;
    toefl: string;
};
  
export type TableRowProps = {
    label: string;
    key: keyof UniversityDataProps;
};

export type CourseFinderFiltersProps = {
    courseTitle: string;
    program: string[];
    intake: string[];
    fieldOfStudy: string[];
    country: string;
    tuitionFees: string;
    sortBy: string[];
};

export type StatInfoCardSmallProps = {
    imageSrc: StaticImageData | string;
    number: number;
    description: string;
    link?: string;
    increase?: number;
    decrease?: number;
};

export type StudentIndividualBannerProps = {
    date: string;
    heading: string;
    description: string;
    BannerImage: ReactNode;
}
export type StudentAgencyBannerNotificationProps = {
    CompanyLogo: StaticImageData;
    description: string;
    BannerImage: ReactNode;
}

type SocialLink = {
    platform: string;
    url: string;
};

export interface FormData {
    studentId?: string | number;
    last_name: string;
    first_name: string;
    language: string;
    email: string;
    dob: Date;
    gender: string[];
    fathers_name: string;
    mothers_name: string;
    national_id: string;
    passport_number: string;
    marital_status: string;
    spouse_name: string;
    spouse_passport_number: string;
    present_address: string;
    present_address_country: string;
    present_address_state: string;
    present_address_city: string;
    present_address_zip_code: string;
    permanent_address: string;
    permanent_address_country: string;
    permanent_address_state: string;
    permanent_address_city: string;
    permanent_address_zip_code: string;
    sponsor_name: string;
    sponsor_email: string;
    sponsor_phone: string;
    sponsor_national_id: string;
    relationship: string;
    note: string;
    phoneNumber: string;
    gurdian_phone_number: string;
    present_country: string;
    present_state: string;
    permanent_country: string;
    permanent_state: string;
    prefferedCountry: string[];
    preferred_subject: string[];
    reference: string;
    socialLink: SocialLink[];
    selectedPlatform: string;
    emergency_first_name: string;
    emergency_middle_name: string;
    emergency_last_name: string;
    emergency_phone_home: string;
    emergency_phone_mobile: string;
    emergency_relation: string;
    social_link_url: string;
    socialLinks: Array<{platform: string, url: string}>;

}

// Education Form Types
export interface EducationFormData {
    academic: Array<{
        foreignDegree: boolean;
        nameOfExam: string;
        institute: string;
        subject: string;
        board: string;
        grade: string;
        passingYear: string;
    }>;
    proficiency: Array<{
        nameOfExam: string;
        overall: number;
        R: number;
        L: number;
        W: number;
        S: number;
        examDate: Date | string;
        expiryDate: Date | string;
        note: string;
    }>;
    publications: Array<{
        subject: string;
        journal: string;
        publicationDate: Date | string;
        link: string;
    }>;
    otherActivities: Array<{
        subject: string;
        certificationLink: string;
        startDate: Date | string;
        endDate: Date | string;
    }>;
}

export type PhoneNumberInputProps = {
    className?: string;
    NumberInputClassName?: string;
    label?: string;
    value?: string;
    onChange?: (value: string) => void;
};

export type UniversityProfileCardProps = {
    className?: string;
    children: React.ReactNode;
    icon: React.ReactNode;
    heading: string;
};

export type ArrowForwardProps = {
    className?: string;
};

export interface InputFieldWithButtonProps {
    name: string;
    setValue: UseFormSetValue<any>;
    getValues: UseFormGetValues<any>;
    placeholder?: string;
}

export interface HeadingProps {
    level: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
    children: React.ReactNode;
    className?: string;
};

export type ApplicationMessageProps = {
    title: string;
    message: string;
    senderImage: React.ReactNode ;
    senderName: string;
    messagetime: string;
    replyContent?: string;
    onReplyClick?: () => void;
    onSubmit?: (response: string) => void;
}

export type CircularProgressProps = {
    value: number;
    size?: number;
    strokeWidth?: number;
    progressColor?: string;
    trackColor?: string;
    centerText?: string;
    animationDuration?: number; 
};

export interface UniversityCardProps {
    university: {
        id: number; 
        universityName: string;
        slug: string;
        universityLogo: StaticImageData;
        location: string;
        country: { 
            label: string; 
            logo: React.ElementType; 
        };
        province?: string;
        city?: string;
        rank: number;
        type: string;
        tuitionFees: string;
        // courses?: {
        //     courseTitle: string;
        //     slug: string;
        // }
    };
    isSelected: boolean;
    onSelect: (id: number) => void; 
    isChecked?: boolean;
}

export interface xIconProps {
    className?: string
}

export interface IconProps {
    className?: string
}

export interface LinkWithIconProps {
    url: string;
    className?: string;
    children:  React.ReactNode;
}

// export interface PageProps {
//     params: Promise<{ slug: string }>;
// }

export type PageProps = {
    params: {
      slug: string;
    };
};

export interface TabLayoutProps {
    tabLists: { value: string; label: string }[];
    tabContents: { value: string; content: () => React.ReactNode }[];
    children?: React.ReactNode;
}

export interface applicationSummaryBoxProps {
    daysLeft: number;
    isIncomplete?: boolean;
    complete?: boolean
}
export type Entry = {
    name: string;
    phone?: string;
    location?: string;
    startDate: string;
    program?: string;
    degree?: string;
    university?: string;
    buttonText: string;
    isInPerson?: boolean;
}

export type Section = {
    date: string;
    count: number;
    entries: Entry[];
}

export type Data = {
    sections: Section[];
}
export interface AllApplicationsTableProps {
    status: string;
}

export interface RequirementCardProps {
    title: string;
    description: string;
    status?: 'Reviewing' | 'Missing' | 'No Status' | 'Completed' | 'Rejected';
    required?: boolean;
    uploadedFiles?: Array<File | string>;
    allowUpload?: boolean;
    allowAnswer?: boolean;
    file?: string;
    RejectionNote?: string;
    ResubmissionDate?: string;
    targetedSection?: string;
}

export interface UpcomingEventProps {
    day: string;
    date: string;
    title: string;
    time: string;
}

type Status = {
    label: string;
    icon: React.ReactNode;
}
type TableRow = {
    id: string;
    name: string;
    mobile: string;
    program: string;
    intake: string;
    country: React.ReactNode;
    status: Status;
};

type TableHead = {
    label: string;
    icon?: React.ReactNode;
}

export type TableLayoutProps = {
    tableHeading: string;
    tableHead: TableHead[];
    tableData: TableRow[];
}

export type AllStudentStatusProps = {
    status: Status;
}

export interface DateRangeSelectProps {
    options: string[];
    selectedValue: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
}

export type EnrollWebinarCardProps = {
    imageSrc: StaticImageData | string;
    title: string;
    enrolled: ( StaticImageData[]);
    totalEnrollee: number;
    date: string;
    startTime: string;
    endTime: string;
};

export interface PaginationProps {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
}

export type StatCardLayoutProps = {
    children: React.ReactNode;
    className?: string;
}

export type StatInfoCardBigProps = {
    imageSrc: StaticImageData | string;
    number: string;
    description: string;
};

interface Step {
    date: string;
    title: string;
    status: string;
    update?: string;
    content?: string;
}

export interface StepperProps {
    steps: Step[];
    currentStep: number;
}

interface VerticalStep {
    date: string;
    title: string;
    update?: string;
    status: 'completed' | 'active' | 'regular' | 'empty' | string;
    content?: string;
    link?: string;
    linkText?: string;
    file?: string;
    iconUrl?: StaticImageData | string;
}

export interface VerticalStepperProps {
    steps: VerticalStep[];
}


interface AccordionItem  {
    title: string;
    details: { label: string; value: string }[];
};

export interface AccordionsProps  {
    data: AccordionItem[];
};

export interface EntryCardProps {
    entry: Entry;
}

export interface ActivitiesSectionProps {
    section: Section;
}

export interface PaymentProps {
    id: string;
    appId: number;
    university: string;
    program: string;
    country: React.ReactNode;
    eslStartDate: string;
    intake: string;
    amount: number;
    action: React.ReactNode;
};

export interface FaqItemProps {
    question?: string;
    answer?: string  
    listAnsText?: string; 
    listAns?: string[]  
    boldListAns?: { label: string; description: string }[]
    isOpen: boolean;
    onToggle: () => void;
}

export interface FaqProps {
    sectionHeading?: string;
    faqData?: {
        question?: string;
        answer?: string;
        listAnsText?: string;
        listAns?: string[]
        boldListAns?: { 
            label: string; 
            description: string 
        }[]
    }[];
    sectionDescription?: string;
    className?:string;
}

interface StatusSteps {
    icon : ElementType,
    heading: string,
    description: string,
    attributes: string[]
    achieved: boolean
}

export interface BadgeStatusProps {
   StatusProps: StatusSteps[]
}

interface VerticalTabMenuProps {
    label: string;
    value: string;
};

interface VerticalTabContentProps {
    value: string;
    children: ReactNode;
};

export interface VerticalTabLayoutProps {
    defaultValue: string;
    tabListClassName?: string;
    tabTriggerClassName?: string;
    tabContentClassName?: string;
    tabMenu: VerticalTabMenuProps[];
    tabContents: VerticalTabContentProps[];
};

export interface FilterOptionProps {
    option: string;
    onRemove?: (item: string) => void;
}

// Documents Form Types
export interface DocumentsFormData {
    academicSections: string[];
    proficiencySections: string[];
    sponsorName: string;
    takeDependents: boolean;
    dependents: Array<{
        name: string;
        passport: string;
    }>;
    children: Array<{
        name: string;
        passport: string;
    }>;
    files: {
        [key: string]: File;
    };
}

export interface DocumentsRef {
    saveForm: () => Promise<{ success: boolean; error?: string; data?: any }>;
}

// Application Form Types
export interface ApplicationFormData {
    studentId: string;
    universityId: number;
    universityCountryId: number;
    universityCountryCampus: number;
    programId: number;
    intakeId: number;
    courseId: number;
    note: string;
    status: string;
    paymentStatus: string;
    applicationType: string;
    applicationId: string;
    currentStage: string;
    overallProgress: number;
    totalAmount: number;
    deliveryMethod: string;
    deadlineDate: string;
}

export interface ApplicationRef {
    saveForm: () => Promise<{ success: boolean; error?: string; data?: any }>;
}

export interface FormSubSectionProps {
    children: React.ReactNode;
    heading: string;
}

export interface InputFieldWithCurrencyProps {
    label?: string;
    value?: string | number;
    onChangeValue?: (value: string) => void;
    currency?: string;
    onChangeCurrency?: (currency: string) => void;
    currencyOptions?: string[];
}
export interface FilterListsProps {
    lists: string[];
    onRemove?: (option: string) => void;
    onClearAll?: () => void;
}

type DocumentStatus = 'pending' | 'approved' | 'rejected' | 'uploaded';

export interface Attachment {
    filename: string;
    url: string;
    type: string;
}

export interface FileReviewProps {
    title: string;
    required?: boolean;
    attachments: Attachment[];
    status: DocumentStatus;
    date: string;
}

export interface ProgressItem {
    img: string;
    value: number;
    label?: string;
}

export interface HorizontalIconChartProps {
  title: string;
  viewAllLink?: string;
  items: ProgressItem[];
}

export interface AccordionWithButtonProps {
    label: string;
    subLabel?: string;
    children: React.ReactNode;
    labelClassName?: string;
}
interface MultiSelectWithGroup {
    label: string;
    selectItem: string[];
}
export interface MultiSelectWithGroupProps {
    label?: string;
    placeholder?: string;
    selectedLists: MultiSelectWithGroup[];
    onChange?: (selectedValues: string[]) => void;
    initialValues?: string[];
}

export interface TutionFeeFilterProps {
    label?: string;
    value?: string | number;
    onChangeValue?: (value: string) => void;
}

export interface TooltipCountryProps {
    label: string;
    logo: React.ReactNode;
}

export interface DropdownOption {
  label: string;
  value: string;
}