'use client';

import { useSelector } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(...inputs));
}

interface Feature {
  id: number;
  name: string;
}

interface Action {
  id: number;
  name: string;
  featureId: number;
  feature: Feature;
}

export const usePermissions = () => {
  const user = useSelector((state: RootState) => state.auth.user);
  
  const permissions = user?.permissions || [];

  const hasPermission = (actionName: string): boolean =>
    permissions.some((perm) => perm.actionName === actionName && perm.permission);

  const hasFeatureAccess = (featureName: string): boolean =>
    permissions.some((perm) => perm.featureName === featureName && perm.permission);

  const getActionsByFeature = (featureName: string): string[] =>
    permissions
      .filter((perm) => perm.featureName === featureName && perm.permission)
      .map((perm) => perm.actionName);

  const hasAnyPermission = (actions: string[]): boolean =>
    actions.some(hasPermission);

  const hasAllPermissions = (actions: string[]): boolean =>
    actions.every(hasPermission);

  return {
    permissions,
    hasPermission,
    hasFeatureAccess,
    getActionsByFeature,
    hasAnyPermission,
    hasAllPermissions,
  };
};

export const formatDate = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  };
  return date.toLocaleDateString('en-US', options);
};

