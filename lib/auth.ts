import NextAuth from "next-auth";
import { loginUser, verifyOTP, googleSS<PERSON>ogin } from "./api";
import Google from "next-auth/providers/google";
import Credentials from "next-auth/providers/credentials";
import { UseDispatch } from "react-redux";

export const { 
    auth, 
    signIn,
    signOut,
    handlers: { GET, POST } 
} = NextAuth({
    providers: [
        Google({
            clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
            clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET,
            authorization: {
                params: {
                    prompt: "consent",
                    access_type: "offline",
                    response_type: "code",
                },
            },
        }),
        Credentials({
            name: 'Credentials',
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.password) return null;

                try {
                    const user = await loginUser({
                        email: credentials.email as string,
                        password: credentials.password as string,
                    })

                    if(!user.success || !user.user || !user.user.email || !user.user.id) {
                        return null;
                    }

                    const roleNemes = user.user?.roles?.map((role) => role.name || '') || [];
                    const actions = user.user?.roles?.flatMap((role) => 
                        role.actions?.map((action) => ({
                            name: action.name,
                            feature: action.feature.name
                    })) || []) || [];
                    
                    return {
                        id: String(user.user?.id),
                        email: user.user?.email,
                        name: user.user?.name || user.user?.email.split('@')[0],
                        roles: roleNemes,
                        actions: actions,
                        accessToken: user.accessToken,
                        refreshToken: user.refreshToken,
                        userData: user.user
                    }
                } catch (error) {
                    console.error("Auth error:", error);
                    return null;
                }
            }
        }),
        Credentials({
            name: 'OTP Verification',
            credentials: {
                email: { label: "Email", type: "email" },
                otp: { label: "OTP", type: "text" },
                type: { label: "Type", type: "text" }, 
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.otp) {
                    return null;
                }
                
                try {
                    const result = await verifyOTP({
                        email: credentials.email as string,
                        otp: credentials.otp as string,
                        type: (credentials.type as any) || 'login',
                    });

                    if (result.success && result.user) {
                        return {
                            id: result.user.id,
                            email: result.user.email,
                            name: result.user.name,
                            accessToken: result.accessToken,
                            refreshToken: result.refreshToken,
                        };
                    }
                    return null;
                } catch (error) {
                    console.error("OTP verification error:", error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async signIn({ user, account }) {
            console.log('', 'account ------------->', user, account);
            
        if (account?.provider === 'google' && account?.id_token) {
            try {
                console.log('id token',account.id_token);
                
            const response = await googleSSOLogin({
                provider: account.provider,
                token: account.id_token,
                email: user?.email || '',
                name: user?.name || '',
            });
            

            if (!response.success) return false;

            const userData = response.user;
            const token = response.accessToken;

            // Redirect with token + user info
            const redirectUrl = new URL(`http://localhost:3002/api/auth/callback/google`);
            redirectUrl.searchParams.set("accessToken", token);
            redirectUrl.searchParams.set("user", encodeURIComponent(JSON.stringify(userData)));

            return redirectUrl.toString();
            } catch (error) {
            console.error("Google SSO error:", error);
            return false;
            }
        }

            return true;
        }
    },
    session: {
        strategy: "jwt",
    },
    pages: {
        signIn: "/login",
    },
    secret: process.env.NEXTAUTH_SECRET,
});