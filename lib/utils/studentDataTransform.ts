import { FormData } from '@/types';
import { StudentData } from '../redux/api/studentApi';

/**
 * Transforms form data from the Personal component to the API request format
 */
export const transformFormDataToStudentData = (formData: FormData): StudentData => {
  return {
    studentId: formData.studentId,
    firstName: formData.first_name || '',
    lastName: formData.last_name || '',
    nameInNative: formData.language || '',
    email: formData.email || '',
    phone: formData.phoneNumber || '',
    guardianPhone: formData.gurdian_phone_number || '',
    dateOfBirth: formData.dob ? formatDate(formData.dob) : '',
    gender: Array.isArray(formData.gender) ? formData.gender[0] || '' : formData.gender || '',
    fatherName: formData.fathers_name || '',
    motherName: formData.mothers_name || '',
    nid: formData.national_id || '',
    passport: formData.passport_number || '',
    presentAddress: {
      address: (formData as any).presentAddress?.address || formData.present_address || '',
      country: (formData as any).presentAddress?.country || formData.present_country || '',
      state: (formData as any).presentAddress?.state || formData.present_state || '',
      city: (formData as any).presentAddress?.city || formData.present_address_city || '',
      postalCode: (formData as any).presentAddress?.zip || formData.present_address_zip_code || '',
    },
    permanentAddress: {
      address: (formData as any).permanentAddress?.address || formData.permanent_address || '',
      country: (formData as any).permanentAddress?.country || formData.permanent_country || '',
      state: (formData as any).permanentAddress?.state || formData.permanent_state || '',
      city: (formData as any).permanentAddress?.city || formData.permanent_address_city || '',
      postalCode: (formData as any).permanentAddress?.zip || formData.permanent_address_zip_code || '',
    },
    maritalStatus: {
      status: formData.marital_status || 'single',
      spouseName: formData.spouse_name || 'none',
      spousePhone: formData.phoneNumber || 'none', // Using the phone number field for spouse
      spousePassport: formData.spouse_passport_number || 'none',
    },
    sponsor: {
      name: formData.sponsor_name || '',
      relation: formData.relationship || '',
      phone: formData.sponsor_phone || '',
      email: formData.sponsor_email || '',
    },
    emergencyContact: {
      lastName: formData.emergency_last_name || '',
      middleName: formData.emergency_middle_name || '',
      firstName: formData.emergency_first_name || '',
      phoneHome: formData.emergency_phone_home || '',
      phoneMobile: formData.emergency_phone_mobile || '',
      relation: formData.emergency_relation || 'father',
    },
    preferredSubject: formData.preferred_subject || [],
    preferredCountry: formData.prefferedCountry || [],
    socialLinks: (formData as any).socialLinks || [],
    reference: Array.isArray(formData.reference) ? formData.reference.join(', ') : formData.reference || '',
    note: formData.note || '',
  };
};

/**
 * Transforms student data from API response to form data format
 */
export const transformStudentDataToFormData = (studentData: StudentData): Partial<FormData> => {
  return {
    studentId: studentData.studentId,
    first_name: studentData.firstName,
    last_name: studentData.lastName,
    language: studentData.nameInNative,
    email: studentData.email,
    phoneNumber: studentData.phone,
    gurdian_phone_number: studentData.guardianPhone,
    dob: studentData.dateOfBirth ? new Date(studentData.dateOfBirth) : undefined,
    gender: [studentData.gender],
    fathers_name: studentData.fatherName,
    mothers_name: studentData.motherName,
    national_id: studentData.nid,
    passport_number: studentData.passport,
    present_address: studentData.presentAddress.address,
    present_country: studentData.presentAddress.country,
    present_state: studentData.presentAddress.state,
    present_address_city: studentData.presentAddress.city,
    present_address_zip_code: studentData.presentAddress.postalCode,
    permanent_address: studentData.permanentAddress.address,
    permanent_country: studentData.permanentAddress.country,
    permanent_state: studentData.permanentAddress.state,
    permanent_address_city: studentData.permanentAddress.city,
    permanent_address_zip_code: studentData.permanentAddress.postalCode,
    marital_status: studentData.maritalStatus.status,
    spouse_name: studentData.maritalStatus.spouseName,
    spouse_passport_number: studentData.maritalStatus.spousePassport,
    sponsor_name: studentData.sponsor.name,
    sponsor_email: studentData.sponsor.email,
    sponsor_phone: studentData.sponsor.phone,
    relationship: studentData.sponsor.relation,
    preferred_subject: studentData.preferredSubject,
    prefferedCountry: studentData.preferredCountry,
    socialLink: studentData.socialLinks,
    reference: studentData.reference || '',
    note: studentData.note,
    emergency_first_name: studentData.emergencyContact.firstName,
    emergency_middle_name: studentData.emergencyContact.middleName,
    emergency_last_name: studentData.emergencyContact.lastName,
    emergency_phone_home: studentData.emergencyContact.phoneHome,
    emergency_phone_mobile: studentData.emergencyContact.phoneMobile,
    emergency_relation: studentData.emergencyContact.relation,
  };
};

/**
 * Formats a Date object to a string in the format expected by the API
 */
const formatDate = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  };
  return date.toLocaleDateString('en-GB', options);
};

/**
 * Validates required fields for student data
 */
export const validateStudentData = (data: StudentData): string[] => {
  const errors: string[] = [];
  console.log('data', data);
  

  if (!data.firstName.trim()) errors.push('First name is required');
  if (!data.lastName.trim()) errors.push('Last name is required');
  if (!data.email.trim()) errors.push('Email is required');
  if (!data.phone.trim()) errors.push('Phone number is required');
  if (!data.dateOfBirth.trim()) errors.push('Date of birth is required');
  if (!data.gender.trim()) errors.push('Gender is required');

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (data.email && !emailRegex.test(data.email)) {
    errors.push('Please enter a valid email address');
  }

  return errors;
};
