import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface FlattenedPermission {
  featureId: number;
  featureName: string;
  actionId: number;
  actionName: string;
  permission: boolean;
};

interface Department {
  id: number;
  name: string;
};

interface Role {
  id: number;
  name: string;
};

interface User {
  id: number;
  email: string;
  name: string;
  image: string;
  roles: Role;
  permissions: FlattenedPermission[];
  departments: Department[];
};

interface AuthState {
  user: User | null;
  accessToken: string | null;
};

const initialState: AuthState = {
  user: null,
  accessToken: null,
};

const transformPermissions = (userFromApi: any): FlattenedPermission[] => {
  const permissions: FlattenedPermission[] = [];

  userFromApi?.roles?.modules?.forEach((module: any) => {
    module.features?.forEach((feature: any) => {
      permissions.push({
        featureId: feature.id,
        featureName: module.name,
        actionId: feature.id,
        actionName: feature.name,
        permission: feature.permission,
      });
    });
  });

  return permissions;
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (
      state,
      action: PayloadAction<{ user: any; accessToken: string }>
    ) => {
      const { user, accessToken } = action.payload;

      const transformedUser: User = {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user?.image || null,
        roles: {
          id: user.roles.id,
          name: user.roles.name,
        },
        permissions: transformPermissions(user),
        departments: user.departments || [],

      };

      state.user = transformedUser;
      state.accessToken = accessToken;
    },
    logout: (state) => {
      state.user = null;
      state.accessToken = null;
    },
  },
});

export const { setUser, logout } = authSlice.actions;
export default authSlice.reducer;
