import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UniversityState {
    universityId: string | null;
}

const initialState: UniversityState = {
    universityId: null,
};

const universitySlice = createSlice({
    name: 'university',
    initialState,
    reducers: {
        setUniversityId(state, action: PayloadAction<string>) {
            state.universityId = action.payload;
        },
        clearUniversityId(state) {
            state.universityId = null;
        },
    },
});

export const { setUniversityId, clearUniversityId } = universitySlice.actions;
export default universitySlice.reducer;