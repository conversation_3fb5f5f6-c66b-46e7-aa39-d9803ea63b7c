import thunk from 'redux-thunk';
import { authApi } from './api/authApi';
import { rolesApi } from './api/rolesApi';
import { studentApi } from './api/studentApi';
import authReducer from './slices/authSlice';
import universityReducer from './slices/universitySlice';
import studentReducer from './slices/studentSlice';
import storage from 'redux-persist/lib/storage';
import { departmentApi } from './api/departmentApi';
import { addUniversityApi } from './api/addUniversityApi';
import { persistReducer, persistStore } from 'redux-persist';
import { configureStore, combineReducers } from '@reduxjs/toolkit';

const rootReducer = combineReducers({
  auth: authReducer,
  university: universityReducer,
  student: studentReducer,
  [authApi.reducerPath]: authApi.reducer,
  [departmentApi.reducerPath]: departmentApi.reducer,
  [rolesApi.reducerPath]: rolesApi.reducer,
  [addUniversityApi.reducerPath]: addUniversityApi.reducer,
  [studentApi.reducerPath]: studentApi.reducer,
});

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'university', 'student'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    })
    .concat(authApi.middleware)
    .concat(departmentApi.middleware)
    .concat(rolesApi.middleware)
    .concat(addUniversityApi.middleware)
    .concat(studentApi.middleware),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
