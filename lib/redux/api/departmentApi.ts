// lib/redux/api/departmentApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';

// const token = Cookies.get('accessToken');
// console.log(token);

interface Department {
    id: number;
    name: string;
    parentId?: number;
    children?: Department[];
}


const BASE_URL = process.env.NEXT_PUBLIC_AUTH_BASE_URL;

export const departmentApi = createApi({
    reducerPath: 'departmentApi',
    baseQuery: fetchBaseQuery({ baseUrl: `${BASE_URL}/auth`,
        prepareHeaders: (headers) => {
            const token = Cookies.get('accessToken');
            if (token) {
                headers.set('Authorization', `Bearer ${token}`);
            }
            return headers;
        },
    }),
    tagTypes: ['Departments'], 
    endpoints: (builder) => ({
        getDepartments: builder.query<{ success: boolean; message: string; departments: Department[] }, void>({
            query: () => '/departments', // Adjust the path as needed
            providesTags: ['Departments'],
        }),
        postDepartments: builder.mutation<any, { name: string; parent: string | null }[]>({
            query: (departments) => ({
                url: '/departments',
                method: 'POST',
                body: departments,
                headers: {
                    'Content-Type': 'application/json',
                },
            }),
            invalidatesTags: ['Departments'],
        }),
    }),
});

export const { useGetDepartmentsQuery, usePostDepartmentsMutation } = departmentApi;