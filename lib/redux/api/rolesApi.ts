import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';

const BASE_URL = process.env.NEXT_PUBLIC_AUTH_BASE_URL;
export interface SubFeature {
    id: number;
    subFeature: string;
    permissions: boolean;
}

export interface Feature {
    id: number;
    feature: string;
    permissions: boolean;
    subFeatures: SubFeature[];
}

export interface Module {
    id: number;
    module: string;
    features: Feature[];
}

export interface Department {
    id: number;
    name: string;
    users: {
        id: number;
        name: string;
        email: string;
    }[];
}

export interface Role {
    id: number;
    role: string;
    modules: Module[];
    department: Department[];
}

export interface RolesResponse {
    success: boolean;
    message: string;
    roles: Role[];
}

export const rolesApi = createApi({
    reducerPath: 'rolesApi',
    baseQuery: fetchBaseQuery({ 
        baseUrl: `${BASE_URL}/auth`,
        prepareHeaders: (headers) => {
            const token = Cookies.get('accessToken');
            if (token) {
                headers.set('Authorization', `Bearer ${token}`);
            }
            return headers;
        },
    }),
    tagTypes: ['Roles'],
    endpoints: (builder) => ({
        getRoles: builder.query<RolesResponse, void>({
            query: () => '/roles',
            providesTags: ['Roles'],
        }),
        createAndUpdateRole: builder.mutation<any, { id: number, name: string }>({
            query: (body) => ({
                url: '/role',
                method: 'POST',
                body,
            }),
            invalidatesTags: ['Roles'],
        }),
        deleteRole: builder.mutation<any, number>({
            query: (id) => ({
                url: `/role/${id}`,
                method: 'DELETE',
            }),
            invalidatesTags: ['Roles'],
        }),
        updateRoleName: builder.mutation<any, { id: number, newName: string }>({
            query: ({ id, newName }) => ({
                url: `/role/${id}/${newName}`,
                method: 'PUT',
            }),
            invalidatesTags: ['Roles'],
        }),
    }),
});

export const { 
    useGetRolesQuery, 
    useCreateAndUpdateRoleMutation, 
    useDeleteRoleMutation, 
    useUpdateRoleNameMutation,
} = rolesApi; 