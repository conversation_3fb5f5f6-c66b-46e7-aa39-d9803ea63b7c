import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { logout } from '@/lib/redux/slices/authSlice';
import Cookies from 'js-cookie';

const AUTH_BASE_URL = process.env.NEXT_PUBLIC_AUTH_BASE_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: `${AUTH_BASE_URL}/auth`,
  credentials: 'include',
});

export const baseQueryWithReauth: BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> = async (
  args,
  api,
  extraOptions
) => {
  let result = await baseQuery(args, api, extraOptions);

  if (result.error?.status === 401) {
    const refreshResult = await baseQuery(
      { url: '/auth/refresh', method: 'POST' },
      api,
      extraOptions
    );

    if (refreshResult.data) {
      result = await baseQuery(args, api, extraOptions);
    } else {
      Cookies.remove('accessToken');
      Cookies.remove('refreshToken');
      api.dispatch(logout());
    }
  }

  return result;
};
