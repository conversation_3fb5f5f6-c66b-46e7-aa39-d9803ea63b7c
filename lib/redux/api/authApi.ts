import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  LoginInput,
  RegisterInput,
  SendOTPInput,
  VerifyOTPInput,
  SSOLoginProps,
  OTPResponse,
  LoginResponse,
  ForgetPasswordInput,
  ForgetPasswordResponse,
  ResetPasswordInput,
  ResetPasswordResponse,
} from '@/types';
import { baseQueryWithReauth } from './baseQueryWithReauth';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    login: builder.mutation<LoginResponse, LoginInput>({
      query: (credentials) => ({
        url: '/login',
        method: 'POST',
        body: credentials,
      }),
    }),
    register: builder.mutation<any, RegisterInput>({
      query: (data) => ({
        url: '/register',
        method: 'POST',
        body: data,
      }),
    }),
    sendOTP: builder.mutation<OTPResponse, SendOTPInput>({
      query: (data) => ({
        url: '/generate-otp',
        method: 'POST',
        body: data,
      }),
    }),
    verifyOTP: builder.mutation<OTPResponse, VerifyOTPInput>({
      query: (data) => ({
        url: '/verify-otp',
        method: 'POST',
        body: data,
      }),
    }),
    googleSSO: builder.mutation<any, SSOLoginProps>({
      query: (data) => ({
        url: '/sso',
        method: 'POST',
        body: data,
      }),
    }),
    logout: builder.mutation<boolean, string>({
      query: (token) => ({
        url: '/logout',
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }),
    }),
    forgetPassword: builder.mutation<ForgetPasswordResponse, ForgetPasswordInput>({
      query: (data) => ({
        url: '/forgot-password',
        method: 'POST',
        body: data,
      }),
    }),
    resetPassword: builder.mutation<ResetPasswordResponse, ResetPasswordInput>({
      query: (data) => ({
        url: '/reset-password',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useSendOTPMutation,
  useVerifyOTPMutation,
  useGoogleSSOMutation,
  useLogoutMutation,
  useForgetPasswordMutation,
  useResetPasswordMutation
} = authApi;
