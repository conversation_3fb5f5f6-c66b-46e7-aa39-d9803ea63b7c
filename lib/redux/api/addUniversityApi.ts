import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';

const BASE_URL = process.env.NEXT_PUBLIC_UNIVERSITY_BASE_URL;

export const addUniversityApi = createApi({
    reducerPath: 'addUniversityApi',
    baseQuery: fetchBaseQuery({ baseUrl: `${BASE_URL}`,
        prepareHeaders: (headers) => {
            const token = Cookies.get('accessToken');
            if (token) {
                headers.set('Authorization', `Bearer ${token}`);
            }
            return headers;
        },
    }),
    tagTypes: ['universities', 'intakes'],
    endpoints: (builder) => ({
        getUniversities: builder.query<any, void>({
            query: () => '/universities',
            providesTags: ['universities'],
        }),
        getUniversityById: builder.query<any, string | number>({
            query: (id) => `/universities/${id}`,
            transformResponse: (response: any) => response.data,
            providesTags: ['universities'],
        }),
        addUniversity: builder.mutation<any, any>({
            query: (body) => ({
                url: '/universities',
                method: 'POST',
                body,
            }),
            invalidatesTags: ['universities'],
        }),
        addIntakes: builder.mutation<any, any>({
            query: (body) => ({
                url: '/intakes',
                method: 'POST',
                body,
            }),
            invalidatesTags: ['intakes'],
        }),
        addContactDetails: builder.mutation<any, any>({
            query: (body) => ({
                url: '/university-contact',
                method: 'POST',
                body,
            }),
        }),
        addBankDetails: builder.mutation<any, any>({
            query: (body) => ({
                url: '/university-bank-details',
                method: 'POST',
                body,
            }),
        }),
        addEligibleCountries: builder.mutation<any, any>({
            query: (body) => ({
                url: '/countries',
                method: 'POST',
                body,
            }),
        }),
        addFieldOfStudy: builder.mutation<any, any>({
            query: (body) => ({
                url: '/fields-of-study',
                method: 'POST',
                body,
            }),
        }),
        addCommissions: builder.mutation<any, any>({
            query: (body) => ({
                url: '/commissions',
                method: 'POST',
                body,
            }),
        })
    }),
});

export const { 
    useAddUniversityMutation,
    useGetUniversitiesQuery,
    useGetUniversityByIdQuery,
    useAddIntakesMutation,
    useAddContactDetailsMutation,
    useAddBankDetailsMutation,
    useAddEligibleCountriesMutation,
    useAddFieldOfStudyMutation,
    useAddCommissionsMutation
} = addUniversityApi;
